{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "sf_v2",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "sf_v2 (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "sf_v2 (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "k_chart_plus-1.0.2",
            "cwd": "packages/k_chart_plus-1.0.2",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "k_chart_plus-1.0.2 (profile mode)",
            "cwd": "packages/k_chart_plus-1.0.2",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "k_chart_plus-1.0.2 (release mode)",
            "cwd": "packages/k_chart_plus-1.0.2",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "tencent_cloud_chat_push-8.3.6498+2",
            "cwd": "packages/tencent/tencent_cloud_chat_push-8.3.6498+2",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "tencent_cloud_chat_uikit-3.1.0+1",
            "cwd": "packages/tencent/tencent_cloud_chat_uikit-3.1.0+1",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "tencent_super_tooltip-0.0.1",
            "cwd": "packages/tencent/tencent_super_tooltip-0.0.1",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "tim_ui_kit_sticker_plugin-3.2.0",
            "cwd": "packages/tencent/tim_ui_kit_sticker_plugin-3.2.0",
            "request": "launch",
            "type": "dart"
        }
    ]
}