import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/services/app_link/app_links.dart';
import 'core/config/app_config.dart';
import 'core/dependency_injection/injectable.dart';
import 'core/observers/chat_button_observer.dart';
import 'core/routes/navigator.dart';
import 'core/routes/route_generator.dart';
import 'core/routes/routes.dart';
import 'core/shared/logic/theme/theme_cubit.dart';
import 'core/theme/app_theme.dart';

class MyApps extends StatefulWidget {
  const MyApps({super.key});

  @override
  State<MyApps> createState() => _MyAppsState();
}

class _MyAppsState extends State<MyApps> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Initialize app links
      AppLinksManager().initAppLinks(
        context: context,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    const designSize = Size(430, 932);
    return ScreenUtilInit(
      designSize: designSize,
      fontSizeResolver: (fontSize, instance) {
        final display = View.of(context).display;
        final screenSize = display.size / display.devicePixelRatio;
        final scaleWidth = screenSize.width / designSize.width;
        return fontSize * scaleWidth;
      },
      builder: (context, child) => GestureDetector(
        onTap: () async => FocusManager.instance.primaryFocus?.unfocus(),
        child: BlocBuilder<ThemeCubit, ThemeState>(
          builder: (context, state) {
            final config = getIt<AppConfig>();

            return MaterialApp(
              localizationsDelegates: context.localizationDelegates,
              supportedLocales: context.supportedLocales,
              locale: context.locale,
              builder: (context, child) => MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler: const TextScaler.linear(1),
                ),
                child: child!,
              ),
              title: config.appName,
              debugShowCheckedModeBanner: false,
              theme: AppTheme.lightTheme,
              darkTheme: AppTheme.darkTheme,
              themeMode: state.themeMode,
              // themeMode: ThemeMode.system,
              onGenerateRoute: (settings) =>
                  RouteGenerator.generateRoute(settings),
              initialRoute: routeRoot,
              navigatorKey: navigatorKey,
              navigatorObservers: [routeObserver, ChatButtonObserver()],
            );
          },
        ),
      ),
    );
  }
}
