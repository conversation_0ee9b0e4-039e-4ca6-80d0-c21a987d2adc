import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app_v2/core/dependency_injection/injectable.dart';
import 'package:sf_app_v2/core/shared/logic/app_data/app_data_cubit.dart';
import 'package:sf_app_v2/core/shared/logic/theme/theme_cubit.dart';
import 'package:sf_app_v2/core/shared/logic/wallet_slider/wallet_slider_cubit.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/account_info/account_info_cubit.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/google_authentication/google_authentication_cubit.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/phone/phone_cubit.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/wallet/wallet_cubit.dart';
import 'package:sf_app_v2/features/auth/forgot/logic/forgot/forgot_cubit.dart';
import 'package:sf_app_v2/features/auth/sign_in/logic/sign_in/sign_in_cubit.dart';
import 'package:sf_app_v2/features/auth/sign_up/logic/sign_up/sign_up_cubit.dart';
import 'package:sf_app_v2/features/auth/verify/logic/verify/verify_cubit.dart';
// import 'package:sf_app_v2/features/chat_v2/cubit/chat/chat_cubit.dart';
import 'package:sf_app_v2/features/community/logic/community/community_cubit.dart';
import 'package:sf_app_v2/features/home/<USER>/home/<USER>';
import 'package:sf_app_v2/features/market_v2/logic/market/market_cubit.dart';
import 'package:sf_app_v2/features/news/logic/news/news_cubit.dart';
import 'package:sf_app_v2/features/notification/logic/notification/notification_cubit.dart';
import 'package:sf_app_v2/features/profile/logic/profile/profile_cubit.dart';
import 'package:sf_app_v2/features/smart_investment/logic/smart_investment/smart_investment_cubit.dart';
import 'package:sf_app_v2/features/support/logic/support/support_cubit.dart';
import 'package:sf_app_v2/features/transfer/logic/transfer/transfer_cubit.dart';
import 'package:sf_app_v2/features/wallet/deposit/logic/deposit/deposit_cubit.dart';
import 'package:sf_app_v2/features/wallet/records/logic/records/records_cubit.dart';
import 'package:sf_app_v2/features/wallet/withdraw/logic/withdraw/withdraw_cubit.dart';

import '../../features/app_update/logic/app_update/app_update_cubit.dart';
import '../../features/finance/logic/finance/finance_cubit.dart';
import '../shared/logic/chat_button/chat_button_cubit.dart';
import '../shared/logic/country_code/country_code_cubit.dart';

/// A utility class that provides all the BLoC providers needed for the application
class AppProviders {
  /// Returns a list of [BlocProvider]s that wrap the entire application
  /// 
  /// This method creates and provides instances of all the Cubits used throughout the app.
  /// Most Cubits are retrieved from the dependency injection container using [getIt].
  /// 
  static getProviders() => [
        BlocProvider(create: (context) => getIt<SignUpCubit>()),
        BlocProvider(create: (context) => getIt<SignInCubit>()),
        BlocProvider(create: (context) => getIt<VerifyCubit>()),
        BlocProvider(create: (context) => AppDataCubit()),
        BlocProvider(create: (context) => getIt<HomeCubit>()),
        BlocProvider(create: (context) => getIt<MarketCubit>()),
        BlocProvider(create: (context) => getIt<ProfileCubit>()),
        BlocProvider(create: (context) => getIt<CommunityCubit>()),
        BlocProvider(create: (context) => WalletSliderCubit()),
        BlocProvider(create: (context) => getIt<DepositCubit>()),
        BlocProvider(create: (context) => getIt<WithdrawCubit>()),
        BlocProvider(create: (context) => getIt<SmartInvestmentCubit>()),
        BlocProvider(create: (context) => ThemeCubit()),
        BlocProvider(create: (context) => getIt<AccountInfoCubit>()),
        BlocProvider(create: (context) => getIt<GoogleAuthenticationCubit>()),
        BlocProvider(create: (context) => getIt<PhoneCubit>()),
        BlocProvider(create: (context) => getIt<WalletCubit>()),
        BlocProvider(create: (context) => getIt<RecordsCubit>()),
        BlocProvider(create: (context) => getIt<TransferCubit>()),
        BlocProvider(create: (context) => getIt<NewsCubit>()),
        BlocProvider(create: (context) => getIt<NotificationCubit>()),
        BlocProvider(create: (context) => ForgotCubit()),
        // BlocProvider(create: (context) => ChatCubit()),
        BlocProvider(create: (context) => getIt<SupportCubit>()),
        BlocProvider(create: (context) => getIt<CountryCodeCubit>()),
        BlocProvider(create: (context) => getIt<FinanceCubit>()),
        BlocProvider(create: (context) => getIt<AppUpdateCubit>()),
        BlocProvider(create: (context) => getIt<ChatButtonCubit>()),
      ];
}
