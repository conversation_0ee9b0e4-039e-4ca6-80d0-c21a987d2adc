import 'dart:developer' as dev;
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

class NetworkLogger {
  static const bool _enableLogs = kDebugMode;
  static const String _tag = 'Network';
  static const int _boxWidth = 100;

  // ANSI color codes
  static const String _reset = '\x1B[0m';
  static const String _blue = '\x1B[34m';
  static const String _green = '\x1B[32m';
  static const String _red = '\x1B[31m';

  static void _log(String message, {String? name, String color = ''}) {
    if (_enableLogs) {
      dev.log('$color$message$_reset', name: name ?? _tag);
    }
  }

  static String _formatJson(dynamic data) {
    try {
      if (data is String) {
        final parsed = json.decode(data);
        return const JsonEncoder.withIndent('  ').convert(parsed);
      } else {
        return const JsonEncoder.withIndent('  ').convert(data);
      }
    } catch (e) {
      return data.toString();
    }
  }

  static List<String> _wrapLine(String line, int width) {
    List<String> lines = [];
    while (line.length > width) {
      lines.add(line.substring(0, width));
      line = line.substring(width);
    }
    lines.add(line);
    return lines;
  }

  static void _printBoxed(String title, Map<String, dynamic> content, {String color = ''}) {
    final titleText = ' $title ';
    const side = '│';
    final top = '┌${'─' * (_boxWidth - 2)}┐';
    final bottom = '└${'─' * (_boxWidth - 2)}┘';
    final titleLine = '├${'─' * (((_boxWidth - 2 - titleText.length) ~/ 2))}$titleText${'─' * ((_boxWidth - 2 - titleText.length + 1) ~/ 2)}┤';

    _log(top, color: color);
    _log(titleLine, color: color);
    content.forEach((key, value) {
      if (value != null) {
        final formattedValue = value is Map || value is List 
            ? _formatJson(value)
            : value.toString();
        final lines = formattedValue.split('\n');
        for (var i = 0; i < lines.length; i++) {
          final prefix = i == 0 ? '$side $key: ' : '$side      ';
          final availableWidth = _boxWidth - prefix.length - 2; // 2 for side borders
          final wrappedLines = _wrapLine(lines[i], availableWidth);
          for (var j = 0; j < wrappedLines.length; j++) {
            final line = wrappedLines[j];
            final pad = ' ' * (availableWidth - line.length);
            _log('$prefix$line$pad$side', color: color);
          }
        }
      }
    });
    _log(bottom, color: color);
  }

  static bool _shouldSkip(RequestOptions options) {
    return options.path.contains('/market');
  }

  static void logRequest(RequestOptions options) {
    if (_shouldSkip(options)) return;
    final requestData = {
      'Method': options.method,
      'URL': '${options.baseUrl}${options.path}',
      if (options.queryParameters.isNotEmpty) 'Query': options.queryParameters,
      if (options.data != null) 'Body': options.data,
    };
    _printBoxed('REQUEST', requestData, color: _green);
  }

  static Map<String, dynamic> filterHeaders(Map<String, dynamic> headers) {
    final importantHeaders = [
      'Authorization',
      'Content-Type',
      'Accept',
      'Accept-Language',
    ];
    return Map.fromEntries(
      headers.entries.where((entry) => 
        importantHeaders.contains(entry.key) || 
        entry.key.toLowerCase().contains('token')
      )
    );
  }

  static void logResponse(Response response) {
    if (_shouldSkip(response.requestOptions)) return;
    final responseData = {
      'Status': '${response.statusCode}',
      'Method': response.requestOptions.method,
      'URL': '${response.requestOptions.baseUrl}${response.requestOptions.path}',
      'Endpoint': response.requestOptions.path,
      // 'Headers': _filterHeaders(response.headers.map),
      'data': response.requestOptions.data,
      'Body': response.data,
    };
    _printBoxed('RESPONSE', responseData, color: _blue);
  }

  static void logError(DioException error) {
    if (_shouldSkip(error.requestOptions)) return;
    final errorData = {
      'Type': error.type.toString(),
      'Method': error.requestOptions.method,
      'URL': error.requestOptions.path,
      'Status': error.response?.statusCode?.toString(),
      'Message': error.message,
      if (error.response?.data != null) 'Response': error.response?.data,
    };
    _printBoxed('ERROR', errorData, color: _red);
  }
} 