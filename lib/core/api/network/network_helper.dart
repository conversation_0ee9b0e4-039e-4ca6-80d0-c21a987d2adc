import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/common_function.dart';
import 'package:sf_app_v2/core/constants/assets.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/custom_alert_dialog.dart';
import 'dart:async';

class NetworkHelper {
  // Track currently displayed messages
  static final Set<String> _activeMessages = {};

  static Future<void> handleMessage(
    String? message,
    BuildContext context, {
    bool useParentContext = false,
    String? actionButtonText,
    bool? hideHeader,
    String? headerImage,
    VoidCallback? onTap,
    String title = 'Error',
    String? buttonText,
    HandleTypes type = HandleTypes.snackbar,
    IconData? icon,
    Color? color,
    Color backgroundColor = Colors.black,
    bool isWarning = false,
    bool isInfinite = false,
    double bottomPadding = 50,
    SnackBarType snackBarType = SnackBarType.validation,
  }) async {
    switch (type) {
      case HandleTypes.customDialog:
        // Create a unique key for this message
        final messageKey = "$message-$snackBarType";
        
        // If this message is already being shown, don't show it again
        if (_activeMessages.contains(messageKey)) {
          return;
        }
        
        // Mark this message as active
        _activeMessages.add(messageKey);
        
        try {
          await CommonFunctions.showDialogPopUp(
            context,
            CustomAlertDialog(
              message: message ?? StringConstants.errorMsg.tr(),
              actionButtonText: actionButtonText ?? StringConstants.ok.tr(),
              buttonBackGroundColor: myColorScheme(context).primaryColor,
              onActionButtonPressed: () {
                // Remove from active messages when dialog is dismissed
                _activeMessages.remove(messageKey);
                if (onTap != null) {
                  onTap();
                } else {
                  Navigator.pop(context);
                }
              },
              headerImage: (hideHeader ?? false)
                  ? null
                  : (headerImage ??
                      ((snackBarType == SnackBarType.error)
                          ? Assets.alertError
                          : Assets.alertSuccess)),
              isLoading: false,
              messageTextStyle: FontPalette.semiBold20
                  .copyWith(color: myColorScheme(context).titleColor),
            ),
            barrierDismissible: snackBarType == SnackBarType.error ? true : false,
          );
          // If dialog is dismissed by tapping outside (when barrierDismissible is true)
          _activeMessages.remove(messageKey);
        } catch (e) {
          // Ensure we clean up if there's an error
          _activeMessages.remove(messageKey);
        }
        break;
      case HandleTypes.snackbar:
        showSnackBar(
          message ?? StringConstants.errorMsg.tr(),
          context,
          backgroundColor: backgroundColor,
          bottomPadding: bottomPadding,
          snackBarType: snackBarType,
          isWarning: isWarning,
          isInfinite: isInfinite,
          color: color,
          icon: icon,
        );
        break;
      default:
    }
  }
}

void showSnackBar(
  String message,
  BuildContext context, {
  IconData? icon,
  Color? color,
  Color backgroundColor = Colors.black,
  bool isWarning = false,
  bool isInfinite = false,
  double bottomPadding = 50,
  SnackBarType snackBarType = SnackBarType.validation,
}) {
  ScaffoldMessenger.of(context)
    ..hideCurrentSnackBar()
    ..showSnackBar(
      SnackBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        content: Container(
          constraints: BoxConstraints(minHeight: 40.h, maxHeight: 50.h),
          margin: EdgeInsets.only(bottom: bottomPadding - 40),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
          decoration: BoxDecoration(
            color: getSnackBarColor(snackBarType, context),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Center(
                    child: Text(
                      message,
                      maxLines: 10,
                      style: TextStyle(
                        fontSize: 15.sp,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                if (snackBarType == SnackBarType.validation)
                  isWarning
                      ? const SizedBox.shrink()
                      : Icon(
                          icon ?? Icons.error,
                          size: 20,
                          color: Colors.red,
                        ),
                // else
                //   getAnimatedIcon(snackBarType)
              ],
            ),
          ),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 20,
        ),
        duration: isInfinite
            ? const Duration(days: 1)
            : Duration(seconds: message.length > 40 ? 5 : 2),
      ),
    );
}
// }

Color getSnackBarColor(SnackBarType snackBarType, BuildContext context) {
  if (snackBarType == SnackBarType.success) {
    return myColorScheme(context).tagGreen ?? ColorPalette.tagGreen;
  } else if (snackBarType == SnackBarType.error) {
    return myColorScheme(context).tagRed ?? ColorPalette.tagRed;
  } else if (snackBarType == SnackBarType.info) {
    return myColorScheme(context).tagBlue ?? ColorPalette.tagBlue;
  } else if (snackBarType == SnackBarType.warning) {
    return myColorScheme(context).tagBlue ?? ColorPalette.tagBlue;
  }
  return Colors.black;
}
