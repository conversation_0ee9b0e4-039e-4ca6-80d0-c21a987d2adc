import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:sf_app_v2/core/api/network/models/user.dart';
import 'package:sf_app_v2/core/constants/keys.dart';
import 'package:sf_app_v2/core/utils/shared_preference_helper.dart';

part 'app_data_state.dart';

class AppDataCubit extends Cubit<AppDataState> {
  AppDataCubit() : super(const AppDataState());

  void getUserData() {
    final userData =
        SharedPreferenceHelper().readData(SharedPreferencesKeys.user);
    if (userData != null) {
      User user = userFromJson(userData);
      emit(state.copyWith(userData: user));
    } else {
      emit(state.copyWith(userData: null));
    }
  }
}
