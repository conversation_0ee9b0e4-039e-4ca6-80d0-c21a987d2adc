part of 'app_data_cubit.dart';

class AppDataState extends Equatable {
  final bool enableAppUpdateTile;
  final User? userData;

  const AppDataState({
    this.enableAppUpdateTile = false,
    this.userData,
  });

  @override
  List<Object?> get props => [enableAppUpdateTile, userData];
  AppDataState copyWith({
    bool? enableAppUpdateTile,
    User? userData,
  }) {
    return AppDataState(
      enableAppUpdateTile: enableAppUpdateTile ?? this.enableAppUpdateTile,
      userData: userData ?? this.userData,
    );
  }
}
