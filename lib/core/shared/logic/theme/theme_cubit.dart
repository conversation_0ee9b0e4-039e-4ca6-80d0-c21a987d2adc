import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

part 'theme_state.dart';

class ThemeCubit extends HydratedCubit<ThemeState> {
  ThemeCubit() : super(const ThemeState(ThemeMode.system));

  void toggleTheme(bool isDark) {
    final newThemeMode = isDark ? ThemeMode.dark : ThemeMode.light;
    emit(state.copyWith(themeMode: newThemeMode));
  }

  void loadTheme(bool isDark) {
    final newThemeMode = isDark ? ThemeMode.dark : ThemeMode.system;
    emit(state.copyWith(themeMode: newThemeMode));
  }

  @override
  ThemeState fromJson(Map<String, dynamic> json) {
    final themeMode = ThemeMode.values[json['themeMode']];
    return ThemeState(themeMode);
  }

  @override
  Map<String, dynamic> toJson(ThemeState state) {
    return {'themeMode': state.themeMode.index};
  }
}
