import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

part 'chat_button_state.dart';

@singleton
class ChatButtonCubit extends Cubit<ChatButtonState> {
  ChatButtonCubit() : super(const ChatButtonState());

  void toggleChatButton({bool? showChatButton}) => emit(
        state.copyWith(
          showChatButton: showChatButton,
        ),
      );
}
