import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

import '../../../constants/enums.dart';
import '../../../models/country_code/country_code.dart';
import '../../../models/phone_country/phone_country.dart';
import '../../../services/country_code/country_code_repository.dart';
import '../../../services/phone_country/phone_country_service.dart';

part 'country_code_state.dart';

@injectable
class CountryCodeCubit extends Cubit<CountryCodeState> {
  final CountryCodeRepository _countryCodeRepository;
  final PhoneCountryService _phoneCountryService;

  CountryCodeCubit(this._countryCodeRepository, this._phoneCountryService)
      : super(const CountryCodeState());

  Future<void> getCountryCode() async {
    emit(state.copyWith(countryCodeStatus: DataStatus.loading));
    try {
      final result = await _countryCodeRepository.getCountryCode();
      if (result.data != null) {
        emit(state.copyWith(
            countryCode: result.data, countryCodeStatus: DataStatus.success));
      } else {
        emit(state.copyWith(error: result.error, countryCodeStatus: DataStatus.failed));
      }
    } on Exception catch (e) {
      emit(state.copyWith(error: e.toString(), countryCodeStatus: DataStatus.failed));
    }
  }

  Future<void> getCountries() async {
    emit(state.copyWith(status: DataStatus.loading));
    try {
      final result = await _phoneCountryService.getCountries();
      if (result.isNotEmpty) {
        emit(state.copyWith(countries: result, status: DataStatus.success));
      } else {
        emit(state.copyWith(error: 'No countries found', status: DataStatus.failed));
      }
    } on Exception catch (e) {
      emit(state.copyWith(error: e.toString(), status: DataStatus.failed));
    }
  }
}
