part of 'country_code_cubit.dart';

class CountryCodeState extends Equatable {
  final CountryCodeData? countryCode;
  final List<PhoneCountry> countries;
  final DataStatus status;
  final DataStatus countryCodeStatus;
  final String? error;

  const CountryCodeState({
    this.countryCode,
    this.countries = const [],
    this.status = DataStatus.idle,
    this.countryCodeStatus = DataStatus.idle,
    this.error,
  });

  @override
  List<Object?> get props => [countryCode, countries, status, error, countryCodeStatus];

  CountryCodeState copyWith({
    CountryCodeData? countryCode,
    List<PhoneCountry>? countries,
    DataStatus? status,
    DataStatus? countryCodeStatus,
    String? error,
  }) =>
      CountryCodeState(
          countryCode: countryCode ?? this.countryCode,
          countries: countries ?? this.countries,
          status: status ?? this.status,
          countryCodeStatus: countryCodeStatus ?? this.countryCodeStatus,
          error: error ?? this.error);
}
