part of 'wallet_slider_cubit.dart';

class WalletSliderState extends Equatable {
  final int currentIndex;
  final int indexContract;

  const WalletSliderState({this.currentIndex = 0, this.indexContract = 0});

  @override
  List<Object> get props => [currentIndex, indexContract];

  WalletSliderState copyWith({
    int? currentIndex,
    int? indexContract,
  }) {
    return WalletSliderState(
      currentIndex: currentIndex ?? this.currentIndex,
      indexContract: indexContract ?? this.indexContract,
    );
  }
}
