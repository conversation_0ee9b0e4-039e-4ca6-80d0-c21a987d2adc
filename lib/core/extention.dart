import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/utils/shared_preference_helper.dart';
import 'package:easy_localization/easy_localization.dart';

import 'constants/app_constants.dart';

extension SignedInExtension on BuildContext {
  Future<void> handleSignedInAction({
    required VoidCallback onTap,
    bool skipAccountCheck = false,
  }) async {
    final isSignedIn = SharedPreferenceHelper().getIsLoggedIn() ?? false;
    final bool isAccount = skipAccountCheck ||
        (SharedPreferenceHelper().isAccountStatusLogged() ?? false);
    final isWalletPasswordSet =
        SharedPreferenceHelper().getIsWalletPasswordSet() ?? false;
    if (isSignedIn && isAccount && isWalletPasswordSet) {
      onTap();
    } else if (isSignedIn) {
      Navigator.pushNamed(this, routeAccountInformationScreen);
    } else {
      Navigator.pushNamed(this, routeLoginScreen);
    }
  }
}

extension WidgetExtension on Widget {
  Widget animatedSwitch({
    Curve? curvesIn,
    Curve? curvesOut,
    int duration = 200,
    int reverseDuration = 200,
  }) {
    return AnimatedSwitcher(
      duration: Duration(milliseconds: duration),
      reverseDuration: Duration(milliseconds: reverseDuration),
      switchInCurve: curvesIn ?? Curves.linear,
      switchOutCurve: curvesOut ?? Curves.linear,
      child: this,
    );
  }

  static Widget crossSwitch({
    required Widget first,
    Widget second = const SizedBox.shrink(),
    required bool value,
    Curve curvesIn = Curves.linear,
    Curve curvesOut = Curves.linear,
  }) {
    return AnimatedCrossFade(
      firstChild: first,
      secondChild: second,
      crossFadeState:
          value ? CrossFadeState.showFirst : CrossFadeState.showSecond,
      duration: const Duration(milliseconds: 300),
      firstCurve: curvesIn,
      secondCurve: curvesOut,
    );
  }
}

extension Context on BuildContext {
  double sh({double size = 1.0}) {
    return MediaQuery.of(this).size.height * size;
  }

  double sw({double size = 1.0}) {
    return MediaQuery.of(this).size.width * size;
  }

  int cacheSize(double size) {
    return (size * MediaQuery.of(this).devicePixelRatio).round();
  }

  void get rootPop => Navigator.of(this, rootNavigator: true).pop();

  Future get circularLoaderPopUp => showGeneralDialog(
        context: this,
        barrierColor: Colors.black.withValues(alpha: 0.3),
        barrierDismissible: false,
        barrierLabel: "",
        useRootNavigator: true,
        transitionDuration: const Duration(milliseconds: 400),
        pageBuilder: (_, __, ___) {
          return WillPopScope(
            child: const SizedBox.expand(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  CircularProgressIndicator(
                    backgroundColor: Colors.white,
                  ),
                ],
              ),
            ),
            onWillPop: () async {
              Navigator.pop(this);
              return false;
            },
          );
        },
      );

  double validateScale({double defaultVal = 0.0}) {
    double value = MediaQuery.of(this).textScaleFactor;
    double pixelRatio = ScreenUtil().pixelRatio ?? 0.0;
    0;
    if (value <= 1.0) {
      defaultVal = defaultVal;
    } else if (value >= 1.3) {
      defaultVal = value - 0.2;
    } else if (value >= 1.1) {
      defaultVal = value - 0.1;
    }
    if (pixelRatio <= 3.0) {
      defaultVal = defaultVal + 0;
    } else if (value >= 3.15) {
      defaultVal = defaultVal + 0.6;
    } else if (value >= 1.1) {
      defaultVal = defaultVal + 0.8;
    }
    return defaultVal;
  }
}

extension StringExtension on String {
  String capitalize() {
    if (isEmpty) {
      return '';
    }
    return '${this[0].toUpperCase()}${substring(1)}';
  }

  bool get notEmpty => (this).isNotEmpty;

  bool get empty => (this).isNotEmpty;
}

extension DateExtension on String? {
  String formatDate({String format = 'dd/MM/yyyy'}) {
    try {
      if (this == null || this!.isEmpty) {
        return DateFormat(format).format(DateTime.now());
      }
      final date = DateTime.parse(this!);
      return DateFormat(format).format(date);
    } catch (e) {
      return DateFormat(format).format(DateTime.now());
    }
  }
}

extension CurrencyExtension on String {
  String toCurrency() {
    if (isEmpty) {
      return '${AppConstants.currencySymbol} 0';
    }

    // Handle negative values
    bool isNegative = startsWith('-');
    String value = isNegative ? substring(1) : this;

    // Format number with commas
    try {
      double number = double.parse(value);
      String formatted = number.toStringAsFixed(2);
      List<String> parts = formatted.split('.');
      parts[0] = parts[0].replaceAllMapped(
        RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
        (Match m) => '${m[1]},',
      );
      value = parts.join('.');
    } catch (e) {
      // If parsing fails, return original value
      return '${AppConstants.currencySymbol} $this';
    }

    return isNegative
        ? '-${AppConstants.currencySymbol} $value'
        : '${AppConstants.currencySymbol} $value';
  }

  String toCurrencyWithSymbol() {
    if (isEmpty) {
      return '${AppConstants.currencySymbol} 0';
    }
    return '${AppConstants.currencySymbol} $this';
  }
}

extension ListExtension<T> on List<T> {
  T? firstWhereOrNull(bool Function(T element) test) {
    for (T element in this) {
      if (test(element)) return element;
    }
    return null;
  }
}
