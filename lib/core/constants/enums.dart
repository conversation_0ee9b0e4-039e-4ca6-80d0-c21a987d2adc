/// HTTP request methods
enum ApiMethod {
  get,
  post,
  patch,
  delete,
}

/// Data loading states
enum DataStatus {
  idle,
  loading,
  success,
  failed,
}

/// Types of UI feedback handlers
enum HandleTypes { snackbar, dialog, customDialog }

/// Types of snackbar messages
enum SnackBarType {
  warning,
  error,
  validation,
  success,
  info,
}

/// Status of investment process
enum ProcessStatus {
  pending(0), // Initialization/pending review
  approved(1), // Review successful
  denied(2), // Review rejected
  active(3), // Purchasing in progress
  closed(4); // Settled/end

  final int value;
  const ProcessStatus(this.value);
}

/// Types of investment actions
enum InvestmentAction { newInvest, append, withdraw }

/// Types of account information
enum AccountInfoType { googleToken, identity, mobileNo, wallet }

/// Types of amount input fields
enum AmountInputType {
  append,
  withdraw,
}

/// Types of investment operations
enum InvestmentType {
  newInvestment, // 0
  append, // 1
  withdraw, // 2
  community, // 3
}

/// Wallet type constants
const walletType = {
  'deposit': 1,
  'bonus': 5,
};

/// Stock index translations from Chinese to symbols
Map<String, String> stockIndexTranslation = {
  "道琼斯": ".DJI",
  "纳斯达克": ".IXIC", 
  "标普500": ".INX",
};

/// Types of crypto payment networks
enum PaymentType {
  ERC20,
  TRC20,
}

/// Types of ID card sides
enum CardType {
  front,
  back,
}

/// Market price color schemes
enum MarketColor {
  redUpGreenDown,
  greenUpRedDown,
}

enum RequestType {
  get,
  post,
  put,
  delete,
  patch,
}