import 'package:flutter/material.dart';

import '../models/Locale/locale_model.dart';

class AppConstants {
  static const String currencySymbol = 'USD';

  /// Base supported languages for all flavors
  static List<LocaleModel> baseSupportedLanguages = [
    LocaleModel(locale: const Locale('en', 'US'), sort: 1,isoCode: 'US'), //English (🇺🇸)
    LocaleModel(locale: const Locale('ko', 'KR'), sort: 2,isoCode: 'KR'), //한국인 (🇰🇷)
    LocaleModel(locale: const Locale('pt', 'BR'), sort: 3,isoCode: 'BR'), //Português (🇧🇷)
    LocaleModel(locale: const Locale('fr', 'FR'), sort: 4,isoCode: 'FR'), //Français (🇫🇷)
    LocaleModel(locale: const Locale('hi', 'IN'), sort: 5,isoCode: 'IN'), //हिंदी (🇮🇳)
    LocaleModel(locale: const Locale('es', 'ES'), sort: 6,isoCode: 'ES'), //Español (🇪🇸)
    LocaleModel(locale: const Locale('es', 'MX'), sort: 7,isoCode: 'MX'), //Español América (🇲🇽)
    LocaleModel(locale: const Locale('ja', 'JP'), sort: 8,isoCode: 'JP'), //日本語 (🇯🇵)
    LocaleModel(locale: const Locale('zh', 'CN'), sort: 9,isoCode: 'CN'), //简体中文 (🇨🇳)
    LocaleModel(locale: const Locale('ar', 'SA'), sort: 10,isoCode: 'SA'), //العربية (🇸🇦)
    LocaleModel(locale: const Locale('de', 'DE'), sort: 12,isoCode: 'DE'), //Deutsch (🇩🇪)
    LocaleModel(locale: const Locale('zh', 'HK'), sort: 99,isoCode: 'CN'), //繁体中文 (🇨🇳)
    LocaleModel(locale: const Locale('ms', 'MY'), sort: 7,isoCode: 'MY'), //Malay (🇲🇾)
  ];

  /// Additional languages only available for SIS flavor
  static List<LocaleModel> sisOnlyLanguages = [
    LocaleModel(locale: const Locale('it', 'IT'), sort: 11,isoCode: 'IT'), //Italiano (🇮🇹)
    LocaleModel(locale: const Locale('cs', 'CZ'), sort: 12,isoCode: 'CZ'), //Čeština (🇨🇿)
    LocaleModel(locale: const Locale('nl', 'NL'), sort: 12,isoCode: 'NL'), //Nederlands (🇳🇱)
    LocaleModel(locale: const Locale('sv', 'SE'), sort: 14,isoCode: 'SE'), //Svenska (🇸🇪)
    LocaleModel(locale: const Locale('no', 'NO'), sort: 15,isoCode: 'NO'), //Norsk (🇳🇴)
    LocaleModel(locale: const Locale('pl', 'PL'), sort: 16,isoCode: 'PL'), //Polski (🇵🇱)
    LocaleModel(locale: const Locale('ru', 'RU'), sort: 17,isoCode: 'RU'), //Русский (🇷🇺)
    LocaleModel(locale: const Locale('lt', 'LT'), sort: 19,isoCode: 'LT'), //Lietuvių (🇱🇹)
    LocaleModel(locale: const Locale('el', 'GR'), sort: 20,isoCode: 'GR'), //Ελληνικά (🇬🇷)
    LocaleModel(locale: const Locale('da', 'DK'), sort: 21,isoCode: 'DK'), //Dansk (🇩🇰)
    LocaleModel(locale: const Locale('fi', 'FI'), sort: 22,isoCode: 'FI'), //Suomi (🇫🇮)
    LocaleModel(locale: const Locale('hu', 'HU'), sort: 23,isoCode: 'HU'), //Magyar (🇭🇺)
    LocaleModel(locale: const Locale('et', 'EE'), sort: 24,isoCode: 'EE'), //Eesti (🇪🇪)
    LocaleModel(locale: const Locale('lv', 'LV'), sort: 25,isoCode: 'LV'), //Latviešu (🇱🇻)
    LocaleModel(locale: const Locale('sk', 'SK'), sort: 26,isoCode: 'SK'), //Slovenčina (🇸🇰)
    LocaleModel(locale: const Locale('sl', 'SI'), sort: 27,isoCode: 'SI'), //Slovenščina (🇸🇮)
    LocaleModel(locale: const Locale('tr', 'TR'), sort: 28,isoCode: 'TR'), //Türkçe (🇹🇷)
  ];

  /// All supported languages (base + SIS-only)
  static List<LocaleModel> supportedLanguages = [
    ...baseSupportedLanguages,
    ...sisOnlyLanguages,
  ];
}
