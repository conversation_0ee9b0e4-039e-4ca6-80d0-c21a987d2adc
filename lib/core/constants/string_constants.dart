class StringConstants {
  static const String appName = "appName";
  static const String invitationDescription = "invitationDescription";
  static const String stocks = "stocks";
  static const String crypto = "crypto";
  static String nameValidatorMsg(int value) =>
      "Must be more than $value characters";
  static String customGenValidatorMsg(String msg) => msg;
  static const String networkErrorMsg =
      "Oops! Something went wrong. Please check your network connection and try again.";

  static const String buildYourProfile = "buildYourProfile";
  static const String onBoardScreenSubtitle = "onBoardScreenSubtitle";
  static const String continueTxt = "continueTxt";
  static const String letsStart = "letsStart";
  static const String login = "login";
  static const String logout = "logout";
  static const String forgotPassword = "forgotPassword";
  static const String signIn = "signIn";
  static const String welcome = "welcome";
  static const String skip = "skip";
  static const String user = "user";
  static const String logoutConfirmation = "logoutConfirmation";
  static const String cancel = 'cancel';
  static const String added = 'added';
  static const String idCard = 'idCard';
  static const String emptyStringMsg = "emptyStringMsg";
  static const String enterPhone = "enterPhone";
  static const String enterCaptcha = "enterCaptcha";
  static const String captchaCode = "captchaCode";
  static const String emptyPasswordMsg = "emptyPasswordMsg";
  static const String emptyWalletMsg = "emptyWalletMsg";
  static const String emptyWalletPasswordMsg = "emptyWalletPasswordMsg";
  static const String emptyUserNameMsg = "emptyUserNameMsg";
  static const String emptyEmailMsg = "emptyEmailMsg";
  static const String emptyAddressNameMsg = "emptyAddressNameMsg";
  static const String emptyAddressEmailMsg = "emptyAddressEmailMsg";
  static const String loginLabelText = "loginLabelText";
  static const String emailLabelText = "emailLabelText";
  static const String password = "password";
  static const String enterYourFriendsEmail = "enterYourFriendsEmail";
  static const String loginHintText = "loginHintText";
  static const String passwordHintText = "passwordHintText";
  static const String confirmHintText = "confirmHintText";
  static const String invitationCode = "invitationCode";
  static const String emailHintText = "emailHintText";
  static const String dontHaveAccount = "dontHaveAccount";
  static const String alreadyHaveAccount = "alreadyHaveAccount";
  static const String iAgree = "iAgree";
  static const String signUp = "signUp";
  static const String logIn = "logIn";
  static const String verify = "verify";
  static const String tc = "tc";
  static const String and = "and";
  static const String privacyPolicy = "privacyPolicy";
  static const String accountName = "accountName";
  static const String accountInformation = "accountInformation";
  static const String submit = "submit";
  static const String idCardNumber = "idCardNumber";
  static const String idCardNumberHint = "idCardNumberHint";
  static const String idNumberErrorMessage = "idCardNumberErrorMessage";
  static const String uploadIdFront = "uploadIdFront";
  static const String uploadIdBack = "uploadIdBack";
  static const String bindIdCard = "bindIdCard";
  static const String uploadIdCard = "uploadIdCard";
  static const String uploadFrontIDCard = "uploadFrontIdCard";
  static const String uploadBackIDCard = "uploadBackIdCard";
  static const String idCardConfirmation = "idCardConfirmation";
  static const String idCardFront = "idCardFront";
  static const String idCardBack = "idCardBack";
  static const String openWallet = "openWallet";
  static const String walletPassword = "walletPassword";
  static const String reservedPhone = "reservedPhone";
  static const String enterPhoneNumber = "enterPhoneNumber";
  static const String googleAuthentication = "googleAuthentication";
  static const String enterGoogleCode = "enterGoogleCode";
  static const String suffixAddText = "suffixAddText";
  static const String setWalletPassword = "setWalletPassword";
  static const String enterWalletPassword = "enterWalletPassword";
  static const String confirmWalletPassword = "confirmWalletPassword";
  static const String phoneVerification = "phoneVerification";
  static const String countryDialCode = "countryDialCode";
  static const String googleAuthTitleText = "googleAuthTitleText";
  static const String googleAuthSubTitleText = "googleAuthSubTitleText";
  static const String googleAuthSubTitle2Text = "googleAuthSubTitle2Text";
  static const String qrCodeScan = "qrCodeScan";
  static const String copyCode = "copyCode";
  static const String code = "code";
  static const String googleAuthCode = "googleAuthCode";
  static const String home = 'home';
  static const String myContracts = 'myContracts';
  static const String community = 'community';
  static const String profile = 'profile';
  static const String deposit = 'deposit';
  static const String withdraw = 'withdraw';
  static const String transfer = 'transfer';
  static const String records = 'records';
  static const String contracts = 'contracts';
  static const String contractList = 'contractList';
  static const String marketUpdates = 'marketUpdates';
  static const String news = 'news';
  static const String newsUpdates = 'newsUpdates';
  static const String viewAll = 'viewAll';
  static const String seeAll = 'seeAll';
  static const String accountTotal = "accountTotal";
  static const String contractedAmount = "contractedAmount";
  static const String availableBalance = "availableBalance";
  static const String freezeAmount = "FreezeAmount";
  static const String referAndEarn = "referAndEarn";

  static const String bankAccount = "bankAccount";
  static const String bankText = "bankText";
  static const String bank = 'bank';
  static const String editBank = 'editBank';
  static const String accountNumber = "accountNumber";

  static const String tasks = "tasks";

  static const String information = "information";

  static const String missions = 'missions';
  static const String security = 'security';
  static const String products = 'products';
  static const String language = 'language';

  static const String share = "share";

  static const String shareText = 'shareText';

  static const String customerSupport = "customerSupport";
  static const String customerService = "customerService";
  static const String support = "support";
  static const String whatsappSupport = "whatsappSupport";
  static const String whatsappSupportDesc = "whatsappSupportDesc";
  static const String emailSupport = "emailSupport";
  static const String emailSupportDesc = "emailSupportDesc";
  static const String liveChat = "liveChat";
  static const String liveChatDesc = "liveChatDesc";
  static const String phoneSupport = "phoneSupport";
  static const String phoneSupportDesc = "phoneSupportDesc";
  static const String subject = "subject";
  static const String message = "message";
  static const String submitRequest = "submitRequest";
  static const String contactSupport = "contactSupport";

  static const String helpCenter = "helpCenter";

  static const String report = "report";

  static const String aboutUs = "aboutUs";

  static const String download = "download";

  static const String securityOptionsLabel = "securityOptionsLabel";

  static const String bindMobile = "bindMobile";

  static const String customValidatorMsg = "customValidatorMsg";

  static const String errorMsg = "errorMsg";
  static const String errorAuth = "errorAuth";
  static const String emailValidatorMsg = "emailValidatorMsg";
  static const String passwordValidationMsg = "passwordValidationMsg";
  static const String passwordNotEqual = "passwordNotEqual";
  static const String error = 'error';
  static const String noInternet = "noInternet";
  static const String validAddressNameMsg = "validAddressNameMsg";
  static const String proceedToLogin = "proceedToLogin";
  static const String incorrectOtp = "incorrectOtp";
  static const String incorrectCaptcha = "incorrectCaptcha";
  static const String adAdminWill = "adAdminWill";
  static const String idCardUnder = "aadhaarCardUnder";
  static const String alertMsgAdminVerify = "alertMsgAdminVerify";
  static const String alertProceedToLogin = "alertProceedToLogin";
  static const String purchasedContracts = "purchasedContracts";
  static const String purchasedProducts = "purchasedProducts";
  static const String loginLinkLabelText = "loginLinkLabelText";
  static const String invitationLinkLabelText = "invitationLinkLabelText";
  static const String securitySettings = "securitySettings";
  static const String changePassword = "changePassword";
  static const String changeWalletPassword = "changeWalletPassword";
  static const String changeLoginPassword = "changeLoginPassword";
  static const String changeGoogleAuthentication = "changeGoogleAuthentication";
  static const String changePaymentPassword = "changePaymentPassword";
  static const String changeNumber = "changeNumber";
  static const String changeAuthCode = "changeAuthCode";
  static const String networkLineLabelText = "networkLineLabelText";
  static const String addressNameTextController = "addressNameTextController";
  static const String addressTextLabelText = "addressTextLabelText";
  static const String addressTextHintText = "addressTextHintText";
  static const String withdrawAddressLabelText = "withdrawAddressLabelText";
  static const String withdrawAddressHintText = "withdrawAddressHintText";
  static const String googleAuthCodeLabelText = "googleAuthCodeLabelText";
  static const String googleAuthCodeHintText = "googleAuthCodeHintText";
  static const String addAddressWarning = "addAddressWarning";
  static const String authentication = "authentication";
  static const String sendCodeToEmail = "sendCodeToEmail";
  static const String sendCodeAlert = "sendCodeAlert";
  static const String checkEmail = "checkEmail";
  static const String checkPhone = "checkPhone";
  static const String resetPassword = "resetPassword";
  static const String passwordUpdatedToast = "passwordUpdatedToast";
  static const String walletUpdatedToast = "walletUpdatedToast";
  static const String numberUpdatedToast = "numberUpdatedToast";
  static const String googleCodeUpdatedToast = "googleCodeUpdatedToast";
  static const String enterCode = "enterCode";
  static const String newWalletPasswordLabel = "newWalletPasswordLabel";
  static const String confirmWalletPasswordLabel = "confirmWalletPasswordLabel";
  static const String confirmPasswordLabel = "confirmPasswordLabel";
  static const String newPhoneNumberLabel = "newPhoneNumberLabel";
  static const String newPassword = "newPassword";
  static const String numberOfTransactions = "numberOfTransactions";
  static const String tootTipWarning = "tootTipWarning";
  static const String toolTipWarning2 = "toolTipWarning2";
  static const String collectionWallet = "collectionWallet";
  static const String handlingFeeMessage = "handlingFeeMessage";
  static const String depositWallet = 'depositWallet';
  static const String bonusWallet = 'bonusWallet';
  static const String rechargeQr = 'rechargeQr';
  static const String rechargeAddress = 'rechargeAddress';
  static const String copied = 'copied';
  static const String copy = 'copy';
  static const String trc20 = "trc20";
  static const String erc20 = "erc20";
  static const String thisRechargeAddressOneTime = "thisRechargeAddressOneTime";
  static const String smallRechargesBelow = "smallRechargesBelow";
  static const String pleaseComplete = "pleaseComplete";
  static const String ok = 'ok';
  static const String toCommunity = 'toCommunity';
  static const String transferTo = 'transferTo';
  static const String usdt = 'usdt';
  static const String toCollectionWallet = 'toCollectionWallet';
  static const String toProfitWallet = 'toProfitWallet';
  static const String toDepositWallet = 'toDepositWallet';
  static const String toCommunityWallet = 'toCommunityWallet';
  static const String notification = 'notification';
  static const String market = 'market';
  static const String mainstreamCurrency = 'mainstreamCurrency';
  static const String latestTransaction = 'latestTransaction';
  static const String todaysChange = "todaysChange";
  static const String transactionRecords = 'transactionRecords';
  static const String alertUnbind = 'alertUnbind';
  static const String alertCancel = 'alertCancel';
  static const String unbind = "unbind";
  static const String canceled = "canceled";
  static const String weHaveSent = "weHaveSent";
  static const String enterVerification = "enterVerification";
  static const String emailVerification = "emailVerification";
  static const String didntRecieve = "didntRecieve";
  static const String resendCode = "resendCode";
  static const String next = "next";
  static const String confirm = "confirm";
  static const String done = "done";
  static const String termsAndConditions = "termsAndConditions";
  static const String tryAgain = "tryAgain";
  static const String close = "close";
  static const String chooseWallet = 'chooseWallet';
  static const String setAmount = 'setAmount';
  static const String enterAmount = 'enterAmount';
  static const String walletPass = 'walletPass';
  static const String summary = 'summary';
  static const String purchaseContracts = 'purchaseContracts';
  static const String availableContracts = 'availableContracts';
  static const String enterPassword = 'enterPassword';
  static const String totalAmount = "totalAmount";
  static const String numOfContracts = "numOfContracts";
  static const String buyIt = 'buyIt';
  static const String returnRate = 'returnRate';
  static const String retracementRate = 'retracementRate';
  static const String contractSize = 'contractSize';
  static const String contractPrice = 'contractPrice';
  static const String contractType = 'contractType';
  static const String enterValidPassword = 'enterValidPassword';
  static const String enterExactly6Digits = 'enterExactly6Digits';
  static const String enterValidGoogleCode = 'enterValidGoogleCode';
  static const String enterValidUsername = 'enterValidUsername';
  static const String enterValidPhone = 'enterValidPhone';
  static const String accountInformationStatus1 = 'accountInformationStatus1';
  static const String accountInformationStatus2 = 'accountInformationStatus2';
  static const String accountInformationStatus3 = 'accountInformationStatus3';
  static const String accountInformationStatus4 = 'accountInformationStatus4';
  static const String contractType1 = 'contractType1';
  static const String contractType2 = 'contractType2';
  static const String contractType3 = 'contractType3';
  static const String invalidPassword = 'invalidPassword';
  static const String otpCodeError = 'otpCodeError';
  static const String enterEmailCode = 'enterEmailCode';
  static const String invalidPassword2 = 'invalidPassword2';
  static const String urlPrefix = 'urlPrefix';
  static const String loginLinkSuffix = 'loginLinkSuffix';
  static const String invitationCodeSuffix = 'invitationCodeSuffix';
  static const String copiedClipboard = 'copiedClipboard';
  static const String enterThePassword = 'enterThePassword';
  static const String actualAmount = 'actualAmount';
  static const String withdrawalFee = 'withdrawalFee';
  static const String summaryWithdraw = 'summaryWithdraw';
  static const String summaryTransfer = 'summaryTransfer';
  static const String purchased = 'purchased';
  static const String successfully = 'successfully';
  static const String walletNameExists = 'walletNameExists';
  static const String walletAddressExists = 'walletAddressExists';
  static const String transferred = 'transferred';
  static const String withdrawLimit = 'withdrawLimit';
  static const String insufficientBalance = 'insufficientBalance';
  static const String emailError = 'emailError';
  static const String uploadImageError = 'uploadImageError';
  static const String frontImageLarge = 'frontImageLarge';
  static const String backImageLarge = 'backImageLarge';
  static const String addWithdrawal = 'addWithdrawal';
  static const String deleteAddress = 'deleteAddress';
  static const String confirmDelete = 'confirmDelete';
  static const String deleteAddressConfirmation = 'deleteAddressConfirmation';
  static const String zeroContractToast = 'zeroContractToast';
  static const String unBindSuccess = 'unBindSuccess';
  static const String cancelSuccess = 'cancelSuccess';
  static const String requestSuccess = 'requestSuccess';
  static const String emailAlready = 'emailAlready';
  static const String registerSuccess = 'registerSuccess';
  static const String approvedSuccess = 'approvedSuccess';
  static const String checkBoxSignupError = 'checkBoxSignupError';
  static const String aadhaar = 'aadhaar';
  static const String wallet = 'wallet';
  static const String phone = 'phone';
  static const String google = 'google';
  static const String typeGoogle = 'typeGoogle';
  static const String typeConfirmWallet = 'typeConfirmWallet';
  static const String signingYouOut = 'signingYouOut';
  static const String or = 'or';
  static const String hintWallet = 'hintWallet';
  static const String hintPhone = 'hintPhone';
  static const String statistics = 'statistics';
  static const String profitRatio = 'profitRatio';
  static const String theStatisticalSample = 'theStatisticalSample';
  static const String buy = 'buy';
  static const String pleaseWait = 'pleaseWait';
  static const String typeCollection = 'typeCollection';
  static const String typeDeposit = 'typeDeposit';
  static const String numbering = 'numbering';
  static const String purchaseTime = 'purchaseTime';
  static const String purchasePrice = 'purchasePrice';
  static const String earningsLastDay = 'earningsLastDay';
  static const String pastEarnings = 'pastEarnings';
  static const String daysOfHolding = 'daysOfHolding';
  static const String daysSinceUnbind = 'daysSinceUnbind';
  static const String days = 'days';
  static const String sellingTime = 'sellingTime';
  static const String sellingPrice = 'sellingPrice';
  static const String handlingFee = 'handlingFee';
  static const String volume = 'volume';
  static const String enterTheCode = 'enterTheCode';
  static const String seconds = 'seconds';
  static const String secondsUpper = 'secondsUpper';
  static const String enter4to12characters = 'enter4to12characters';
  static const String atLeast8character = 'atLeast8character';
  static const String atLeast8characterWithoutUpperCase =
      'atLeast8characterWithoutUpperCase';
  static const String transactionsHistory = 'transactionsHistory';
  static const String addWithdrawalAddress = 'addWithdrawalAddress';
  static const String datePrivacy = 'datePrivacy';
  static const String dateTerms = 'dateTerms';
  static const String passwordHint = 'passwordHint';
  static const String OR = 'OR';
  static const String name = 'name';
  static const String type = 'type';
  static const String size = 'size';
  static const String address = 'address';
  static const String withdrawAddresses = 'withdrawAddresses';
  static const String somethingWentWrong = 'somethingWentWrong';
  static const String somethingWentWrongTryAgain = 'somethingWentWrongTryAgain';
  static const String sendCode = 'sendCode';
  static const String passwordUpdatedSuccessfully =
      'passwordUpdatedSuccessfully';
  static const String timesUpper = "timesUpper";
  static const String profitTimes = "profitTimes";
  static const String lossesTimes = "lossesTimes";
  static const String notAvailable = 'notAvailable';
  static const String empty = 'empty';
  static const String profitWallet = 'profitWallet';

  // App Update Overlay
  static const String appUpdateAvailable = 'appUpdateAvailable';
  static const String unknownVersion = 'unknownVersion';
  static const String updating = 'updating';
  static const String readyToUpdate = 'readyToUpdate';
  static const String currentVersion = 'currentVersion';
  static const String updateNow = 'updateNow';
  static const String downloading = 'downloading';
  static const String installing = 'installing';
  static const String downloadComplete = 'downloadComplete';
  static const String installationComplete = 'installationComplete';
  static const String downloadFailed = 'downloadFailed';
  static const String installationFailed = 'installationFailed';
  static const String retryUpdate = 'retryUpdate';
  static const String skipUpdate = 'skipUpdate';
  static const String forceUpdateRequired = 'forceUpdateRequired';
  static const String downloadingUpdate = 'downloadingUpdate';
  static const String installingUpdate = 'installingUpdate';
  static const String updateRequired = 'updateRequired';
  static const String networkError = 'networkError';
  static const String storagePermissionRequired = 'storagePermissionRequired';
  static const String installPermissionRequired = 'installPermissionRequired';
  static const String downloadUrlNotAvailable = 'downloadUrlNotAvailable';
  static const String failedToDownloadUpdate = 'failedToDownloadUpdate';
  static const String failedToInstallUpdate = 'failedToInstallUpdate';
  static const String communityWallet = 'communityWallet';
  static const String walletPasswordShouldBe = 'walletPasswordShouldBe';
  static const String googleCodeShouldBe = 'googleCodeShouldBe';
  static const String enterValidErc = 'enterValidErc';
  static const String enterValidTrc = 'enterValidTrc';
  static const String maintenanceNotice = 'maintenanceNotice';
  static const String systemNotifications = 'systemNotifications';
  static const String contractAnnouncement = 'contractAnnouncement';
  static const String noGalleryPermission = 'noGalleryPermission';
  static const String changeIdentity = 'changeIdentity';
  static const String addAccountInfo = 'addAccountInfo';
  static const String serviceAgreement = 'serviceAgreement';
  static const String mentors = 'mentors';
  static const String follow = 'follow';
  static const String marketOverview = 'marketOverview';
  static const String winRate = 'winRate';
  static const String monthly = 'monthly';
  static const String drawdown = 'drawdown';
  static const String darkMode = 'darkMode';
  static const String usMarket = 'usMarket';
  static const String stockTrading = 'stockTrading';
  static const String todaysStockMarket = 'todaysStockMarket';
  static const String hotStocks = 'hotStocks';
  static const String leadingConcept = 'leadingConcept';
  static const String leadingIndustry = 'leadingIndustry';
  static const String gainers = 'gainers';
  static const String losers = 'losers';
  static const String change = 'change';
  static const String latestPrice = 'latestPrice';
  static const String search = 'search';
  static const String noResultsFound = 'noResultsFound';
  static const String sell = 'sell';
  static const String optional = 'optional';
  static const String warning = 'warning';
  static const String high = 'high';
  static const String low = 'low';
  static const String open = 'open';
  static const String smartInvestment = 'smartInvestment';
  static const String officeCompany = 'officeCompany';
  static const String workingAge = 'workingAge';
  static const String jobPosition = 'jobPosition';
  static const String expert = 'expert';
  static const String portfolio = 'portfolio';
  static const String maxDrawdown = 'maxDrawdown';
  static const String oneClickPurchase = 'oneClickPurchase';
  static const String automaticRenewal = 'automaticRenewal';
  static const String celebrityMentor = 'celebrityMentor';
  static const String smartInvestmentCycle = 'smartInvestmentCycle';
  static const String tutorCommission = 'tutorCommission';
  static const String minimumPurchaseAmount = 'minimumPurchaseAmount';
  static const String maximumPurchaseAmount = 'maximumPurchaseAmount';
  static const String investmentUnits = 'investmentUnits';
  static const String followUpPurchaseAmount = 'followUpPurchaseAmount';
  static const String amountMustBeInUnits = 'amountMustBeInUnits';
  static const String smartInvestmentProducts = 'smartInvestmentProducts';
  static const String tradingDays = 'tradingDays';
  static const String haveReadAndAgreedToThe = 'haveReadAndAgreedToThe';
  static const String intelligentFollowUpInvestment =
      'intelligentFollowUpInvestment';
  static const String oneClickSmartInvestmentInstructions =
      'oneClickSmartInvestmentInstructions';
  static const String oneClickSmartInvestmentDescription =
      'oneClickSmartInvestmentDescription';
  static const String transactionCycle = 'transactionCycle';
  static const String fundingThreshold = 'fundingThreshold';
  static const String followPurchaseDetails = 'followPurchaseDetails';
  static const String followPurchaseRecords = 'followPurchaseRecords';
  static const String auditing = 'auditing';
  static const String progress = 'progress';
  static const String following = 'following';
  static const String inProcess = 'inProcess';
  static const String finished = 'finished';
  static const String rejected = 'rejected';
  static const String completed = 'completed';
  static const String readMore = 'readMore';
  static const String readLess = 'readLess';

  static const String typePaymentERC = 'ERC20';
  static const String typePaymentTRC = 'TRC20';
  static const String typeFront = 'front';
  static const String typeBack = 'back';

  static const String successful = 'successful';
  static const String confirmInvestment = 'confirmInvestment';
  static const String additionalInvestment = 'additionalInvestment';
  static const String withdrawInvestment = 'withdrawInvestment';
  static const String paymentWallet = 'paymentWallet';
  static const String noBalanceInformation = 'noBalanceInformation';
  static const String pleaseDepositSomeAmountToContinue =
      'pleaseDepositSomeAmountToContinue';
  static const String noBalanceAvailable = 'noBalanceAvailable';
  static const String willBeSentToProfitWallet = 'willBeSentToProfitWallet';
  static const String withdrawal = 'withdrawal';
  static const String purchase = 'purchase';
  static const String years = 'years';
  static const String orderNo = 'orderNo';
  static const String cycle = 'cycle';
  static const String mentorCommission = 'mentorCommission';
  static const String followUpPeriod = 'followUpPeriod';
  static const String productName = 'productName';
  static const String amount = 'amount';
  static const String starMentor = 'starMentor';
  static const String reason = 'reason';
  static const String day = 'day';
  static const String append = 'append';
  static const String details = 'details';
  static const String followPurchaseDetailsTitle = 'followPurchaseDetailsTitle';
  static const String noDetailsAvailable = 'noDetailsAvailable';
  static const String stockName = 'stockName';
  static const String stockCode = 'stockCode';
  static const String buyQuantity = 'buyQuantity';
  static const String buyingPrice = 'buyingPrice';
  static const String buyPosition = 'buyPosition';
  static const String purchaseDate = 'purchaseDate';
  static const String sellQuantity = 'sellQuantity';
  static const String sellDate = 'sellDate';
  static const String revenueDetails = 'revenueDetails';
  static const String initialCapital = 'initialCapital';
  static const String additionalFunds = 'additionalFunds';
  static const String cumulativeIncome = 'cumulativeIncome';
  static const String withdrawnProfit = 'withdrawnProfit';
  static const String appendRejectedAmount = 'appendRejectedAmount';
  static const String totalRevenue = 'totalRevenue';
  static const String platformCommission = 'platformCommission';
  static const String revenue = 'revenue';
  static const String commission = 'commission';
  static const String additionalAmount = 'additionalAmount';
  static const String withdrawalAmount = 'withdrawalAmount';
  static const String mustBeMultipleOf = 'mustBeMultipleOf';
  static const String enterWithdrawalAmount = 'enterWithdrawalAmount';
  static const String max = 'max';
  static const String todayProfit = 'todayProfit';
  static const String pleaseEnterAValidAmount = 'pleaseEnterAValidAmount';
  static const String minimumAmount = 'minimumAmount';
  static const String maximumAmount = 'maximumAmount';
  static const String minimumWithdrawalAmount = 'minimumWithdrawalAmount';
  static const String maximumWithdrawalAmount = 'maximumWithdrawalAmount';
  static const String amountMustBeMultipleOf = 'amountMustBeMultipleOf';
  static const String amountExceedsMaximumAllowed =
      'amountExceedsMaximumAllowed';
  static const String invalidAmount = 'invalidAmount';
  static const String insufficientAvailableBalance =
      'insufficientAvailableBalance';
  static const String invitationCodeError = 'invitationCodeError';
  static const String chat = 'chat';
  static const String certificationCompleted = 'certificationCompleted';
  static const String connecting = 'connecting';
  static const String connected = 'connected';
  static const String email = 'email';
  static const String phoneNumber = 'phone_number';
  static const String enterEmail = 'enter_email';
  static const String otp = 'otp';
  static const String otpPhone = 'otp_phone';
  static const String sendCode1 = 'send_code';
  static const String in1 = 'in';
  static const String fundingWallet = 'fundingWallet';
  static const String tradingWallet = 'tradingWallet';
  static const String lockedBalance = 'lockedBalance';
  static const String preview = 'preview';
  static const String orderDate = 'orderDate';
  static const String investmentAmount = 'investmentAmount';
  static const String buyPrice = 'buyPrice';
  static const String sellPrice = 'sellPrice';
  static const String profit = 'profit';
  static const String pending = 'pending';
  static const String signinAgree = 'signinAgree';
  static const String loginPasswordUpdatedToast = 'loginPasswordUpdatedToast';
  static const String buyingTimeNotice = 'buyingTimeNotice';
  static const String enterTransactionHash = 'enterTransactionHash';
  static const String transactionHash = 'transactionHash';
  static const String submitRecharge = 'submitRecharge';
  static const String rechargeOrderSubmittedSuccessfully =
      'rechargeOrderSubmittedSuccessfully';
  static const String enterValidTransactionHash = 'enterValidTransactionHash';
  static const String depositTip = 'depositTip';
  static const String finance = 'finance';
  static const String expectedProfit = 'expectedProfit';
  static const String actualProfit = 'actualProfit';
  static const String purchaseList = 'purchaseList';
  static const String noPurchasesYet = 'noPurchasesYet';
  static const String interestRate = 'interestRate';
  static const String lockPeriod = 'lockPeriod';
  static const String createTime = 'createTime';
  static const String profitAmount = 'profitAmount';
  static const String unknown = 'unknown';
  static const String settled = 'settled';
  static const String interestAmount = 'interestAmount';
  static const String sellTime = 'sellTime';
  static const String availableFrom = 'availableFrom';
  static const String availableUntil = 'availableUntil';
  static const String upcoming = 'upcoming';
  static const String available = 'available';
  static const String soldOut = 'soldOut';
  static const String directMembers = 'directMembers';
  static const String totalMembers = 'totalMembers';
  static const String otherMembers = 'otherMembers';
  static const String firstGeneration = 'firstGeneration';
  static const String secondGeneration = 'secondGeneration';
  static const String thirdGeneration = 'thirdGeneration';
  static const String failedToLoadCommissionStats =
      'failedToLoadCommissionStats';
  static const String retry = 'retry';
  static const String members = 'members';
  static const String memberInfo = 'memberInfo';
  static const String reviewFailed = 'reviewFailed';
  static const String up = 'up';
  static const String flat = 'flat';
  static const String down = 'down';
  static const String pending2 = 'pending2';

  static const String realtime = 'realtime';
  static const String week = 'week';
  static const String month = 'month';
  static const String daily = 'daily';
  static const String oneMin = 'oneMin';
  static const String fiveMin = 'fiveMin';
  static const String fifteenMin = 'fifteenMin';
  static const String thirtyMin = 'thirtyMin';
  static const String yearsOfExperience = 'yearsOfExperience';
  static const String monthlyReturn = 'monthlyReturn';
  static const String endTime = 'endTime';
  static const String minimumAmountNotMeet = '未滿足最低金額';
  static const String startTime = 'startTime';
  static const String noDataAvailable = 'noDataAvailable';
  static const String benefitRules = 'benefitRules';
  static const String selectWithdrawalAddress = "selectWithdrawalAddress";
  static const String withdrawHistory = "withdrawHistory";
  static const String noWithdrawHistory = "noWithdrawHistory";
  static const String withdrawalStatus = "withdrawalStatus";
  static const String processing = "processing";
  static const String completeTime = "completeTime";
  static const String all = "all";
  static const String unprocessed = "unprocessed";
  static const String success = "success";
  static const String pleaseEnterAValidInvestmentAmount =
      'pleaseEnterAValidInvestmentAmount';
  static const String pleaseSelectAnInvestmentProduct =
      'pleaseSelectAnInvestmentProduct';
  static const String pleaseAcceptTheServiceAgreement =
      'pleaseAcceptTheServiceAgreement';
  static const String vipLevelError = 'vipLevelError';

  static const String whatIsATransactionHash = 'whatIsATransactionHash';
  static const String whatIsATransactionHashDescription =
      'whatIsATransactionHashDescription';
  static const String erc20_title = 'erc20_title';
  static const String trc20_title = 'trc20_title';
  static const String erc20_description = 'erc20_description';
  static const String trc20_description = 'trc20_description';
  static const String transactionHashDescription = 'transactionHashDescription';
  static const String transactionHashDescription2 = 'transactionHashDescription2';
  static const String vipNotice = 'vipNotice';
  static const String vipNoticeDescription = 'vipNoticeDescription';
  static const String aMonthAgo = 'aMonthAgo';
  static const String daysAgo = 'daysAgo';
  static const String anHourAgo = 'anHourAgo';
  static const String aMinuteAgo = 'aMinuteAgo';
  static const String justNow = 'justNow';
  static const String aDayAgo = 'aDayAgo';
  static const String hoursAgo = 'hoursAgo';
  static const String minutesAgo = 'minutesAgo';
  static const String singleAmount = 'singleAmount';
  static const String iHaveReadAndAgreed = 'iHaveReadAndAgreed';
  static const String unavailableFunds = 'unavailableFunds';
  static const String availableFunds = 'availableFunds';
}
