import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app_v2/core/api/network/route_arguments/change_wallet_password_arguments.dart';
import 'package:sf_app_v2/core/api/network/route_arguments/common_auth_verification_arguments.dart';
import 'package:sf_app_v2/core/api/network/route_arguments/notification_details_arguments.dart';
import 'package:sf_app_v2/core/models/cm_model/cm_product_model.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/features/auth/account_info/screens/account_info_screen.dart';
import 'package:sf_app_v2/features/auth/account_info/screens/google_authentication_screen.dart';
import 'package:sf_app_v2/features/auth/account_info/screens/open_wallet_screen.dart';
import 'package:sf_app_v2/features/auth/account_info/screens/phone_verification_screen.dart';
import 'package:sf_app_v2/features/auth/account_info/screens/upload_aadhaar.dart';
import 'package:sf_app_v2/features/auth/forgot/screens/forgot_screen.dart';
import 'package:sf_app_v2/features/auth/forgot/screens/reset_password.dart';
import 'package:sf_app_v2/features/auth/sign_in/screens/sign_in_screen.dart';
import 'package:sf_app_v2/features/auth/sign_up/screens/benefit_rules.dart';
import 'package:sf_app_v2/features/auth/sign_up/screens/privacy_policy_screen.dart';
import 'package:sf_app_v2/features/auth/sign_up/screens/sign_up_screen.dart';
import 'package:sf_app_v2/features/auth/sign_up/screens/terms_and_condition_screen.dart';
import 'package:sf_app_v2/features/community/screens/member_info_screen.dart';
import 'package:sf_app_v2/features/finance/screens/finance_screen.dart';
import 'package:sf_app_v2/features/home/<USER>/support_screen.dart';
import 'package:sf_app_v2/features/main/screens/main_screen.dart';
import 'package:sf_app_v2/features/news/screens/news_details_screen.dart';
import 'package:sf_app_v2/features/notification/screens/notification_detail_screen.dart';
import 'package:sf_app_v2/features/notification/screens/notification_list_screen.dart';
import 'package:sf_app_v2/features/profile/screens/add_withdrawal_address_screen.dart';
import 'package:sf_app_v2/features/profile/screens/common_auth_verification_screen.dart';
import 'package:sf_app_v2/features/settings/screens/change_login_password_screen.dart';
import 'package:sf_app_v2/features/settings/screens/change_password_screen.dart';
import 'package:sf_app_v2/features/smart_investment/screens/agreement_screen.dart';
import 'package:sf_app_v2/features/smart_investment/screens/mentor_profile_screen.dart';
import 'package:sf_app_v2/features/smart_investment/screens/purchase_record_details_screen.dart';
import 'package:sf_app_v2/features/smart_investment/screens/purchase_record_list_screen.dart';
import 'package:sf_app_v2/features/splash/screens/onboard_screen.dart';
import 'package:sf_app_v2/features/splash/screens/splash_screen.dart';
import 'package:sf_app_v2/features/support/screens/customer_service_screen.dart';
import 'package:sf_app_v2/features/wallet/deposit/screens/deposit_screen.dart';
import 'package:sf_app_v2/features/wallet/records/screens/records_screen.dart';
import 'package:sf_app_v2/features/wallet/withdraw/screens/withdraw_screen.dart';

import '../../features/market_v2/logic/search/search_cubit.dart';
import '../../features/market_v2/market_search_screen.dart';
import '../../features/market_v2/market_section_screen.dart';
import '../../features/smart_investment/domain/models/mentor/mentor_model.dart';
import '../../features/smart_investment/screens/mentor_list_screen.dart';
import '../../features/transfer/screens/transfer_screen.dart';
import '../../features/wallet/withdraw/screens/withdraw_history_screen.dart';
import '../dependency_injection/injectable.dart';

class RouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    // Helper function to create MaterialPageRoute with settings
    MaterialPageRoute buildRoute(Widget widget) {
      return MaterialPageRoute(
        settings: settings,
        builder: (_) => widget,
      );
    }

    switch (settings.name) {
      case routeRoot:
        return buildRoute(const SplashScreen());

      case routeOnBoardScreen:
        return buildRoute(const OnBoardScreen());

      case routeLoginScreen:
        return buildRoute(const SignInScreen());

      case routeRegistrationScreen:
        return buildRoute(const SignUpScreen());

      case routeAccountInformationScreen:
        return buildRoute(const AccountInfoScreen());

      case routeMainScreen:
        final arguments = settings.arguments as Map<String, dynamic>?;
        return buildRoute(MainScreen(
          initialTabIndex: arguments?['initialTabIndex'],
        ));

      case routeDepositScreen:
        return buildRoute(const DepositScreen());

      case routeWithdrawScreen:
        return buildRoute(const WithdrawScreen());

      case routeTransferScreen:
        return buildRoute(const TransferScreen());

      case routeSmartInvestmentScreen:
        final arguments = settings.arguments as Map<String, dynamic>;
        return buildRoute(MentorProfileScreen(
          mentor: arguments['mentor'] as Mentor,
          data: arguments['data'] as CMProductModel?,
        ));

      case routeAuthVerificationScreen:
        final arguments = settings.arguments as CommonAuthVerificationArguments;
        return buildRoute(CommonAuthVerificationScreen(
          routeName: arguments.routeName.toString(),
        ));

      case routeChangeLoginPassword:
        final arguments = settings.arguments as ChangeSettingsArguments;
        return buildRoute(ChangeLoginPasswordScreen(
          nonce: arguments.nonce,
        ));

      case routeChangePasswordScreen:
        final arguments = settings.arguments as ChangeSettingsArguments;
        return buildRoute(ChangeWalletPasswordScreen(nonce: arguments.nonce));

      case routeForgotPassword:
        return buildRoute(const ForgotPasswordScreen());

      case routeUploadAadhaarScreen:
        return buildRoute(UploadAadhaarScreen());

      case routeOpenWalletScreen:
        return buildRoute(const OpenWalletScreen());

      case routePhoneVerificationScreen:
        return buildRoute(const PhoneVerificationScreen());

      case routeGoogleAuthenticationScreen:
        return buildRoute(const GoogleAuthenticationScreen());

      case routeRecordsScreen:
        return buildRoute(const RecordScreen());

      case routeAddAddressScreen:
        final arguments = settings.arguments as Map<String, dynamic>;
        return buildRoute(AddWithDrawlAddressScreen(items: arguments['items']));

      case routeServiceAgreementScreen:
        return buildRoute(const ServiceAgreementScreen());

      case routeNewsDetailScreen:
        final arguments = settings.arguments as Map<String, dynamic>;
        return buildRoute(NewsDetailsScreen(id: arguments['id']));

      case routeSmartInvestmentRecordScreen:
        return buildRoute(const PurchaseHistoryScreen());

      case routeSmartInvestmentRecordDetailScreen:
        final arguments = settings.arguments as Map<String, dynamic>;
        return buildRoute(PurchaseDetailScreen(
          transactionId: arguments['id'].toString(),
        ));

      case routeNotificationDetailScreen:
        final arguments = settings.arguments as NotificationDetailsArguments;
        return buildRoute(NotificationDetailScreen(
          id: arguments.id ?? 0,
          haveRead: arguments.haveRead ?? false,
          appBarTitle: arguments.title ?? '',
          appBarContent: arguments.content ?? '',
        ));

      case routeNotificationListScreen:
        return buildRoute(const NotificationListScreen());

      case routeTermsAndConditionScreen:
        return buildRoute(const TermsAndConditions());

      case routePrivacyPolicyScreen:
        return buildRoute(const PrivacyPolicyScreen());

      case routeBenefitRulesScreen:
        return buildRoute(const BenefitRules());

      case routeResetPassword:
        return buildRoute(const ResetPasswordScreen());

      case routeConversationScreen:
        return buildRoute(const ResetPasswordScreen());

      case routeFinanceScreen:
        return buildRoute(const FinanceScreen());

      case routeSupportScreen:
        return buildRoute(const SupportScreen());

      case routeCustomerSupportScreen:
        return buildRoute(const CustomerServiceScreen());

      case routeMemberInfoScreen:
        final arguments = settings.arguments as Map<String, dynamic>;
        return buildRoute(MemberInfoScreen(level: arguments['level']));

      case routeMarketSearchScreen:
        return buildRoute(
          BlocProvider(
            create: (context) => getIt<SearchCubit>(),
            child: const MarketSearchScreen(),
          ),
        );

      case routeMentorListScreen:
        final arguments = settings.arguments as Map<String, dynamic>;
        return buildRoute(MentorListScreen(
          showBackButton: arguments['showBackButton'] ?? false,
        ));

      case routeMarketSectionScreen:
        final arguments = settings.arguments as Map<String, dynamic>;
        return buildRoute(MarketSectionScreen(
          showBackButton: arguments['showBackButton'] ?? false,
        ));

      case routeWithdrawHistoryScreen:
        return buildRoute(const WithdrawHistoryScreen());
      default:
        return buildRoute(Scaffold(
          body: Center(child: Text('No route defined for ${settings.name}')),
        ));
    }
  }

  static Route<dynamic> errorRoute({String? error, bool argsError = false}) {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        appBar: AppBar(
          title: const Text('Error'),
          centerTitle: true,
        ),
        body: Center(
          child: Text(
            error ?? '${argsError ? 'Arguments' : 'Navigation'} Error',
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.black54,
            ),
          ),
        ),
      ),
    );
  }
}
