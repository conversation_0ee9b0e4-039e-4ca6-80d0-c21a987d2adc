import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/models/result.dart';
import '../../../../core/api/endpoint/api_endpoints.dart';
import '../../../../core/api/network/network.dart';
import '../../models/country_code/country_code.dart';
import 'country_code_repository.dart';

/// Service class that handles fetching country code data from the API
///
/// Implements [CountryCodeRepository] interface to provide country code functionality
@Injectable(as: CountryCodeRepository)
class CountryCodeService implements CountryCodeRepository {
  /// Fetches country code data from the API
  ///
  /// Makes a GET request to the country code endpoint and processes the response
  /// Returns a [ResponseResult] containing either:
  /// * [CountryCodeData] on success
  /// * Error message string on failure
  @override
  Future<ResponseResult<CountryCodeData>> getCountryCode() async {
    try {
      final Response response = await <PERSON><PERSON><PERSON>ider().get(
        ApiEndpoints.countryCode,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: CountryCode.fromJson(response.data).data);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch country code');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
