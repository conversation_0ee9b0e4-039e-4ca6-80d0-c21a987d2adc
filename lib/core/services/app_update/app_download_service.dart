import 'dart:async';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:install_plugin/install_plugin.dart';
import 'package:injectable/injectable.dart';

import '../../constants/string_constants.dart';
import '../../models/app_download/app_download.dart';
import '../../utils/log.dart';

enum DownloadStatus {
  idle,
  downloading,
  downloadComplete,
  downloadFailed,
  installing,
  installationComplete,
  installationFailed,
  permissionDenied,
}

@injectable
class AppDownloadService {
  final Dio _dio = Dio();
  CancelToken? _cancelToken;

  /// Download and install app update
  Stream<DownloadProgress> downloadAndInstallUpdate(String downloadUrl) async* {
    try {
      // Check and request permissions
      yield const DownloadProgress(progress: 0.0, status: DownloadStatus.idle);

      final hasPermissions = await _checkPermissions();
      if (!hasPermissions) {
        yield  DownloadProgress(
          progress: 0.0,
          status: DownloadStatus.permissionDenied,
          error: StringConstants.installPermissionRequired.tr(),
        );
        return;
      }

      // Start download
      yield const DownloadProgress(
          progress: 0.0, status: DownloadStatus.downloading);

      String? filePath;
      await for (final progress in _downloadFileWithProgress(downloadUrl)) {
        if (progress.status == DownloadStatus.downloadComplete) {
          filePath = progress.filePath;
        }
        yield progress;
        if (progress.status == DownloadStatus.downloadFailed) {
          return;
        }
        if (progress.status == DownloadStatus.downloadComplete) {
          break;
        }
      }

      if (filePath == null) {
        yield  DownloadProgress(
          progress: 0.0,
          status: DownloadStatus.downloadFailed,
          error: StringConstants.downloadFailed.tr(),
        );
        return;
      }

      // Download complete
      yield DownloadProgress(
        progress: 1.0,
        status: DownloadStatus.downloadComplete,
        filePath: filePath,
      );

      // Start installation
      yield DownloadProgress(
        progress: 1.0,
        status: DownloadStatus.installing,
        filePath: filePath,
      );

      final installSuccess = await _installApk(filePath);

      if (installSuccess) {
        yield DownloadProgress(
          progress: 1.0,
          status: DownloadStatus.installationComplete,
          filePath: filePath,
        );
      } else {
        yield DownloadProgress(
          progress: 1.0,
          status: DownloadStatus.installationFailed,
          filePath: filePath,
          error: StringConstants.installationFailed.tr(),
        );
      }
    } catch (e) {
      logDev(
          'AppDownloadService.downloadAndInstallUpdate.Error: $e', 'APP_UPDATE',
          error: true);
      yield DownloadProgress(
        progress: 0.0,
        status: DownloadStatus.downloadFailed,
        error: e.toString(),
      );
    }
  }

  /// Check required permissions
  Future<bool> _checkPermissions() async {
    if (Platform.isAndroid) {
      // Check install permission for Android 8.0+
      var installStatus = await Permission.requestInstallPackages.status;
      if (!installStatus.isGranted) {
        installStatus = await Permission.requestInstallPackages.request();
        if (!installStatus.isGranted) {
          return false;
        }
      }
    }
    return true;
  }

  /// Download file with progress tracking
  Stream<DownloadProgress> _downloadFileWithProgress(String url) async* {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          'app_update_${DateTime.now().millisecondsSinceEpoch}.apk';
      final filePath = '${directory.path}/$fileName';

      _cancelToken = CancelToken();

      // Use a stream controller to handle progress updates
      final progressController = StreamController<DownloadProgress>();

      // Start the download
      _dio.download(
        url,
        filePath,
        cancelToken: _cancelToken,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = received / total;
            progressController.add(DownloadProgress(
              progress: progress,
              status: DownloadStatus.downloading,
            ));
          }
        },
      ).then((_) {
        // Download completed successfully
        progressController.add(DownloadProgress(
          progress: 1.0,
          status: DownloadStatus.downloadComplete,
          filePath: filePath,
        ));
        progressController.close();
      }).catchError((error) {
        // Download failed
        progressController.add(DownloadProgress(
          progress: 0.0,
          status: DownloadStatus.downloadFailed,
          error: StringConstants.downloadFailed.tr(),
        ));
        progressController.close();
      });

      // Yield progress updates
      await for (final progress in progressController.stream) {
        yield progress;
      }
    } catch (e) {
      logDev('AppDownloadService._downloadFileWithProgress.Error: $e',
          'APP_UPDATE',
          error: true);
      yield DownloadProgress(
        progress: 0.0,
        status: DownloadStatus.downloadFailed,
        error: e.toString(),
      );
    }
  }

  /// Install APK file
  Future<bool> _installApk(String filePath) async {
    try {
      if (Platform.isAndroid) {
        final result = await InstallPlugin.installApk(filePath, appId: '');
        return result['isSuccess'] == true;
      }
      return false;
    } catch (e) {
      logDev('AppDownloadService._installApk.Error: $e', 'APP_UPDATE',
          error: true);
      return false;
    }
  }

  /// Cancel ongoing download
  void cancelDownload() {
    _cancelToken?.cancel('Download cancelled by user');
  }

  /// Clean up downloaded files
  Future<void> cleanupDownloadedFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final files = directory.listSync();

      for (final file in files) {
        if (file.path.contains('app_update_') && file.path.endsWith('.apk')) {
          await file.delete();
        }
      }
    } catch (e) {
      logDev(
          'AppDownloadService.cleanupDownloadedFiles.Error: $e', 'APP_UPDATE',
          error: true);
    }
  }
}
