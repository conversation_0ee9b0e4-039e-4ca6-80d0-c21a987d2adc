import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sf_app_v2/core/utils/log.dart';
import 'package:sf_app_v2/features/app_update/domain/models/app_update_model.dart';
import 'package:sf_app_v2/features/app_update/logic/app_update/app_update_cubit.dart';
import 'package:sf_app_v2/features/app_update/screens/update_overlay.dart';

/// Service class to handle showing update dialogs
@lazySingleton
class AppUpdateDialogService {
  /// Show update dialog using the existing UpdateOverlay
  /// Requires a valid BuildContext that has access to an Overlay widget
  static Future<void> showUpdateDialog(
    BuildContext context,
    AppUpdateModel updateModel,
  ) async {
    // Verify context is mounted and has access to Overlay
    if (!context.mounted) return;

    // Check if an Overlay widget exists in the widget tree
    try {
      Overlay.of(context);
    } catch (e) {
      // If no Overlay is found, we can't show the dialog
      debugPrint('AppUpdateDialogService: No Overlay widget found in context');
      return;
    }

    final cubit = context.read<AppUpdateCubit>();

    AppUpdateOverlay.show(
      context,
      updateInfo: updateModel,
      cubit: cubit,
      onUpdatePressed: () => cubit.startUpdate(),
      onCancelPressed: updateModel.data?.forceUpdate == true
          ? null
          : () => cubit.resetUpdateDialog(),
    );
  }

  /// Check if app should update by comparing versions
  Future<bool> shouldUpdate(String newVersion) async {
    // Check if version format matches x.x.x
    final versionRegex = RegExp(r'^\d+\.\d+\.\d+$');
    if (!versionRegex.hasMatch(newVersion)) {
      return false;
    }

    // Get current app version
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String currentVersion = packageInfo.version;
    logDev("Current version: $currentVersion, New version: $newVersion",
        'VERSION_CHECK',
        info: true);

    // Split version numbers into parts
    List<String> currentParts = currentVersion.split('.');
    List<String> newParts = newVersion.split('.');

    // Ensure both versions have 3 parts
    while (currentParts.length < 3) {
      currentParts.add('0');
    }
    while (newParts.length < 3) {
      newParts.add('0');
    }

    // Compare version numbers
    for (int i = 0; i < 3; i++) {
      int currentNum = int.parse(currentParts[i]);
      int newNum = int.parse(newParts[i]);

      if (newNum > currentNum) {
        return true;
      } else if (newNum < currentNum) {
        return false;
      }
    }

    return false;
  }
}
