import 'package:flutter/material.dart';

import '../../models/Locale/locale_model.dart';

/// Repository interface for language selection functionality
abstract class LanguageRepository {
  /// Get all supported locales
  List<LocaleModel> getSupportedLocales(BuildContext context);

  /// Get current locale
  Locale getCurrentLocale(BuildContext context);

  /// Set new locale
  Future<void> setLocale(BuildContext context, Locale locale);

  /// Get flag emoji for a locale
  String getFlagEmoji(BuildContext context, Locale? locale);

  /// Get language name for a locale
  String getLanguageName(Locale locale);

  /// Check if a locale is currently selected
  bool isLocaleSelected(BuildContext context, Locale locale);

  /// Set intl default locale
  void setIntlDefaultLocale(BuildContext context);
}
