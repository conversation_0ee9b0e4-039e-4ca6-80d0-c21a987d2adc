// import 'dart:developer';
// import 'dart:convert';

// import 'package:flutter/material.dart';
// import 'package:sf_app_v2/core/routes/navigator.dart';
// import 'package:tencent_cloud_chat_push/common/common_defines.dart';
// import 'package:tencent_cloud_chat_push/common/tim_push_listener.dart';
// import 'package:tencent_cloud_chat_push/tencent_cloud_chat_push.dart';

// import '../../../features/chat_v2/screens/chat_screen.dart';
// import '../../../features/chat_v2/utils/utils.dart';

// class TencentPushService {
//   final TencentCloudChatPush cPush = TencentCloudChatPush();

//   Future<void> initialize() async {
//     try {
//       // 1. First register the push service
//       await cPush.forceUseFCMPushChannel(
//         enable: true,
//       );
//       await cPush.registerPush(
//           sdkAppId: 70000554,
//           appKey: "nQq6yXUTBDa9dLo8HkjYYLkAyLkv5KxDfU9he2PoelK3KnIkReML649kKJW5BqnP",
//           onNotificationClicked: onNotificationClicked,
//           apnsCertificateID: 15292);
//       // TencentCloudChatPush().setApnsCertificateID(apnsCertificateID: 15292);

//       // 2. Get the registration ID from the service
//       final TencentCloudChatPushResult<dynamic> result = await cPush.getRegistrationID();
//       final String? regID = result.data as String?;
//       if (regID != null) {
//         log("Push Registration ID: $regID");
//         // 3. Set the registration ID only after getting it
//         // await cPush.setRegistrationID(registrationID: regID);
//       }
//       await cPush.createNotificationChannel(
//         channelID: "default_channel_id",
//         channelName: "Default Channel",
//       );
//       await cPush.addPushListener(
//         listener: TIMPushListener(
//           onRecvPushMessage: (msg) => log("Push Message Received: ${msg.toJson()}"),
//           onRevokePushMessage: (msg) => log("Push Message Revoked: $msg"),
//         ),
//       );
//     } catch (e) {
//       log("Push initialization error: $e");
//     }
//   }

//   void onNotificationClicked({required String ext, String? userID, String? groupID}) {
//     log("Push notification clicked: ext=$ext, userID=$userID, groupID=$groupID");
//     if (userID != null) {
//       navigatorKey.currentState?.push<dynamic>(
//         MaterialPageRoute(
//           builder: (context) => ChatScreen(
//             selectedConversation: getConversation(
//               userID: userID,
//               name: userID,
//             ),
//           ),
//         ),
//       );
//     }
//     if (groupID != null) {
//       navigatorKey.currentState?.push<dynamic>(
//         MaterialPageRoute(
//           builder: (context) => ChatScreen(
//             selectedConversation: getConversation(
//               groupID: groupID,
//               name: groupID,
//             ),
//           ),
//         ),
//       );
//     }
//   }

//   void onRecvPushMessage({
//     required String title,
//     required String desc,
//     required String ext,
//     required String messageID,
//   }) {
//     // Decode the Base64 encoded description
//     final String decodedDesc = utf8.decode(base64.decode(desc));

//     // Parse the ext JSON string
//     final Map<String, dynamic> extData = jsonDecode(ext);

//     // log("Decoded message: $decodedDesc");
//     // log("Ext data: $extData");

//     // Now you can use the decoded message to show the notification
//     _showNotification(
//       title: title.isNotEmpty ? title : "New Message",
//       body: decodedDesc,
//       ext: extData,
//     );
//   }

//   void _showNotification({
//     required String title,
//     required String body,
//     required Map<String, dynamic> ext,
//   }) {
//     // Your notification display logic here
//     log("Showing notification - Title: $title, Body: $body, Ext: $ext");
//   }
// }
