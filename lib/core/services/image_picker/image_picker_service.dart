import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/common_function.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/services/image_picker/image_picker_repository.dart';

/// Service class that handles image picking and cropping functionality
///
/// Implements [ImagePickerRepository] interface to provide image handling capabilities
@Injectable(as: ImagePickerRepository)
class ImagePickerService implements ImagePickerRepository {
  /// Picks an image from the specified source and crops it
  ///
  /// Takes an [ImageSource] parameter to determine where to pick the image from (camera/gallery)
  /// Returns a [File] containing the picked and cropped image
  /// Shows a toast message if gallery permissions are not granted
  @override
  Future getImage(ImageSource imageSource) async {
    try {
      XFile? pickedImage = await ImagePicker().pickImage(
        imageQuality: 20,
        source: imageSource,
      );
      File croppedFile = await cropImage(pickedImage?.path);
      return croppedFile;
    } catch (e) {
      if (e is PlatformException) {
        CommonFunctions()
            .showFlutterToast(StringConstants.noGalleryPermission.tr());
      }
    }
  }

  /// Crops the provided image file
  ///
  /// Takes an image file path as input
  /// Returns a [File] containing the cropped image if successful
  /// The image is compressed to 10% quality during cropping
  @override
  Future cropImage(imagePath) async {
    CroppedFile? croppedImage = await ImageCropper()
        .cropImage(sourcePath: imagePath, compressQuality: 10);
    if (croppedImage != null) {
      return File(croppedImage.path);
    }
  }
}
