import 'package:flutter/services.dart';
import 'package:injectable/injectable.dart';
import '../../models/phone_country/phone_country.dart';

/// Service class for managing phone country data and operations
/// Provides functionality to load, search and retrieve country information
@singleton
class PhoneCountryService {
  /// Cached list of phone countries to avoid repeated loading
  List<PhoneCountry>? _countries;
  
  /// Loads and returns the list of all phone countries
  /// Returns cached data if already loaded, otherwise loads from JSON file
  /// Throws exception if loading fails
  /// Returns [List<PhoneCountry>] containing all phone countries
  Future<List<PhoneCountry>> getCountries() async {
    if (_countries != null) {
      return _countries!;
    }
    
    try {
      final String jsonString = await rootBundle.loadString('assets/flags/phone-code-en.json');
      _countries = phoneCountryFromJson(jsonString);
      return _countries!;
    } catch (e) {
      throw Exception('Failed to load countries: $e');
    }
  }
  
  /// Finds and returns a country by its ISO country code
  /// [code] The ISO country code to search for (case insensitive)
  /// Returns [PhoneCountry?] - matching country or null if not found
  Future<PhoneCountry?> getCountryByCode(String code) async {
    final countries = await getCountries();
    try {
      return countries.firstWhere((country) => country.code.toUpperCase() == code.toUpperCase());
    } catch (e) {
      return null;
    }
  }
  
  /// Finds and returns a country by its phone code
  /// [phoneCode] The phone code to search for (e.g. '+1', '+44')
  /// Returns [PhoneCountry?] - matching country or null if not found
  Future<PhoneCountry?> getCountryByPhoneCode(String phoneCode) async {
    final countries = await getCountries();
    try {
      return countries.firstWhere((country) => country.phoneCode == phoneCode);
    } catch (e) {
      return null;
    }
  }
  
  /// Returns the default country (Mexico)
  /// If Mexico is not found in the data, returns first country from the list
  /// If list is empty, returns a hardcoded Mexico country object
  /// Returns [PhoneCountry] - default country object
  Future<PhoneCountry> getDefaultCountry() async {
    // Default to Mexico as per current implementation
    final mexico = await getCountryByCode('MX');
    if (mexico != null) {
      return mexico;
    }
    
    // Fallback if Mexico is not found
    final countries = await getCountries();
    return countries.isNotEmpty ? countries.first : const PhoneCountry(
      name: 'Mexico',
      code: 'MX',
      phoneCode: '+52',
      flagEmoji: '🇲🇽',
    );
  }
  
  /// Searches countries by name, code or phone code
  /// [query] The search query string
  /// Returns [List<PhoneCountry>] - all countries if query is empty, otherwise filtered list
  /// matching the query (case insensitive)
  Future<List<PhoneCountry>> searchCountries(String query) async {
    if (query.isEmpty) {
      return await getCountries();
    }
    
    final countries = await getCountries();
    final lowerQuery = query.toLowerCase();
    
    return countries.where((country) {
      return country.name.toLowerCase().contains(lowerQuery) ||
             country.code.toLowerCase().contains(lowerQuery) ||
             country.phoneCode.contains(query);
    }).toList();
  }
}
