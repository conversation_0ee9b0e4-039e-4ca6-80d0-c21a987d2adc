import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app_v2/core/widgets/notice_alert_dialog.dart';

import '../../features/notification/logic/notification/notification_cubit.dart';

/// Service class for managing notice dialog functionality
/// Provides methods to show, handle and track state of notice dialogs
class NoticeDialogService {
  /// Shows the notice dialog with notification data
  /// [context] The build context to show dialog in
  /// Returns [Future<bool>] indicating if dialog was shown successfully
  static Future<bool> showNoticeDialog(BuildContext context) async {
    try {
      // Check if a dialog with the same key is already showing
      if (_isDialogAlreadyShowing()) {
        return false;
      }

      // Mark dialog as active
      _setDialogActive(true);

      await showDialog<void>(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext dialogContext) =>
            BlocBuilder<NotificationCubit, NotificationState>(
          builder: (context, state) {
            if (state.notifications?.data?.isNotEmpty ?? false) {
              return NoticeAlertDialog(
                notification: state.notifications?.data,
                onOkPressed: () => _handleOkPressed(dialogContext),
                onClosePressed: () => _handleClosePressed(dialogContext),
              );
            }
            return const CupertinoActivityIndicator();
          },
        ),
      );

      // Mark dialog as inactive when closed
      _setDialogActive(false);
      return true;
    } catch (e) {
      // Mark dialog as inactive in case of error
      _setDialogActive(false);
      return false;
    }
  }

  /// Handles OK button press by closing the dialog
  /// [context] The build context to close dialog from
  static void _handleOkPressed(BuildContext context) {
    Navigator.of(context).pop();
  }

  /// Handles Close button press by closing the dialog
  /// [context] The build context to close dialog from
  static void _handleClosePressed(BuildContext context) =>
      Navigator.of(context).pop();

  /// Flag to track if dialog is currently showing
  static bool _isDialogActive = false;

  /// Checks if a notice dialog is already showing
  /// Returns [bool] indicating if dialog is active
  static bool _isDialogAlreadyShowing() => _isDialogActive;

  /// Sets the dialog active state
  /// [active] Boolean flag to set dialog state
  static void _setDialogActive(bool active) => _isDialogActive = active;
}
