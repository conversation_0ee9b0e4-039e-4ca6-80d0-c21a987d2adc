import 'dart:async';
import 'dart:convert';
import 'package:sf_app_v2/core/utils/log.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

import '../config/app_config.dart';
import '../dependency_injection/injectable.dart';

/// Represents the current state of the WebSocket connection
enum WebSocketConnectionState { disconnected, connecting, connected, error }

/// Service class for managing WebSocket connections and communication
/// Provides functionality for connecting, disconnecting, sending messages and handling reconnection
class WebSocketService {
  /// The WebSocket channel instance
  WebSocketChannel? _channel;
  
  /// Timer for sending periodic ping messages
  Timer? _pingTimer;
  
  /// Interval in seconds between ping messages
  int pingIn = 15;
  
  /// Flag to enable/disable logging
  bool logger = false;
  
  /// Current connection state
  WebSocketConnectionState _connectionState =
      WebSocketConnectionState.disconnected;

  /// Gets the current connection state as a string
  String get connectionState => _connectionState.name;

  /// Establishes a WebSocket connection
  /// 
  /// [onMessage] Callback function for handling received messages
  /// [onError] Callback function for handling errors
  /// [onDone] Callback function for when connection is closed
  void connect({
    required void Function(String message) onMessage,
    required void Function(String error) onError,
    required void Function() onDone,
  }) {
    if (_connectionState == WebSocketConnectionState.connected) {
      if (logger) logDev('Already connected to WebSocket', 'WS', info: true);
      return;
    }

    _updateConnectionState(WebSocketConnectionState.connecting);

    try {
      if (logger) logDev('Attempting to connect to WebSocket', 'WS', api: true);
      _channel = WebSocketChannel.connect(Uri.parse(getIt<AppConfig>().marketWsUrl));

      _updateConnectionState(WebSocketConnectionState.connected);
      if (logger) {
        logDev('WebSocket connected successfully', 'WS', success: true);
      }

      _channel?.stream.listen(
        (message) {
          if (logger) logDev(message, 'WS::MessageReceived', special: true);
          onMessage(message);
        },
        onDone: () {
          if (logger) logDev('WebSocket disconnected', 'WS::Close', info: true);
          _updateConnectionState(WebSocketConnectionState.disconnected);
          onDone();
          _attemptReconnect(
            onMessage: onMessage,
            onError: onError,
            onDone: onDone,
          );
        },
        onError: (error) {
          if (logger) logDev(error.toString(), 'WS::Error', error: true);
          _updateConnectionState(WebSocketConnectionState.error);
          onError(error.toString());
          _attemptReconnect(
            onMessage: onMessage,
            onError: onError,
            onDone: onDone,
          );
        },
        cancelOnError: true,
      );

      _startPingTimer();
    } catch (e) {
      if (logger) logDev(e.toString(), 'WS::ConnectionFailed', error: true);
      _updateConnectionState(WebSocketConnectionState.error);
      onError(e.toString());
      _attemptReconnect(onMessage: onMessage, onError: onError, onDone: onDone);
    }
  }

  /// Closes the WebSocket connection and cleans up resources
  void disconnect() {
    _stopPingTimer();
    _channel?.sink.close();
    _channel = null;
    _updateConnectionState(WebSocketConnectionState.disconnected);
    if (logger) logDev('WebSocket disconnected', 'WS', info: true);
  }

  /// Sends a message through the WebSocket connection
  /// 
  /// [action] The action type for the message
  /// [data] The data payload to send
  void sendMessage(String action, Map<String, dynamic> data) {
    if (_connectionState == WebSocketConnectionState.connected &&
        _channel != null) {
      final message = jsonEncode({"action": action, ...data});
      _channel?.sink.add(message);
      if (logger) logDev(message, 'WS::MessageSent', api: true);
    } else {
      if (logger) {
        logDev(
          'Cannot send message; WebSocket not connected',
          'WS',
          error: true,
        );
      }
    }
  }

  /// Starts the periodic ping timer
  void _startPingTimer() {
    _pingTimer?.cancel();
    _pingTimer = Timer.periodic(Duration(seconds: pingIn), (_) {
      if (_connectionState == WebSocketConnectionState.connected) {
        sendMessage("ping", {});
        if (logger) logDev('Ping sent to WebSocket', 'WS', info: true);
      }
    });
  }

  /// Stops the periodic ping timer
  void _stopPingTimer() {
    _pingTimer?.cancel();
    _pingTimer = null;
    if (logger) logDev('Ping timer stopped', 'WS', info: true);
  }

  /// Attempts to reconnect to the WebSocket server after a delay
  /// 
  /// [onMessage] Callback function for handling received messages
  /// [onError] Callback function for handling errors
  /// [onDone] Callback function for when connection is closed
  void _attemptReconnect({
    required void Function(String message) onMessage,
    required void Function(String error) onError,
    required void Function() onDone,
  }) {
    if (_connectionState == WebSocketConnectionState.connected) return;

    _stopPingTimer();
    Future.delayed(const Duration(seconds: 10), () {
      if (logger) logDev('Reconnecting to WebSocket...', 'WS', api: true);
      connect(onMessage: onMessage, onError: onError, onDone: onDone);
    });
  }

  /// Updates the connection state and logs the change if logging is enabled
  /// 
  /// [state] The new connection state
  void _updateConnectionState(WebSocketConnectionState state) {
    _connectionState = state;
    if (logger) {
      logDev('Connection state updated to: ${state.name}', 'WS', info: true);
    }
  }
}
