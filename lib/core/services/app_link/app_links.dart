import 'dart:async';
import 'dart:developer';

import 'package:app_links/app_links.dart';
import 'package:flutter/cupertino.dart';
import 'package:sf_app_v2/core/utils/global.dart' as global;

class AppLinksManager {
  late AppLinks _appLinks;
  StreamSubscription<Uri>? linkSubscription;

  Future<void> initAppLinks({
    required BuildContext context,
  }) async {
    _appLinks = AppLinks();
    linkSubscription = _appLinks.uriLinkStream.listen(
      (uri) {
        if (context.mounted) {
          openAppLink(uri, context);
        }
      },
    );
  }

  Future<void> openAppLink(Uri uri, BuildContext context) async {
    // Extract invitation code from query parameters
    String? invitationCode = uri.queryParameters['invitationCode'];
    if (invitationCode != null) {
      log('\n-----------------invitation code from query params-----------------');
      log(invitationCode);
    }

    if (invitationCode != null) {
      global.invitationCode = invitationCode;
    }
    // switch (pathSegments[0].toString()) {
    //   case 'product':
    //     final product = await context.read<MainCubit>().getProductIdFromSlug(
    //           slug: pathSegments[1],
    //         );
    //     navigatorKey.currentState?.pushNamed(
    //       RouteGenerator.routeProductDetails,
    //       arguments: {'item': product},
    //     );
    //     break;
    // }
  }
}
