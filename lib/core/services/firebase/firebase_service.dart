// import 'dart:convert';
// import 'dart:developer';

// import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';

// Future<void> fcmBackgroundHandler(RemoteMessage message) async {
//   log('Handling a background message: ${message.messageId}');
//   log('Notification: ${message.notification?.toMap()}');
// }

// const MethodChannel _channel = MethodChannel('com.sf.notifications');
// void initialize() {
//   _channel.setMethodCallHandler(_handleMethodCall);
// }

// Future<void> _handleMethodCall(MethodCall call) async {
//   log('heloooooooo');
 
// }

// // void handleNotificationClick(NotificationResponse response) async {
// //   initialize();
// //   log('Notification clicked');
// //   // Handle the notification click event here
// //   if (response.payload != null) {
// //     log('Notification payload: ${response.payload}');
// //     // Parse the payload to extract the notification_type
// //     final Map<String, dynamic> data = jsonDecode(response.payload!);
// //     if (data.containsKey('notification_type')) {
// //       log('Notification type: ${data['notification_type']}');
// //       if (data['notification_type'] != null) {
// //         navigatorKey.currentState?.context
// //             .read<OrderCubit>()
// //             .orderDetails(orderId: int.parse(data['notification_type']));
// //         navigatorKey.currentState?.pushNamed(routeOrderDetails);
// //       }
// //     }
// //   }
// // }
// void handleNotificationClick(NotificationResponse response) async {
//   log('Notification clicked');
//   if (response.payload != null) {
//     try {
//       log('Notification payload: ${response.payload}');
//       final Map<String, dynamic> data = jsonDecode(response.payload!);
//       if (data.containsKey('notification_type') && data['notification_type'] != null) {
//         log('Notification type: ${data['notification_type']}');
//         final notificationType = data['notification_type'];
//         if (int.tryParse(notificationType) != null) {
//             log('Notification type: $notificationType');
//         } else {
//           log('Invalid notification type: $notificationType');
//         }
//       } else {
//         log('Notification payload does not contain notification_type');
//       }
//     } catch (e) {
//       log('Error parsing notification payload: $e');
//     }
//   } else {
//     log('Notification payload is null');
//   }
// }

// class FirebaseUtils {
//   FirebaseUtils._();

//   static final FirebaseUtils _instance = FirebaseUtils._();

//   factory FirebaseUtils() => _instance;

//   final FirebaseMessaging _fcm = FirebaseMessaging.instance;
//   final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

//   Future<void> initialize() async {
//     try {
//       await _fcm.requestPermission();

//       const AndroidInitializationSettings initializationSettingsAndroid =
//           AndroidInitializationSettings('@mipmap/ic_launcher');

//       const DarwinInitializationSettings initializationSettingsDarwin = DarwinInitializationSettings(
//           requestSoundPermission: true,
//           requestBadgePermission: true,
//           requestAlertPermission: true,

//           // onDidReceiveLocalNotification: (int id, String? title, String? body, String? payload) async {
//           //   if (payload != null) {
//           //     handleNotificationClick(NotificationResponse(
//           //         payload: payload, notificationResponseType: NotificationResponseType.selectedNotification));
//           //   }
//           // }
//           );

//       const InitializationSettings initializationSettings = InitializationSettings(
//         android: initializationSettingsAndroid,
//         iOS: initializationSettingsDarwin,
//         macOS: initializationSettingsDarwin,
//       );

//       await flutterLocalNotificationsPlugin.initialize(
//         initializationSettings,
//         onDidReceiveBackgroundNotificationResponse: handleNotificationClick,
//         onDidReceiveNotificationResponse: handleNotificationClick,
//       );

//       const AndroidNotificationChannel channel = AndroidNotificationChannel(
//         'default_channel_id', // id
//         'Default Channel', // name
//         description: 'This is the default notification channel', // description
//         importance: Importance.high,
//       );

//       await flutterLocalNotificationsPlugin
//           .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
//           ?.createNotificationChannel(channel);

//       FirebaseMessaging.onMessage.listen((RemoteMessage message) {
//         log('Got a message whilst in the foreground!', name: 'FCM');
//         log('Message data: ${message.data}', name: 'FCM');

//         if (message.notification != null) {
//           log('Notification: =-=-=- =- =- =- =- =- =- ${message.notification?.toMap()}', name: 'FCM');
//           _showNotification(
//             message.notification?.title ?? '',
//             message.notification?.body ?? '',
//             message.data,
//           );
//         }
//       });

//       FirebaseMessaging.onBackgroundMessage(fcmBackgroundHandler);

//       await getToken;
//     } catch (e) {
//       log('Error initializing Firebase: $e');
//     }
//   }

//   Future<void> handleInitialNotification() async {
//     final NotificationAppLaunchDetails? notificationAppLaunchDetails =
//         await flutterLocalNotificationsPlugin.getNotificationAppLaunchDetails();
//     bool didNotificationLaunchApp = notificationAppLaunchDetails?.didNotificationLaunchApp ?? false;
//     if (didNotificationLaunchApp) {
//       final payload = notificationAppLaunchDetails?.notificationResponse?.payload;
//       if (payload != null) {
//         try {
//           final Map<String, dynamic> data = jsonDecode(payload);
//           if (data.containsKey('notification_type') && data['notification_type'] != null) {
//             final notificationType = data['notification_type'].toString();
//             if (notificationType == 'tencent') {
//             } else {
//               print('Invalid order ID in notification payload');
//             }
//           } else {
//             print('Notification payload does not contain notification_type');
//           }
//         } catch (e) {
//           print('Error parsing notification payload: $e');
//         }
//       } else {
//         print('Notification payload is null');
//       }
//     }
//   }

//   Future<void> _showNotification(String title, String body, Map<String, dynamic> data) async {
//     const AndroidNotificationDetails androidPlatformChannelSpecifics = AndroidNotificationDetails(
//       'default_channel_id', // Use the same channel id
//       'Default Channel',
//       importance: Importance.max,
//       priority: Priority.high,
//       icon: '@mipmap/ic_launcher',
//       showWhen: false,
//     );

//     const DarwinNotificationDetails darwinPlatformChannelSpecifics = DarwinNotificationDetails();

//     const NotificationDetails platformChannelSpecifics = NotificationDetails(
//       android: androidPlatformChannelSpecifics,
//       iOS: darwinPlatformChannelSpecifics,
//     );

//     await flutterLocalNotificationsPlugin.show(
//       0,
//       title,
//       body,
//       platformChannelSpecifics,
//       payload: jsonEncode(data), // Pass the data as payload
//     );
//   }

//   Future<String?> get getToken async {
//     String? token = await _fcm.getToken();
//     print('FCM Token: $token');
//     return token;
//   }

//   Future<void> get deleteToken async {
//     await _fcm.deleteToken();
//   }
// }
