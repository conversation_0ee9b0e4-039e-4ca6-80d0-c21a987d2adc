import 'dart:convert';

User userFromJson(String str) => User.fromJson(json.decode(str));

String userToJson(User data) => json.encode(data.toJson());

class User {
  bool? auth;
  String? email;
  int? level;
  String? username;

  User({
    this.auth,
    this.email,
    this.level,
    this.username,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
        auth: json["auth"],
        email: json["email"],
        level: json["level"],
        username: json["username"],
      );

  Map<String, dynamic> toJson() => {
        "auth": auth,
        "email": email,
        "level": level,
        "username": username,
      };

  User copyWith({
    bool? auth,
    String? email,
    int? level,
    String? username,
  }) {
    return User(
      auth: auth ?? this.auth,
      email: email ?? this.email,
      level: level ?? this.level,
      username: username ?? this.username,
    );
  }
}
