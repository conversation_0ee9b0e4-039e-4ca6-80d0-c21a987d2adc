import '../../services/app_update/app_download_service.dart';

class DownloadProgress {
  final double progress;
  final DownloadStatus status;
  final String? error;
  final String? filePath;

  const DownloadProgress({
    required this.progress,
    required this.status,
    this.error,
    this.filePath,
  });

  DownloadProgress copyWith({
    double? progress,
    DownloadStatus? status,
    String? error,
    String? filePath,
  }) {
    return DownloadProgress(
      progress: progress ?? this.progress,
      status: status ?? this.status,
      error: error ?? this.error,
      filePath: filePath ?? this.filePath,
    );
  }
}