import '../../../features/wallet/deposit/domain/models/wallet_coin/wallet_coin.dart';
import '../../constants/assets.dart';
import '../../constants/enums.dart';

class PaymentTypeModel {
  final String code;
  final String name;
  final PaymentType networkType;
  final String icon;
  final int? id;

  const PaymentTypeModel({
    required this.code,
    required this.name,
    required this.networkType,
    required this.icon,
     this.id,
  });

  /// Creates PaymentTypeModel from API wallet coin data (for SIS flavor)
  factory PaymentTypeModel.fromWalletCoin(WalletCoinData walletCoin) {
    final code = walletCoin.code ?? '';
    final networkType =
        code.contains('ERC20') ? PaymentType.ERC20 : PaymentType.TRC20;

    return PaymentTypeModel(
      code: code,
      name: walletCoin.name ?? '',
      networkType: networkType,
      icon: _getIconForNetworkType(networkType),
      id: walletCoin.id,
    );
  }

  /// Creates PaymentTypeModel from static configuration for non-SIS flavors
  /// Used when creating payment types for all other flavors that don't use the API
  factory PaymentTypeModel.fromStatic({
    required String code,
    required String name,
    required PaymentType networkType,
    int? id,
  }) {
    return PaymentTypeModel(
      code: code,
      name: name,
      networkType: networkType,
      icon: _getIconForNetworkType(networkType),
      id: id,
    );
  }

  /// Gets the icon asset path based on network type
  static String _getIconForNetworkType(PaymentType networkType) =>
      switch (networkType) {
        PaymentType.ERC20 => Assets.ercIcon,
        PaymentType.TRC20 => Assets.trcIcon,
      };

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentTypeModel &&
          runtimeType == other.runtimeType &&
          code == other.code &&
          id == other.id;

  @override
  int get hashCode => Object.hash(code, id);

  @override
  String toString() =>
      'PaymentTypeConfig(code: $code, name: $name, networkType: $networkType)';
}
