import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'phone_country.freezed.dart';
part 'phone_country.g.dart';

List<PhoneCountry> phoneCountryFromJson(String str) => 
    List<PhoneCountry>.from(json.decode(str).map((x) => PhoneCountry.fromJson(x)));

String phoneCountryToJson(List<PhoneCountry> data) => 
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

@freezed
class PhoneCountry with _$PhoneCountry {
  const factory PhoneCountry({
    required String name,
    required String code,
    required String phoneCode,
    required String flagEmoji,
  }) = _PhoneCountry;

  factory PhoneCountry.fromJson(Map<String, dynamic> json) => _$PhoneCountryFromJson(json);
}

// Extension to provide compatibility with the old CountryCode from package
extension PhoneCountryExtension on PhoneCountry {
  // For compatibility with existing code that expects dialCode
  String get dialCode => phoneCode;
  
  // For compatibility with existing code that expects isoCode
  String get isoCode => code;
  
  // For compatibility with existing code that expects flagUri
  String get flagUri => 'emoji:$flagEmoji';
}
