// To parse this JSON data, do
//
//     final countryCode = countryCodeFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'country_code.freezed.dart';
part 'country_code.g.dart';

CountryCode countryCodeFromJson(String str) => CountryCode.fromJson(json.decode(str));

String countryCodeToJson(CountryCode data) => json.encode(data.toJson());

@freezed
class CountryCode with _$CountryCode {
    const factory CountryCode({
        required int code,
        required CountryCodeData data,
        required String msg,
    }) = _CountryCode;

    factory CountryCode.fromJson(Map<String, dynamic> json) => _$CountryCodeFromJson(json);
}

@freezed
class CountryCodeData with _$CountryCodeData {
    const factory CountryCodeData({
         String? city,
         String? country,
         String? ip,
         String? isoCode,
         double? latitude,
         double? longitude,
         String? network,
         String? subdivision,
    }) = _CountryCodeData;

    factory CountryCodeData.fromJson(Map<String, dynamic> json) => _$CountryCodeDataFromJson(json);
}
