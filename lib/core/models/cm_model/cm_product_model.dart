import 'package:freezed_annotation/freezed_annotation.dart';

part 'cm_product_model.freezed.dart';
part 'cm_product_model.g.dart';

@freezed
class CMProductModel with _$CMProductModel {
  const factory CMProductModel({
    String? id,
    int? mentorId,
    String? productName,
    String? applicationTime,
    String? mentorName,
    double? price,
    int? commissionRatio,
    int? productId,
  }) = _CMProductModel;

  factory CMProductModel.fromJson(Map<String, dynamic> json) => _$CMProductModelFromJson(json);
}
