import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app_v2/core/shared/logic/chat_button/chat_button_cubit.dart';

import '../routes/navigator.dart';

class ChatButtonObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    if (route is ModalBottomSheetRoute || route is DialogRoute) {
      _updateChatButton(false);
    }
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    if (previousRoute != null) {
      if ((route is ModalBottomSheetRoute || route is DialogRoute)) {
        _updateChatButton(true);
      }
    }
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    if (newRoute != null) {
      if ((newRoute is ModalBottomSheetRoute || newRoute is DialogRoute)) {
        _updateChatButton(false);
      }
    }
  }

  void _updateChatButton(bool value) {
    navigatorKey.currentState?.context
        .read<ChatButtonCubit>()
        .toggleChatButton(showChatButton: value);
  }
}
