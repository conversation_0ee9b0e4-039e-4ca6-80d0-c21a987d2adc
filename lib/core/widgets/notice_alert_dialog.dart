import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';

import '../../features/notification/domain/models/notification_list/notification_model.dart';

class NoticeAlertDialog extends StatefulWidget {
  const NoticeAlertDialog({
    super.key,
    required this.notification,
    this.onOkPressed,
    this.onClosePressed,
  });

  final List<NotificationData>? notification;
  final VoidCallback? onOkPressed;
  final VoidCallback? onClosePressed;

  @override
  State<NoticeAlertDialog> createState() => _NoticeAlertDialogState();
}

class _NoticeAlertDialogState extends State<NoticeAlertDialog> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final notifications = widget.notification ?? [];

    if (notifications.isEmpty) {
      return const SizedBox.shrink();
    }

    return BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
        child: AlertDialog(
          backgroundColor: Colors.transparent,
          contentPadding: EdgeInsets.zero,
          insetPadding: EdgeInsets.symmetric(horizontal: 24.w),
          content: Container(
            width: double.maxFinite,
            padding: EdgeInsets.all(24.w),
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20.r,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: IntrinsicHeight(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with close button
                  Text(
                    notifications[_currentIndex].title ?? 'Notice',
                    style: FontPalette.bold20.copyWith(
                      color: Colors.black87,
                    ),
                  ),

                  SizedBox(height: 20.h),

                  // Content with PageView for swipe functionality
                  SizedBox(
                    height: 300.h,
                    child: PageView.builder(
                      controller: _pageController,
                      itemCount: notifications.length,
                      onPageChanged: (index) =>
                          setState(() => _currentIndex = index),
                      itemBuilder: (context, index) {
                        final notification = notifications[index];
                        return Scrollbar(
                          thumbVisibility: true,
                          thickness: 4.w,
                          radius: Radius.circular(2.r),
                          child: SingleChildScrollView(
                            padding: EdgeInsets.only(right: 8.w),
                            child: HtmlWidget(
                              notification.content ?? 'No content available',
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  SizedBox(height: 16.h),

                  // Dot indicators (only show if more than one notification)
                  if (notifications.length > 1) _buildDotIndicators(),

                  SizedBox(height: notifications.length > 1 ? 20.h : 30.h),

                  // Action buttons
                  CustomButton(
                    onPressed:
                        widget.onOkPressed ?? () => Navigator.of(context).pop(),
                    label: 'OK',
                    btnTextStyle: FontPalette.medium16.copyWith(
                      color: Colors.white,
                    ),
                    backgroundColor: Theme.of(context).primaryColor,
                    width: double.infinity,
                    height: 48.h,
                  ),
                ],
              ),
            ),
          ),
        ));
  }

  /// Builds the dot indicators for multiple notifications
  Widget _buildDotIndicators() {
    final notifications = widget.notification ?? [];

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        notifications.length,
        (index) => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: _currentIndex == index ? 12.w : 8.w,
          height: 8.h,
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _currentIndex == index
                ? Theme.of(context).primaryColor
                : Colors.grey.shade300,
          ),
        ),
      ),
    );
  }
}
