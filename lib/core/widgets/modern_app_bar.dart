import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';

enum ModernAppBarStyle {
  solid,
  glass,
  transparent,
  gradient,
}

class ModernAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final bool centerTitle;
  final double elevation;
  final ModernAppBarStyle style;
  final List<Color>? gradientColors;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final VoidCallback? onBackPressed;
  final PreferredSizeWidget? bottom;
  final double toolbarHeight;
  final bool useBlur;

  const ModernAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.centerTitle = true,
    this.elevation = 0,
    this.style = ModernAppBarStyle.solid,
    this.gradientColors,
    this.backgroundColor,
    this.foregroundColor,
    this.onBackPressed,
    this.bottom,
    this.toolbarHeight = kToolbarHeight,
    this.useBlur = false,
  });

  @override
  Size get preferredSize => Size.fromHeight(
    toolbarHeight + (bottom?.preferredSize.height ?? 0),
  );

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      decoration: _buildDecoration(isDark),
      child: _buildContent(context, isDark),
    );
  }

  BoxDecoration _buildDecoration(bool isDark) {
    switch (style) {
      case ModernAppBarStyle.solid:
        return BoxDecoration(
          color: backgroundColor ?? 
              (isDark ? ColorPalette.cardColorDark : ColorPalette.cardColor),
          boxShadow: elevation > 0 ? [
            BoxShadow(
              color: isDark ? Colors.black.withValues(alpha: 0.3) : Colors.black.withValues(alpha: 0.1),
              blurRadius: elevation,
              offset: Offset(0, elevation / 2),
            ),
          ] : null,
        );
      case ModernAppBarStyle.glass:
        return BoxDecoration(
          color: (backgroundColor ?? 
              (isDark ? ColorPalette.glassColorDark : ColorPalette.glassColor)),
          border: Border(
            bottom: BorderSide(
              color: isDark ? ColorPalette.glassBorderDark : ColorPalette.glassBorder,
              width: 1,
            ),
          ),
        );
      case ModernAppBarStyle.transparent:
        return const BoxDecoration(
          color: Colors.transparent,
        );
      case ModernAppBarStyle.gradient:
        final colors = gradientColors ?? 
            (isDark ? ColorPalette.primaryGradientDark : ColorPalette.primaryGradient);
        return BoxDecoration(
          gradient: LinearGradient(
            colors: colors,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: elevation > 0 ? [
            BoxShadow(
              color: colors.first.withValues(alpha: 0.3),
              blurRadius: elevation,
              offset: Offset(0, elevation / 2),
            ),
          ] : null,
        );
    }
  }

  Widget _buildContent(BuildContext context, bool isDark) {
    Widget content = AppBar(
      title: titleWidget ?? (title != null ? Text(
        title!,
        style: FontPalette.semiBold18.copyWith(
          color: _getTitleColor(isDark),
        ),
      ) : null),
      leading: leading ?? (automaticallyImplyLeading ? _buildLeading(context, isDark) : null),
      actions: actions,
      centerTitle: centerTitle,
      backgroundColor: Colors.transparent,
      foregroundColor: foregroundColor ?? _getForegroundColor(isDark),
      elevation: 0,
      systemOverlayStyle: _getSystemOverlayStyle(isDark),
      automaticallyImplyLeading: false,
      toolbarHeight: toolbarHeight,
      bottom: bottom,
    );

    if (style == ModernAppBarStyle.glass && useBlur) {
      content = ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: content,
        ),
      );
    }

    return content;
  }

  Widget? _buildLeading(BuildContext context, bool isDark) {
    if (!Navigator.of(context).canPop()) return null;
    
    return IconButton(
      onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      icon: Icon(
        Icons.arrow_back_ios_new_rounded,
        color: _getIconColor(isDark),
        size: 20.sp,
      ),
      style: IconButton.styleFrom(
        backgroundColor: _getLeadingBackgroundColor(isDark),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
      ),
    );
  }

  Color _getTitleColor(bool isDark) {
    if (style == ModernAppBarStyle.gradient) {
      return ColorPalette.white;
    }
    return foregroundColor ?? 
        (isDark ? ColorPalette.titleColorDark : ColorPalette.titleColor);
  }

  Color _getForegroundColor(bool isDark) {
    if (style == ModernAppBarStyle.gradient) {
      return ColorPalette.white;
    }
    return foregroundColor ?? 
        (isDark ? ColorPalette.titleColorDark : ColorPalette.titleColor);
  }

  Color _getIconColor(bool isDark) {
    if (style == ModernAppBarStyle.gradient) {
      return ColorPalette.white;
    }
    return isDark ? ColorPalette.iconColorDark : ColorPalette.iconColor;
  }

  Color _getLeadingBackgroundColor(bool isDark) {
    if (style == ModernAppBarStyle.gradient || style == ModernAppBarStyle.transparent) {
      return Colors.white.withValues(alpha: 0.2);
    }
    return isDark ? ColorPalette.greyColor1Dark : ColorPalette.greyColor1;
  }

  SystemUiOverlayStyle _getSystemOverlayStyle(bool isDark) {
    if (style == ModernAppBarStyle.gradient) {
      return SystemUiOverlayStyle.light;
    }
    return isDark ? SystemUiOverlayStyle.light : SystemUiOverlayStyle.dark;
  }
}

// Convenience constructors for common app bar types
class GlassAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final bool centerTitle;
  final VoidCallback? onBackPressed;
  final double toolbarHeight;

  const GlassAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.centerTitle = true,
    this.onBackPressed,
    this.toolbarHeight = kToolbarHeight,
  });

  @override
  Size get preferredSize => Size.fromHeight(toolbarHeight);

  @override
  Widget build(BuildContext context) {
    return ModernAppBar(
      title: title,
      titleWidget: titleWidget,
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      centerTitle: centerTitle,
      style: ModernAppBarStyle.glass,
      onBackPressed: onBackPressed,
      toolbarHeight: toolbarHeight,
      useBlur: true,
    );
  }
}

class GradientAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final bool centerTitle;
  final List<Color>? gradientColors;
  final VoidCallback? onBackPressed;
  final double toolbarHeight;
  final double elevation;

  const GradientAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.centerTitle = true,
    this.gradientColors,
    this.onBackPressed,
    this.toolbarHeight = kToolbarHeight,
    this.elevation = 8,
  });

  @override
  Size get preferredSize => Size.fromHeight(toolbarHeight);

  @override
  Widget build(BuildContext context) {
    return ModernAppBar(
      title: title,
      titleWidget: titleWidget,
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      centerTitle: centerTitle,
      style: ModernAppBarStyle.gradient,
      gradientColors: gradientColors,
      onBackPressed: onBackPressed,
      toolbarHeight: toolbarHeight,
      elevation: elevation,
    );
  }
}
