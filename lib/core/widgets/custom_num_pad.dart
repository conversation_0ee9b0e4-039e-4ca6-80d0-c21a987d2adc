import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/constants/app_constants.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

class NumPad extends StatefulWidget {
  final double width;
  final double? numHeight;
  final TextEditingController controller;
  final Function delete;
  final Function onSubmit;
  final Function? onChanged;
  final bool isEnabled;

  const NumPad({
    super.key,
    required this.width,
    required this.delete,
    required this.onSubmit,
    this.onChanged,
    required this.controller,
    this.numHeight,
    this.isEnabled = true,
  });

  @override
  State<NumPad> createState() => _NumPadState();
}

class _NumPadState extends State<NumPad> {
  var _previous = '0'.toCurrencyWithSymbol();

  @override
  void initState() {
    super.initState();
    final RegExp regexNumber = RegExp(r'^(?!0)\d{1,10}(\.\d{0,2})?$');
    widget.controller.addListener(() {
      var currentText = widget.controller.text;

      var amount = currentText.replaceAll(AppConstants.currencySymbol, '').trim();
      amount = amount.replaceAll(RegExp(r'^0+(?=.)'), '');

      if (regexNumber.hasMatch(amount)) {
        var newText = amount.toCurrencyWithSymbol();
        if (currentText != newText) {
          widget.controller.value = widget.controller.value.copyWith(
            text: newText,
            selection: TextSelection.collapsed(offset: newText.length),
          );
        }
        _previous = newText;
      } else {
        if (amount.isEmpty || amount == '0') {
          var newText = '0'.toCurrencyWithSymbol();
          if (currentText != newText) {
            widget.controller.value = widget.controller.value.copyWith(
              text: newText,
              selection: TextSelection.collapsed(offset: newText.length),
            );
          }
          _previous = newText;
        } else {
          if (currentText != _previous) {
            widget.controller.value = widget.controller.value.copyWith(
              text: _previous,
              selection: TextSelection.collapsed(offset: _previous.length),
            );
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      child: Column(
        children: [
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              NumberButton(
                onChanged: widget.onChanged,
                width: widget.width / 3,
                height: widget.numHeight ?? 60.h,
                number: 1,
                controller: widget.controller,
                isEnabled: widget.isEnabled,
              ),
              NumberButton(
                onChanged: widget.onChanged,
                width: widget.width / 3,
                height: widget.numHeight ?? 60.h,
                number: 2,
                controller: widget.controller,
                isEnabled: widget.isEnabled,
              ),
              NumberButton(
                onChanged: widget.onChanged,
                width: widget.width / 3,
                height: widget.numHeight ?? 60.h,
                number: 3,
                controller: widget.controller,
                isEnabled: widget.isEnabled,
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              NumberButton(
                onChanged: widget.onChanged,
                width: widget.width / 3,
                height: widget.numHeight ?? 60.h,
                number: 4,
                controller: widget.controller,
                isEnabled: widget.isEnabled,
              ),
              NumberButton(
                onChanged: widget.onChanged,
                width: widget.width / 3,
                height: widget.numHeight ?? 60.h,
                number: 5,
                controller: widget.controller,
                isEnabled: widget.isEnabled,
              ),
              NumberButton(
                onChanged: widget.onChanged,
                width: widget.width / 3,
                height: widget.numHeight ?? 60.h,
                number: 6,
                controller: widget.controller,
                isEnabled: widget.isEnabled,
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              NumberButton(
                onChanged: widget.onChanged,
                width: widget.width / 3,
                height: widget.numHeight ?? 60.h,
                number: 7,
                controller: widget.controller,
                isEnabled: widget.isEnabled,
              ),
              NumberButton(
                onChanged: widget.onChanged,
                width: widget.width / 3,
                height: widget.numHeight ?? 60.h,
                number: 8,
                controller: widget.controller,
                isEnabled: widget.isEnabled,
              ),
              NumberButton(
                onChanged: widget.onChanged,
                width: widget.width / 3,
                height: widget.numHeight ?? 60.h,
                number: 9,
                controller: widget.controller,
                isEnabled: widget.isEnabled,
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              DecimalButton(
                numHeight: widget.numHeight ?? 60.h,
                number: ".",
                controller: widget.controller,
                isEnabled: widget.isEnabled,
              ),
              NumberButton(
                onChanged: widget.onChanged,
                width: widget.width / 3,
                height: widget.numHeight ?? 60.h,
                number: 0,
                controller: widget.controller,
                isEnabled: widget.isEnabled,
              ),
              InkWell(
                onTap: widget.isEnabled
                    ? () {
                        widget.delete();
                      }
                    : null,
                child: SizedBox(
                  height: widget.numHeight,
                  width: 120,
                  child: Icon(
                    Icons.backspace,
                    color: myColorScheme(context).titleColor,
                    size: 30,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class NumberButton extends StatelessWidget {
  final int number;
  final double width;
  final double height;
  final Function? onChanged;
  final TextEditingController controller;
  final bool isEnabled;

  const NumberButton({
    super.key,
    required this.number,
    required this.width,
    required this.height,
    this.onChanged,
    required this.controller,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          side: BorderSide(
            width: 1.r,
            color: myColorScheme(context).borderColor ?? Colors.grey,
          ),
          backgroundColor: myColorScheme(context).backgroundColor,
          disabledBackgroundColor: myColorScheme(context).greyColor1,
          elevation: 0,
        ),
        onPressed: isEnabled
            ? () {
                controller.text += number.toString();
                if (onChanged != null) onChanged!();
              }
            : null,
        child: Center(
          child: Text(
            number.toString(),
            style: FontPalette.semiBold25.copyWith(
              color: isEnabled
                  ? myColorScheme(context).titleColor ?? Colors.black
                  : myColorScheme(context).white,
            ),
          ),
        ),
      ),
    );
  }
}

class DecimalButton extends StatelessWidget {
  final String number;
  final double numHeight;
  final TextEditingController controller;
  final bool isEnabled;

  const DecimalButton({
    super.key,
    required this.number,
    required this.controller,
    required this.numHeight,
    this.isEnabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 120,
      height: numHeight,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          elevation: 0,
          disabledBackgroundColor: Colors.transparent,
        ),
        onPressed: isEnabled
            ? () {
                controller.text += number.toString();
              }
            : null,
        child: Center(
          child: Text(
            number.toString(),
            style:
                FontPalette.semiBold25.copyWith(color: myColorScheme(context).titleColor),
          ),
        ),
      ),
    );
  }
}
