import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';

enum ModernTextFieldStyle {
  filled,
  outlined,
  underlined,
  glass,
}

class ModernTextField extends StatefulWidget {
  final String? labelText;
  final String? hintText;
  final String? helperText;
  final String? errorText;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final List<TextInputFormatter>? inputFormatters;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final VoidCallback? onTap;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final FormFieldValidator<String>? validator;
  final ModernTextFieldStyle style;
  final double borderRadius;
  final EdgeInsets? contentPadding;
  final TextStyle? textStyle;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final Color? fillColor;
  final Color? borderColor;
  final bool autofocus;

  const ModernTextField({
    super.key,
    this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.controller,
    this.focusNode,
    this.keyboardType,
    this.textInputAction,
    this.inputFormatters,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.prefixIcon,
    this.suffixIcon,
    this.onTap,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.style = ModernTextFieldStyle.filled,
    this.borderRadius = 12,
    this.contentPadding,
    this.textStyle,
    this.labelStyle,
    this.hintStyle,
    this.fillColor,
    this.borderColor,
    this.autofocus = false,
  });

  @override
  State<ModernTextField> createState() => _ModernTextFieldState();
}

class _ModernTextFieldState extends State<ModernTextField>
    with SingleTickerProviderStateMixin {
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _borderAnimation;
  late Animation<Color?> _colorAnimation;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _borderAnimation = Tween<double>(
      begin: 1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _focusNode.addListener(_handleFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  void _handleFocusChange() {
    if (_focusNode.hasFocus != _isFocused) {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
      
      if (_isFocused) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    _colorAnimation = ColorTween(
      begin: widget.borderColor ?? 
          (isDark ? ColorPalette.borderColorDark : ColorPalette.borderColor),
      end: isDark ? ColorPalette.primaryColorDark : ColorPalette.primaryColor,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return TextFormField(
          controller: widget.controller,
          focusNode: _focusNode,
          keyboardType: widget.keyboardType,
          textInputAction: widget.textInputAction,
          inputFormatters: widget.inputFormatters,
          obscureText: widget.obscureText,
          enabled: widget.enabled,
          readOnly: widget.readOnly,
          maxLines: widget.maxLines,
          minLines: widget.minLines,
          maxLength: widget.maxLength,
          onTap: widget.onTap,
          onChanged: widget.onChanged,
          onFieldSubmitted: widget.onSubmitted,
          validator: widget.validator,
          autofocus: widget.autofocus,
          style: widget.textStyle ?? FontPalette.normal16.copyWith(
            color: isDark ? ColorPalette.titleColorDark : ColorPalette.titleColor,
          ),
          decoration: _buildInputDecoration(isDark),
        );
      },
    );
  }

  InputDecoration _buildInputDecoration(bool isDark) {
    switch (widget.style) {
      case ModernTextFieldStyle.filled:
        return _buildFilledDecoration(isDark);
      case ModernTextFieldStyle.outlined:
        return _buildOutlinedDecoration(isDark);
      case ModernTextFieldStyle.underlined:
        return _buildUnderlinedDecoration(isDark);
      case ModernTextFieldStyle.glass:
        return _buildGlassDecoration(isDark);
    }
  }

  InputDecoration _buildFilledDecoration(bool isDark) {
    return InputDecoration(
      labelText: widget.labelText,
      hintText: widget.hintText,
      helperText: widget.helperText,
      errorText: widget.errorText,
      prefixIcon: widget.prefixIcon,
      suffixIcon: widget.suffixIcon,
      filled: true,
      fillColor: widget.fillColor ?? 
          (isDark ? ColorPalette.cardColorDark : ColorPalette.cardColor),
      contentPadding: widget.contentPadding ?? 
          EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      labelStyle: widget.labelStyle ?? FontPalette.normal14.copyWith(
        color: isDark ? ColorPalette.labelColorDark : ColorPalette.labelColor,
      ),
      hintStyle: widget.hintStyle ?? FontPalette.normal14.copyWith(
        color: isDark ? ColorPalette.greyColor4Dark : ColorPalette.greyColor4,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide(
          color: _colorAnimation.value ?? ColorPalette.primaryColor,
          width: _borderAnimation.value,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide(
          color: isDark ? ColorPalette.errorColorDark : ColorPalette.errorColor,
          width: 1,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide(
          color: isDark ? ColorPalette.errorColorDark : ColorPalette.errorColor,
          width: 2,
        ),
      ),
    );
  }

  InputDecoration _buildOutlinedDecoration(bool isDark) {
    return InputDecoration(
      labelText: widget.labelText,
      hintText: widget.hintText,
      helperText: widget.helperText,
      errorText: widget.errorText,
      prefixIcon: widget.prefixIcon,
      suffixIcon: widget.suffixIcon,
      contentPadding: widget.contentPadding ?? 
          EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      labelStyle: widget.labelStyle ?? FontPalette.normal14.copyWith(
        color: isDark ? ColorPalette.labelColorDark : ColorPalette.labelColor,
      ),
      hintStyle: widget.hintStyle ?? FontPalette.normal14.copyWith(
        color: isDark ? ColorPalette.greyColor4Dark : ColorPalette.greyColor4,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide(
          color: isDark ? ColorPalette.borderColorDark : ColorPalette.borderColor,
          width: 1,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide(
          color: isDark ? ColorPalette.borderColorDark : ColorPalette.borderColor,
          width: 1,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide(
          color: _colorAnimation.value ?? ColorPalette.primaryColor,
          width: _borderAnimation.value,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide(
          color: isDark ? ColorPalette.errorColorDark : ColorPalette.errorColor,
          width: 1,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide(
          color: isDark ? ColorPalette.errorColorDark : ColorPalette.errorColor,
          width: 2,
        ),
      ),
    );
  }

  InputDecoration _buildUnderlinedDecoration(bool isDark) {
    return InputDecoration(
      labelText: widget.labelText,
      hintText: widget.hintText,
      helperText: widget.helperText,
      errorText: widget.errorText,
      prefixIcon: widget.prefixIcon,
      suffixIcon: widget.suffixIcon,
      contentPadding: widget.contentPadding ?? 
          EdgeInsets.symmetric(horizontal: 0, vertical: 16.h),
      labelStyle: widget.labelStyle ?? FontPalette.normal14.copyWith(
        color: isDark ? ColorPalette.labelColorDark : ColorPalette.labelColor,
      ),
      hintStyle: widget.hintStyle ?? FontPalette.normal14.copyWith(
        color: isDark ? ColorPalette.greyColor4Dark : ColorPalette.greyColor4,
      ),
      border: UnderlineInputBorder(
        borderSide: BorderSide(
          color: isDark ? ColorPalette.borderColorDark : ColorPalette.borderColor,
          width: 1,
        ),
      ),
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: isDark ? ColorPalette.borderColorDark : ColorPalette.borderColor,
          width: 1,
        ),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: _colorAnimation.value ?? ColorPalette.primaryColor,
          width: _borderAnimation.value,
        ),
      ),
      errorBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: isDark ? ColorPalette.errorColorDark : ColorPalette.errorColor,
          width: 1,
        ),
      ),
      focusedErrorBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: isDark ? ColorPalette.errorColorDark : ColorPalette.errorColor,
          width: 2,
        ),
      ),
    );
  }

  InputDecoration _buildGlassDecoration(bool isDark) {
    return InputDecoration(
      labelText: widget.labelText,
      hintText: widget.hintText,
      helperText: widget.helperText,
      errorText: widget.errorText,
      prefixIcon: widget.prefixIcon,
      suffixIcon: widget.suffixIcon,
      filled: true,
      fillColor: widget.fillColor ?? 
          (isDark ? ColorPalette.glassColorDark : ColorPalette.glassColor),
      contentPadding: widget.contentPadding ?? 
          EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      labelStyle: widget.labelStyle ?? FontPalette.normal14.copyWith(
        color: isDark ? ColorPalette.labelColorDark : ColorPalette.labelColor,
      ),
      hintStyle: widget.hintStyle ?? FontPalette.normal14.copyWith(
        color: isDark ? ColorPalette.greyColor4Dark : ColorPalette.greyColor4,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide(
          color: isDark ? ColorPalette.glassBorderDark : ColorPalette.glassBorder,
          width: 1,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide(
          color: isDark ? ColorPalette.glassBorderDark : ColorPalette.glassBorder,
          width: 1,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide(
          color: _colorAnimation.value ?? ColorPalette.primaryColor,
          width: _borderAnimation.value,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide(
          color: isDark ? ColorPalette.errorColorDark : ColorPalette.errorColor,
          width: 1,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
        borderSide: BorderSide(
          color: isDark ? ColorPalette.errorColorDark : ColorPalette.errorColor,
          width: 2,
        ),
      ),
    );
  }
}
