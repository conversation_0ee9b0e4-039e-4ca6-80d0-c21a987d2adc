import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';

class ModernBottomNavItem {
  final IconData icon;
  final IconData? activeIcon;
  final String label;
  final bool requiresAuth;

  const ModernBottomNavItem({
    required this.icon,
    this.activeIcon,
    required this.label,
    this.requiresAuth = false,
  });
}

class ModernBottomNavBar extends StatelessWidget {
  final List<ModernBottomNavItem> items;
  final int currentIndex;
  final ValueChanged<int> onTap;
  final bool useGlassEffect;
  final double height;

  const ModernBottomNavBar({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    this.useGlassEffect = true,
    this.height = 100,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      height: height.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
        boxShadow: [
          BoxShadow(
            color: isDark ? Colors.black.withValues(alpha: 0.3) : Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
        child: useGlassEffect
            ? _buildGlassNavBar(context, isDark)
            : _buildSolidNavBar(context, isDark),
      ),
    );
  }

  Widget _buildGlassNavBar(BuildContext context, bool isDark) {
    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
      child: Container(
        decoration: BoxDecoration(
          color: isDark 
              ? ColorPalette.glassColorDark
              : ColorPalette.glassColor,
          border: Border(
            top: BorderSide(
              color: isDark 
                  ? ColorPalette.glassBorderDark
                  : ColorPalette.glassBorder,
              width: 1,
            ),
          ),
        ),
        child: _buildNavContent(context, isDark),
      ),
    );
  }

  Widget _buildSolidNavBar(BuildContext context, bool isDark) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? ColorPalette.cardColorDark : ColorPalette.cardColor,
        border: Border(
          top: BorderSide(
            color: isDark ? ColorPalette.borderColorDark : ColorPalette.borderColor,
            width: 1,
          ),
        ),
      ),
      child: _buildNavContent(context, isDark),
    );
  }

  Widget _buildNavContent(BuildContext context, bool isDark) {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: List.generate(
            items.length,
            (index) => _buildNavItem(context, index, isDark),
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(BuildContext context, int index, bool isDark) {
    final item = items[index];
    final isSelected = currentIndex == index;
    
    return Expanded(
      child: GestureDetector(
        onTap: () => onTap(index),
        behavior: HitTestBehavior.opaque,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildIcon(item, isSelected, isDark),
              SizedBox(height: 4.h),
              _buildLabel(item, isSelected, isDark),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIcon(ModernBottomNavItem item, bool isSelected, bool isDark) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      padding: EdgeInsets.all(isSelected ? 8.r : 6.r),
      decoration: BoxDecoration(
        color: isSelected
            ? (isDark ? ColorPalette.primaryColorDark : ColorPalette.primaryColor)
                .withValues(alpha: 0.15)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
        child: Icon(
          isSelected ? (item.activeIcon ?? item.icon) : item.icon,
          key: ValueKey(isSelected),
          size: 24.sp,
          color: isSelected
              ? (isDark ? ColorPalette.primaryColorDark : ColorPalette.primaryColor)
              : (isDark ? ColorPalette.greyColor4Dark : ColorPalette.greyColor4),
        ),
      ),
    );
  }

  Widget _buildLabel(ModernBottomNavItem item, bool isSelected, bool isDark) {
    return AnimatedDefaultTextStyle(
      duration: const Duration(milliseconds: 300),
      style: FontPalette.medium10.copyWith(
        color: isSelected
            ? (isDark ? ColorPalette.primaryColorDark : ColorPalette.primaryColor)
            : (isDark ? ColorPalette.greyColor4Dark : ColorPalette.greyColor4),
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
      ),
      child: Text(
        item.label,
        textAlign: TextAlign.center,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}

// Alternative floating action button style nav bar
class ModernFloatingNavBar extends StatelessWidget {
  final List<ModernBottomNavItem> items;
  final int currentIndex;
  final ValueChanged<int> onTap;
  final EdgeInsets margin;

  const ModernFloatingNavBar({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    this.margin = const EdgeInsets.all(16),
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      margin: margin,
      height: 70.h,
      decoration: BoxDecoration(
        color: isDark ? ColorPalette.cardColorDark : ColorPalette.cardColor,
        borderRadius: BorderRadius.circular(35.r),
        boxShadow: [
          BoxShadow(
            color: isDark ? Colors.black.withValues(alpha: 0.3) : Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(35.r),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: isDark 
                  ? ColorPalette.glassColorDark
                  : ColorPalette.glassColor,
              borderRadius: BorderRadius.circular(35.r),
              border: Border.all(
                color: isDark 
                    ? ColorPalette.glassBorderDark
                    : ColorPalette.glassBorder,
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(
                items.length,
                (index) => _buildFloatingNavItem(context, index, isDark),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingNavItem(BuildContext context, int index, bool isDark) {
    final item = items[index];
    final isSelected = currentIndex == index;
    
    return GestureDetector(
      onTap: () => onTap(index),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: isSelected
              ? (isDark ? ColorPalette.primaryColorDark : ColorPalette.primaryColor)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(25.r),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isSelected ? (item.activeIcon ?? item.icon) : item.icon,
              size: 22.sp,
              color: isSelected
                  ? ColorPalette.white
                  : (isDark ? ColorPalette.greyColor4Dark : ColorPalette.greyColor4),
            ),
            if (isSelected) ...[
              SizedBox(width: 8.w),
              Text(
                item.label,
                style: FontPalette.medium12.copyWith(
                  color: ColorPalette.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
