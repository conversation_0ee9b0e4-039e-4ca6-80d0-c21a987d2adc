import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';

enum ModernCardStyle {
  elevated,
  glass,
  neumorphism,
  gradient,
  outlined,
}

class ModernCard extends StatefulWidget {
  final Widget child;
  final ModernCardStyle style;
  final double? width;
  final double? height;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double borderRadius;
  final VoidCallback? onTap;
  final Color? customColor;
  final List<Color>? gradientColors;
  final double elevation;
  final bool isInteractive;

  const ModernCard({
    super.key,
    required this.child,
    this.style = ModernCardStyle.elevated,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius = 16,
    this.onTap,
    this.customColor,
    this.gradientColors,
    this.elevation = 8,
    this.isInteractive = true,
  });

  @override
  State<ModernCard> createState() => _ModernCardState();
}

class _ModernCardState extends State<ModernCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _elevationAnimation = Tween<double>(
      begin: widget.elevation,
      end: widget.elevation * 1.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.isInteractive && widget.onTap != null) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.isInteractive && widget.onTap != null) {
      setState(() => _isPressed = false);
      _animationController.reverse();
      widget.onTap?.call();
    }
  }

  void _handleTapCancel() {
    if (widget.isInteractive && widget.onTap != null) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.isInteractive ? _scaleAnimation.value : 1.0,
          child: Container(
            width: widget.width,
            height: widget.height,
            margin: widget.margin,
            child: GestureDetector(
              onTapDown: _handleTapDown,
              onTapUp: _handleTapUp,
              onTapCancel: _handleTapCancel,
              child: Container(
                decoration: _buildDecoration(isDark),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(widget.borderRadius.r),
                  child: _buildContent(isDark),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  BoxDecoration _buildDecoration(bool isDark) {
    switch (widget.style) {
      case ModernCardStyle.elevated:
        return _buildElevatedDecoration(isDark);
      case ModernCardStyle.glass:
        return _buildGlassDecoration(isDark);
      case ModernCardStyle.neumorphism:
        return _buildNeumorphismDecoration(isDark);
      case ModernCardStyle.gradient:
        return _buildGradientDecoration(isDark);
      case ModernCardStyle.outlined:
        return _buildOutlinedDecoration(isDark);
    }
  }

  BoxDecoration _buildElevatedDecoration(bool isDark) {
    final color = widget.customColor ?? 
        (isDark ? ColorPalette.cardColorDark : ColorPalette.cardColor);
    
    return BoxDecoration(
      color: color,
      borderRadius: BorderRadius.circular(widget.borderRadius.r),
      boxShadow: [
        BoxShadow(
          color: isDark ? ColorPalette.shadowColorDark : ColorPalette.shadowColor,
          blurRadius: widget.isInteractive ? _elevationAnimation.value : widget.elevation,
          offset: Offset(0, (widget.isInteractive ? _elevationAnimation.value : widget.elevation) / 2),
        ),
      ],
    );
  }

  BoxDecoration _buildGlassDecoration(bool isDark) {
    return BoxDecoration(
      color: isDark ? ColorPalette.glassColorDark : ColorPalette.glassColor,
      borderRadius: BorderRadius.circular(widget.borderRadius.r),
      border: Border.all(
        color: isDark ? ColorPalette.glassBorderDark : ColorPalette.glassBorder,
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: (isDark ? Colors.black : Colors.white).withValues(alpha: 0.1),
          blurRadius: 20,
          offset: const Offset(0, 8),
        ),
      ],
    );
  }

  BoxDecoration _buildNeumorphismDecoration(bool isDark) {
    final bgColor = widget.customColor ?? 
        (isDark ? ColorPalette.cardColorDark : ColorPalette.cardColor);
    
    return BoxDecoration(
      color: bgColor,
      borderRadius: BorderRadius.circular(widget.borderRadius.r),
      boxShadow: _isPressed ? [
        BoxShadow(
          color: isDark ? ColorPalette.neumorphismDarkShadowDark : ColorPalette.neumorphismDarkShadow,
          blurRadius: 8,
          offset: const Offset(4, 4),
        ),
      ] : [
        BoxShadow(
          color: isDark ? ColorPalette.neumorphismLightShadowDark : ColorPalette.neumorphismLightShadow,
          blurRadius: 12,
          offset: const Offset(-6, -6),
        ),
        BoxShadow(
          color: isDark ? ColorPalette.neumorphismDarkShadowDark : ColorPalette.neumorphismDarkShadow,
          blurRadius: 12,
          offset: const Offset(6, 6),
        ),
      ],
    );
  }

  BoxDecoration _buildGradientDecoration(bool isDark) {
    final colors = widget.gradientColors ?? 
        (isDark ? ColorPalette.primaryGradientDark : ColorPalette.primaryGradient);
    
    return BoxDecoration(
      gradient: LinearGradient(
        colors: colors,
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(widget.borderRadius.r),
      boxShadow: [
        BoxShadow(
          color: colors.first.withValues(alpha: 0.3),
          blurRadius: widget.elevation,
          offset: Offset(0, widget.elevation / 2),
        ),
      ],
    );
  }

  BoxDecoration _buildOutlinedDecoration(bool isDark) {
    return BoxDecoration(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(widget.borderRadius.r),
      border: Border.all(
        color: isDark ? ColorPalette.borderColorDark : ColorPalette.borderColor,
        width: 1.5,
      ),
    );
  }

  Widget _buildContent(bool isDark) {
    Widget content = Padding(
      padding: widget.padding ?? EdgeInsets.all(16.r),
      child: widget.child,
    );

    // Add backdrop filter for glass effect
    if (widget.style == ModernCardStyle.glass) {
      content = BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: content,
      );
    }

    return content;
  }
}

// Convenience constructors for common card types
class GlassCard extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double borderRadius;
  final VoidCallback? onTap;

  const GlassCard({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius = 16,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      style: ModernCardStyle.glass,
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      borderRadius: borderRadius,
      onTap: onTap,
      child: child,
    );
  }
}

class NeumorphismCard extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double borderRadius;
  final VoidCallback? onTap;

  const NeumorphismCard({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.borderRadius = 16,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      style: ModernCardStyle.neumorphism,
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      borderRadius: borderRadius,
      onTap: onTap,
      child: child,
    );
  }
}
