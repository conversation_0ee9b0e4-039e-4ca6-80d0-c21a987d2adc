import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:shimmer_animation/shimmer_animation.dart';

class CommonShimmer extends StatelessWidget {
  final double? width;
  final double? height;
  final double? br;
  final Color? color;
  
  const CommonShimmer({
    super.key,
    this.height,
    this.br,
    this.color,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular((br ?? 8.r)),
      child: Shimmer(
        duration: const Duration(seconds: 3),
        interval: const Duration(milliseconds: 200),
        colorOpacity: 1,
        enabled: true,
        color: Theme.of(context).brightness == Brightness.dark
            ? ColorPalette.shadowColorDark
            : Colors.white,
        direction: const ShimmerDirection.fromLTRB(),
        child: Container(
          color: color ?? myColorScheme(context).shimmerColor,
          width: width ?? 38.r,
          height: height ?? 38.r,
        ),
      ),
    );
  }
}
