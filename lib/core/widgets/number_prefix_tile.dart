import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import '../constants/assets.dart';

class NumberPrefixTile extends StatelessWidget {
  const NumberPrefixTile({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            Assets.iconsFlag,
            height: 15.w,
            width: 23.w,
          ),
          4.horizontalSpace,
          Text(
            StringConstants.countryDialCode.tr(),
            style: FontPalette.normal14,
          ),
          /*Icon(
            Icons.arrow_drop_down,
            size: 18.r,
          ),*/
          Container(
            width: 1.0.w,
            height: 18.h,
            color: HexColor('#DBDBDB'),
            margin: EdgeInsets.only(right: 10.w, left: 4.w),
          ),
        ],
      ),
    );
  }
}
