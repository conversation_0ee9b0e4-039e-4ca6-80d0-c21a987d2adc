// ignore_for_file: unused_element

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

import '../constants/assets.dart';

class CommonAppBar extends AppBar {
  final String? pageTitle;
  final String leadingIcon;
  final bool enableNavBack;
  final double? elevationVal;
  final Widget? titleWidget;
  final BuildContext buildContext;
  final List<Widget>? actionList;
  final bool alignCenter;
  final Color? color;
  final PreferredSizeWidget? preferredSizeBottom;
  final Function()? onBackPressed;
  final Function()? onActionButtonSearchOnPressed;
  final TextStyle? titleStyle;

  CommonAppBar({
    super.key,
    this.pageTitle,
    this.leadingIcon = '',
    this.enableNavBack = true,
    this.elevationVal,
    required this.buildContext,
    this.titleWidget,
    this.actionList,
    this.color,
    this.alignCenter = false,
    this.preferredSizeBottom,
    this.onBackPressed,
    this.onActionButtonSearchOnPressed,
    this.titleStyle,
  }) : super(
          iconTheme: const IconThemeData(
            color: Colors.black, //change your color here
          ),
          bottom: preferredSizeBottom ??
              PreferredSize(
                preferredSize: Size.fromHeight(0.h),
                child: Container(
                  color: myColorScheme(buildContext).borderColor3,
                  height: 1.h,
                ),
              ),
          backgroundColor: color ?? myColorScheme(buildContext).backgroundColor,
          elevation: elevationVal ?? 0,
          surfaceTintColor: Colors.transparent,
          centerTitle: alignCenter || actionList != null && actionList.isEmpty,
          leading: enableNavBack
              ? IconButton(
                  onPressed: onBackPressed ??
                      () => Navigator.of(buildContext).maybePop(),
                  icon: SvgPicture.asset(
                    Assets.arrowLeft,
                    height: 14.h,
                    width: 14.w,
                    color: myColorScheme(buildContext).appBarIconColor,
                  ),
                )
              : const SizedBox.shrink(),
          // leadingWidth: 35.w,
          shadowColor: HexColor('#D9E3E3'),
          systemOverlayStyle: SystemUiOverlayStyle(
            systemNavigationBarIconBrightness: Brightness.dark,
            statusBarIconBrightness: Brightness.dark,
            statusBarColor: color ?? Colors.white,
            statusBarBrightness:
                Platform.isIOS ? Brightness.light : Brightness.dark,
          ),
          titleSpacing: 0,
          title: titleWidget ??
              Text(
                pageTitle ?? '',
                style: titleStyle ??
                    FontPalette.medium18.copyWith(color: HexColor('#282C3F')),
              ),
          automaticallyImplyLeading: enableNavBack,
          actions: actionList ??
              [
                // _ActionButton(
                //   onPressed: onActionButtonSearchOnPressed ??
                //       () => Navigator.pushNamed(
                //           buildContext, RouteGenerator.routeSearchScreen),
                //   icon: Assets.iconsSearch,
                // ),
              ],
        );
}

class CommonSliverAppBar extends SliverAppBar {
  final bool enableNavBack;
  final double? elevationVal;
  final Widget? titleWidget;
  final bool centerTitleText;
  final BuildContext buildContext;
  final List<Widget>? actionList;
  final PreferredSizeWidget? preferredSizeBottom;
  final Function()? onBackPressed;

  CommonSliverAppBar({
    super.key,
    this.enableNavBack = true,
    this.elevationVal,
    required this.buildContext,
    this.titleWidget,
    this.actionList,
    this.centerTitleText = false,
    this.preferredSizeBottom,
    this.onBackPressed,
  }) : super(
          floating: false,
          backgroundColor: Colors.transparent,
          leading: enableNavBack
              ? IconButton(
                  onPressed: onBackPressed ??
                      () => Navigator.of(buildContext).maybePop(),
                  icon: SvgPicture.asset(
                    Assets.arrowLeft,
                    height: 18.h,
                    width: 14.w,
                    color: myColorScheme(buildContext).appBarIconColor,
                  ),
                )
              : const SizedBox.shrink(),
          // leadingWidth: 35.w,
          title: titleWidget,
          centerTitle: centerTitleText,
          systemOverlayStyle: SystemUiOverlayStyle(
            systemNavigationBarIconBrightness: Brightness.dark,
            statusBarIconBrightness: Brightness.dark,
            statusBarColor: Colors.white,
            statusBarBrightness:
                Platform.isIOS ? Brightness.light : Brightness.dark,
          ),
          titleSpacing: 0,
          iconTheme: const IconThemeData(
            color: Colors.black, //change your color here
          ),
          automaticallyImplyLeading: enableNavBack,
          actions: actionList,
        );
}

class _ActionButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final String icon;
  final double? size;
  final Widget? onTop;

  const _ActionButton({
    super.key,
    this.onPressed,
    required this.icon,
    this.onTop,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: onPressed,
      icon: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox.square(
            dimension: size ?? 20.r,
            // child: SvgPicture.asset(icon),
          ),
          if (onTop != null) onTop!,
        ],
      ),
    );
  }
}

class CommonAppBarWidget extends StatelessWidget {
  final String? pageTitle;
  final String? leadingIcon;
  final bool enableNavBack;
  final double? elevationVal;
  final Widget? titleWidget;
  final BuildContext buildContext;
  final List<Widget>? actionList;
  final bool alignCenter;
  final double? size;
  final PreferredSizeWidget? preferredSizeBottom;
  final Function()? onBackPressed;
  final Function()? onActionButtonSearchOnPressed;

  const CommonAppBarWidget({
    super.key,
    this.pageTitle,
    this.leadingIcon,
    this.enableNavBack = true,
    this.elevationVal,
    required this.buildContext,
    this.titleWidget,
    this.actionList,
    this.alignCenter = false,
    this.preferredSizeBottom,
    this.size,
    this.onBackPressed,
    this.onActionButtonSearchOnPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        IconButton(
          onPressed: onBackPressed ?? () => Navigator.pop(context),
          icon: SvgPicture.asset(
            leadingIcon ?? Assets.arrowLeft,
            height: size ?? 18.h,
            width: size ?? 12.w,
            color: myColorScheme(context).appBarIconColor,
          ),
        ),
      ],
    );
  }
}
