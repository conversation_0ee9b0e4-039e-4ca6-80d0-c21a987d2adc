import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';

class GrButton extends StatelessWidget {
  final String text;
  final VoidCallback? onTap;
  final IconData? icon;
  final Color? color;

  const GrButton({
    super.key,
    required this.text,
    this.icon,
    this.onTap,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Bounceable(
      onTap: onTap ?? () {},
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              color?.withValues(alpha: 0.6) ?? ColorPalette.mainColor,
              color ?? ColorPalette.mainColor,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 14),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (icon != null)
                Padding(
                  padding: const EdgeInsets.only(right: 3),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 12,
                  ),
                ),
              Text(
                text,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 12,
                  letterSpacing: 0,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class IcButton extends StatelessWidget {
  final VoidCallback? onTap;
  final IconData icon;
  final Color? color;

  const IcButton({
    super.key,
    required this.icon,
    this.onTap,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Bounceable(
      onTap: onTap ?? () {},
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              color?.withValues(alpha: 0.6) ?? const Color.fromARGB(255, 240, 240, 240),
              color ?? const Color.fromARGB(255, 240, 240, 240),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(2, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 6),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(2),
            child: Icon(
              icon,
              color: color ?? ColorPalette.mainColor,
              size: 15,
            ),
          ),
        ),
      ),
    );
  }
}
