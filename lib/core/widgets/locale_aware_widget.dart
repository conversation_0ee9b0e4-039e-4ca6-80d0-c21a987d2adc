import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

/// A widget that automatically rebuilds when the locale changes.
/// 
/// This widget is useful for ensuring that child widgets that contain
/// translatable text are properly rebuilt when the user changes the language.
/// 
/// Usage:
/// ```dart
/// LocaleAwareWidget(
///   builder: (context) => Text('hello'.tr()),
/// )
/// ```
class LocaleAwareWidget extends StatelessWidget {
  /// The builder function that creates the child widget
  final Widget Function(BuildContext context) builder;
  
  /// Optional key for the widget
  final Key? widgetKey;

  const LocaleAwareWidget({
    super.key,
    required this.builder,
    this.widgetKey,
  });

  @override
  Widget build(BuildContext context) {
    // Use the current locale as part of the key to force rebuilds
    // when the locale changes
    return KeyedSubtree(
      key: widgetKey ?? ValueKey('locale_${context.locale.toString()}'),
      child: builder(context),
    );
  }
}

/// A specialized version of LocaleAwareWidget for ListView items
/// that need to rebuild when locale changes.
class LocaleAwareListItem extends StatelessWidget {
  /// The child widget to wrap
  final Widget child;
  
  /// A unique identifier for this list item
  final String itemId;

  const LocaleAwareListItem({
    super.key,
    required this.child,
    required this.itemId,
  });

  @override
  Widget build(BuildContext context) {
    return KeyedSubtree(
      key: ValueKey('${itemId}_${context.locale.toString()}'),
      child: child,
    );
  }
}
