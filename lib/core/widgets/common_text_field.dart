// ignore_for_file: overridden_fields

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/constants/assets.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

import '../constants/enums.dart';

class CommonTextField extends StatefulWidget {
  final double? height;
  final Widget? prefixIcon;
  final String? labelText;
  final String? hintText;
  final AccountInfoType? hintType;
  final int? maxLength;
  final String? Function(String?)? validator;
  final Function(String)? onFieldSubmitted;
  final Function(String)? onChanged;
  final List<TextInputFormatter>? textInputFormatter;
  final bool enableObscure;
  final TextEditingController? controller;
  final TextInputType? textInputType;
  final TextInputAction? textInputAction;
  final FocusNode? focusNode;
  final bool? isEditable;
  final bool? enabled;
  final bool showCustomSuffixBox;
  final String? customSuffixText;
  final String? initialText;
  final Color? customSuffixBackgroundColor;
  final Function()? onSuffixIconTap;
  final Widget? suffixIcon;
  final TextCapitalization? textCapitalization;
  final bool? autofocus;
  final Color? borderColor;
  final bool enableSpacing;
  final Color? fillColor;
  final int? status;
  final AutovalidateMode autovalidateMode;
  final Iterable<String>? autofillHints;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  const CommonTextField({
    super.key,
    this.height,
    this.prefixIcon,
    this.labelText,
    this.hintText,
    this.hintType,
    this.maxLength,
    this.validator,
    this.onFieldSubmitted,
    this.onChanged,
    this.textInputFormatter,
    this.enableObscure = false,
    this.controller,
    this.textInputType,
    this.initialText,
    this.textInputAction,
    this.isEditable,
    this.enabled,
    this.showCustomSuffixBox = false,
    this.enableSpacing = false,
    this.customSuffixText,
    this.customSuffixBackgroundColor = const Color(0xFF063b87),
    this.onSuffixIconTap,
    this.focusNode,
    this.suffixIcon,
    this.textCapitalization,
    this.autofocus,
    this.fillColor,
    this.autovalidateMode = AutovalidateMode.disabled,
    this.status = -1,
    this.autofillHints,
    this.borderColor,
    this.labelStyle,
    this.hintStyle,
  });

  @override
  State<CommonTextField> createState() => _CommonTextFieldState();
}

class _CommonTextFieldState extends State<CommonTextField> {
  late final FocusNode focusNode;
  bool obscureStat = false;
  bool isPassword = false;

  @override
  void initState() {
    super.initState();
    focusNode = widget.focusNode ?? FocusNode();
    obscureStat = widget.enableObscure;
    isPassword = widget.enableObscure;
    focusNode.addListener(() {
      setState(() {});
    });
  }

  OutlineInputBorder borderColor(Color color) => OutlineInputBorder(
        borderRadius: BorderRadius.circular(5.r),
        borderSide: BorderSide(
          color: color,
          width: 1.0,
        ),
      );

  Widget _obscureBtn() {
    return InkWell(
      onTap: () {
        setState(() {
          obscureStat = !obscureStat;
        });
      },
      splashColor: Colors.transparent,
      child: Padding(
        padding: const EdgeInsets.all(8).r,
        child: SvgPicture.asset(
          obscureStat ? Assets.iconsObscureHide : Assets.iconsObscure,
        ),
      ),
    );
  }

  mangeSuffixIcon(status) {
    switch (status) {
      case -1:
        return suffixNotAddedWidget();
      case 0:
        return suffixPendingWidget();
      case 1:
        return suffixUpdatedWidget();
      case 2:
        return suffixRejectWidget();
      case 3:
        return suffixLoadingWidget();
    }
  }

  suffixNotAddedWidget() {
    return InkWell(
      onTap: widget.onSuffixIconTap,
      child: Container(
        height: 45.h,
        width: 55.w,
        decoration: BoxDecoration(
          color: widget.customSuffixBackgroundColor,
          borderRadius: BorderRadius.circular(5.r),
        ),
        child: Center(
          child: Text(
            widget.customSuffixText ?? StringConstants.suffixAddText.tr(),
            style: FontPalette.medium12.copyWith(color: ColorPalette.white),
          ),
        ),
      ),
    );
  }

  suffixPendingWidget() {
    return InkWell(
      onTap: widget.onSuffixIconTap,
      child: Container(
        height: 45.h,
        width: 55.w,
        decoration: BoxDecoration(
          color: ColorPalette.pendingColor,
          borderRadius: BorderRadius.circular(5.r),
        ),
        child: Padding(
          padding: EdgeInsets.all(10.0.r),
          child: SizedBox(
            height: 16.h,
            width: 16.h,
            child: Icon(
              Icons.refresh,
              color: ColorPalette.white,
            ),
          ),
        ),
      ),
    );
  }

  suffixLoadingWidget() {
    return InkWell(
      onTap: () {},
      child: Container(
        height: 45.h,
        width: 55.w,
        decoration: BoxDecoration(
          color: ColorPalette.primaryColor,
          borderRadius: BorderRadius.circular(5.r),
        ),
        child: Padding(
          padding: EdgeInsets.fromLTRB(15.w, 10.h, 15.w, 10.h),
          child: const CircularProgressIndicator.adaptive(
            backgroundColor: Colors.white,
          ),
        ),
      ),
    );
  }

  suffixRejectWidget() {
    return InkWell(
      onTap: widget.onSuffixIconTap,
      child: Container(
        height: 45.h,
        width: 55.w,
        decoration: BoxDecoration(
          color: ColorPalette.deniedColor,
          borderRadius: BorderRadius.circular(5.r),
        ),
        child: Padding(
          padding: EdgeInsets.all(10.0.r),
          child: SizedBox(
            height: 16.h,
            width: 16.h,
            child: Icon(
              Icons.upload_rounded,
              color: ColorPalette.white,
            ),
          ),
        ),
      ),
    );
  }

  suffixUpdatedWidget() {
    return Container(
      height: 45.h,
      width: 55.w,
      decoration: BoxDecoration(
        color: ColorPalette.successColor,
        borderRadius: BorderRadius.circular(5.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(13.0.r),
        child: SvgPicture.asset(height: 16.h, width: 16.h, Assets.checkMark),
      ),
    );
  }

  mangeSuffixHintColor(status) {
    switch (status) {
      case -1:
        return ColorPalette.labelColor;
      case 0:
        return ColorPalette.pendingColor;
      case 1:
        return ColorPalette.successColor;
      case 2:
        return ColorPalette.deniedColor;
      case 3:
        return ColorPalette.labelColor;
    }
  }

  mangeSuffixHintText(status, type) {
    switch (status) {
      case -1:
        return widget.hintText;
      case 0:
        return StringConstants.pending.tr();
      case 1:
        return suffixText(type);
      case 2:
        return StringConstants.reviewFailed.tr();
      case 3:
        return widget.hintText;
    }
  }

  suffixText(AccountInfoType type) {
    switch (type) {
      case AccountInfoType.identity:
        return StringConstants.accountInformationStatus1.tr();
      case AccountInfoType.wallet:
        return StringConstants.accountInformationStatus2.tr();
      case AccountInfoType.mobileNo:
        return StringConstants.accountInformationStatus3.tr();
      case AccountInfoType.googleToken:
        return StringConstants.accountInformationStatus4.tr();
    }
  }

  @override
  Widget build(BuildContext context) {
    return CustomForm(
      onSuffixIconTap: widget.onSuffixIconTap,
      initValue: widget.initialText ?? '',
      controller: widget.controller,
      focusNode: focusNode,
      style: widget.enableSpacing
          ? FontPalette.semiBold20.copyWith(letterSpacing: 40.w)
          : FontPalette.normal15,
      height: widget.height ?? 45.h,
      hintText: mangeSuffixHintText(widget.status, widget.hintType),
      focusedBorder: borderColor(
        widget.borderColor ??
            myColorScheme(context).textFieldBorderColor ??
            ColorPalette.textFieldBorderColor,
      ),
      enabledBorder: borderColor(
        widget.borderColor ??
            myColorScheme(context).textFieldBorderColor ??
            ColorPalette.textFieldBorderColor,
      ),
      contentPadding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 12.w),
      validator: widget.validator,
      labelStyle: widget.labelStyle ?? FontPalette.normal15,
      hintStyle: widget.hintStyle ?? FontPalette.normal12
          .copyWith(color: mangeSuffixHintColor(widget.status)),
      expandedLabelStyle: FontPalette.normal12,
      labelText: widget.labelText,
      onChanged:
          widget.onChanged != null ? (val) => widget.onChanged!(val) : null,
      onFieldSubmitted: widget.onFieldSubmitted,
      prefixIcon:
          widget.prefixIcon ?? (widget.enableObscure ? _obscureBtn() : null),
      maxLength: widget.maxLength,
      suffixIcon: widget.suffixIcon ??
          (widget.showCustomSuffixBox ? mangeSuffixIcon(widget.status) : null),
      inputFormatters: widget.textInputFormatter,
      obscureStat: obscureStat,
      isPassword: isPassword,
      textInputType: widget.textInputType,
      textInputAction: widget.textInputAction,
      hasFocus: focusNode.hasFocus,
      isEditable: widget.isEditable ?? true,
      isEnabled: widget.enabled ?? true,
      textCaps: widget.textCapitalization,
      autofocus: widget.autofocus,
      fillColor: widget.fillColor,
      autovalidateMode: widget.autovalidateMode,
    );
  }
}

class CustomForm extends FormField<String> {
  final double? height;
  final Color? fillColor;
  final TextEditingController? controller;
  final OutlineInputBorder? focusedBorder;
  final OutlineInputBorder? enabledBorder;
  final FocusNode? focusNode;
  final Function(String)? onChanged;
  final VoidCallback? onTap;
  final int? maxLength;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final Function()? onSuffixIconTap;
  final TextStyle? style;
  final TextStyle? labelStyle;
  final TextStyle? expandedLabelStyle;
  final TextStyle? hintStyle;
  final String? labelText;
  final String? hintText;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputType? textInputType;
  final EdgeInsets? contentPadding;
  final Function(String)? onFieldSubmitted;
  final String initValue;
  final bool obscureStat;
  final bool isPassword;
  final bool hasFocus;
  final bool isEditable;
  final bool isEnabled;
  final TextInputAction? textInputAction;
  final TextCapitalization? textCaps;
  final bool? autofocus;
  @override
  final AutovalidateMode autovalidateMode;
  final Iterable<String>? autofillHints;

  CustomForm({
    super.key,
    super.onSaved,
    super.validator,
    this.height,
    this.fillColor,
    this.controller,
    this.focusedBorder,
    this.enabledBorder,
    this.focusNode,
    this.onChanged,
    this.onTap,
    this.maxLength,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.style,
    this.labelStyle,
    this.expandedLabelStyle,
    this.hintStyle,
    this.labelText,
    this.hintText,
    this.inputFormatters,
    this.textInputType,
    this.contentPadding,
    this.onFieldSubmitted,
    this.initValue = '',
    this.obscureStat = false,
    this.isPassword = false,
    this.hasFocus = false,
    this.isEditable = true,
    this.isEnabled = true,
    this.textInputAction,
    this.textCaps,
    this.autofocus,
    this.autovalidateMode = AutovalidateMode.disabled,
    this.autofillHints,
  }) : super(
          initialValue: initValue,
          autovalidateMode: autovalidateMode,
          builder: (
            FormFieldState<String> state,
          ) {
            return Builder(
              builder: (context) {
                OutlineInputBorder border = OutlineInputBorder(
                  borderRadius: BorderRadius.circular(5.r),
                  borderSide: BorderSide(
                    color: state.hasError
                        ? myColorScheme(context).textFieldBorderColor ??
                            ColorPalette.primaryColor
                        : HexColor('#DBDBDB'),
                    width: 1.r,
                  ),
                );
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: height ?? 45.h,
                      child: TextFormField(
                        focusNode: focusNode,
                        autofillHints: autofillHints,
                        controller: controller,
                        autofocus: autofocus ?? false,
                        textCapitalization: textCaps ?? TextCapitalization.none,
                        style: style,
                        onTap: onTap,
                        obscureText: obscureStat,
                        inputFormatters: inputFormatters,
                        keyboardType: textInputType,
                        textInputAction: textInputAction,
                        maxLength: maxLength,
                        readOnly: !isEditable,
                        enabled: isEnabled,
                        onChanged: (val) {
                          state.reset();
                          state.didChange(val);
                          if (onChanged != null) onChanged(val);
                        },
                        onFieldSubmitted: onFieldSubmitted,
                        decoration: InputDecoration(
                          fillColor: fillColor ?? Colors.transparent,
                          filled: fillColor != null,
                          prefixIcon: isPassword
                              ? Padding(
                                  padding: const EdgeInsets.all(0),
                                  child: prefixIcon,
                                )
                              : Padding(
                                  padding: EdgeInsets.all(8.0.r),
                                  child: prefixIcon,
                                ),
                          prefixIconConstraints: BoxConstraints(
                            minWidth: 15.w,
                            minHeight: 15.h,
                          ),
                          border: state.hasError ? border : enabledBorder,
                          suffixIcon: suffixIcon,
                          suffixIconConstraints: BoxConstraints(
                            minWidth: 13.w,
                            minHeight: 13.h,
                          ),
                          counterText: '',
                          focusedBorder:
                              state.hasError ? border : focusedBorder,
                          enabledBorder: state.hasError
                              ? border
                              : hasFocus
                                  ? focusedBorder
                                  : enabledBorder,
                          contentPadding: contentPadding,
                          errorBorder: border,
                          labelText: labelText,
                          hintText: hintText,
                          labelStyle: labelStyle?.copyWith(
                            color:
                                myColorScheme(context).textFieldBorderColor ??
                                    ColorPalette.primaryColor,
                          ),
                          hintStyle: hintStyle,
                          floatingLabelBehavior: FloatingLabelBehavior.always,
                        ),
                      ),
                    ),
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      child: state.hasError
                          ? Padding(
                              padding: EdgeInsets.only(top: 4.h),
                              child: Text(
                                state.errorText ?? '',
                                style: FontPalette.normal10
                                    .copyWith(color: ColorPalette.deniedColor),
                              ),
                            )
                          : const SizedBox.shrink(),
                    ),
                  ],
                );
              },
            );
          },
        );
}

// class SuffixPendingWidget extends StatefulWidget {
//   const SuffixPendingWidget({Key? key}) : super(key: key);
//
//   @override
//   State<SuffixPendingWidget> createState() => _SuffixPendingWidgetState();
// }
//
// class _SuffixPendingWidgetState extends State<SuffixPendingWidget>
//     with SingleTickerProviderStateMixin {
//   late AnimationController _animationController;
//   late Animation<double> _animation;
//
//   @override
//   void initState() {
//     super.initState();
//
//     _animationController = AnimationController(
//       duration: const Duration(milliseconds: 200),
//       vsync: this,
//     );
//
//     _animation = Tween(begin: 0.0, end: 1.0).animate(
//       CurvedAnimation(
//         parent: _animationController,
//         curve: Curves.linear,
//       ),
//     );
//   }
//
//   @override
//   void dispose() {
//     _animationController.dispose();
//     super.dispose();
//   }
//
//   void _startAnimation() {
//     _animationController.reset();
//     _animationController.forward();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       onTap: () {
//         _startAnimation();
//         context.read<AccountInfoProvider>().getStatus(context);
//       },
//       splashColor: Colors.white.withOpacity(0.5),
//       borderRadius: BorderRadius.circular(5.0.r),
//       child: Container(
//         height: 45.h,
//         width: 55.w,
//         decoration: BoxDecoration(
//           color: ColorPalette.pendingColor,
//           borderRadius: BorderRadius.circular(5.0.r),
//         ),
//         child: Padding(
//           padding: EdgeInsets.all(10.0.r),
//           child: AnimatedBuilder(
//             animation: _animation,
//             builder: (context, child) {
//               return RotationTransition(
//                 turns: _animation,
//                 child: child,
//               );
//             },
//             child: SizedBox(
//               height: 16.0,
//               width: 16.0,
//               child: Icon(
//                 Icons.refresh,
//                 color: ColorPalette.white,
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
