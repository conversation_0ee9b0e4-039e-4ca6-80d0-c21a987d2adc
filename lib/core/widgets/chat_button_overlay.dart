import 'dart:async';
import 'package:flutter/material.dart';
import 'chat_floating_button.dart';

class ChatButtonOverlay extends StatefulWidget {
  final Widget child;
  final bool isDraggable;
  final double? initPositionYMarginBorder;
  final double navigatorBarHeight;
  final StreamController<bool>? eventStreamController;

  const ChatButtonOverlay({
    super.key,
    required this.child,
    this.isDraggable = true,
    this.initPositionYMarginBorder,
    this.navigatorBarHeight = 0,
    this.eventStreamController,
  });

  @override
  State<ChatButtonOverlay> createState() => _ChatButtonOverlayState();
}

class _ChatButtonOverlayState extends State<ChatButtonOverlay> {
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.isDraggable) {
        _createOverlay();
      }
    });
  }

  @override
  void dispose() {
    _overlayEntry?.remove();
    super.dispose();
  }

  void _createOverlay() {
    _overlayEntry = OverlayEntry(builder: (context) {
      return const ChatFloatingButton();
    });

    Overlay.of(context).insert(_overlayEntry!);
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
