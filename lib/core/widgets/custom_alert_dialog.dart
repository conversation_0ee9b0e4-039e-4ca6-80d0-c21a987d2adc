import 'dart:async';
import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

import 'custom_button.dart';

class CustomAlertDialog extends StatefulWidget {
  const CustomAlertDialog({
    super.key,
    this.child,
    this.width,
    this.height,
    this.title,
    this.message,
    this.messageTextStyle,
    this.titleTextStyle,
    this.actionButtonText,
    this.insetPadding,
    this.enableCloseBtn = true,
    this.onActionButtonPressed,
    this.isLoading,
    this.headerImage,
    this.buttonBackGroundColor,
  });

  final Widget? child;
  final double? width;
  final double? height;
  final EdgeInsets? insetPadding;
  final String? title;
  final String? message;
  final TextStyle? messageTextStyle;
  final TextStyle? titleTextStyle;
  final String? actionButtonText;
  final bool enableCloseBtn;
  final Function()? onActionButtonPressed;
  final bool? isLoading;
  final String? headerImage;
  final Color? buttonBackGroundColor;

  @override
  State<CustomAlertDialog> createState() => _CustomAlertDialogState();
}

class _CustomAlertDialogState extends State<CustomAlertDialog> {
  Timer? debounceTimer;

  @override
  void dispose() {
    debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final buttonBackGroundColor =
        widget.buttonBackGroundColor ?? myColorScheme(context).primaryColor;
    return Material(
      color: Colors.transparent,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30.r),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: <Widget>[
                BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 5,
                    sigmaY: 5,
                  ),
                  child: Container(
                    color: Colors.transparent,
                  ),
                ),
                Container(
                  width: widget.width ?? context.sw(),
                  padding: EdgeInsets.only(
                    left: 20.w,
                    top: widget.child == null ? 65.h : 43.h,
                    right: 20.w,
                    bottom: 20.h,
                  ),
                  margin: widget.child == null
                      ? EdgeInsets.only(top: 45.h)
                      : EdgeInsets.only(top: 0.h),
                  decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    color: myColorScheme(context).cardColor,
                    borderRadius: BorderRadius.circular(20.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        offset: const Offset(0, 10),
                        blurRadius: 10.r,
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      if (widget.child == null)
                        Column(
                          children: [
                            if (widget.headerImage != null) 55.verticalSpace,
                            widget.title != null
                                ? Text(
                                    widget.title ?? '',
                                    textAlign: TextAlign.center,
                                    style: widget.titleTextStyle ??
                                        FontPalette.medium18,
                                  )
                                : const SizedBox.shrink(),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 19.w),
                              child: Text(
                                widget.message ?? '',
                                textAlign: TextAlign.center,
                                style: widget.messageTextStyle ??
                                    FontPalette.medium16.copyWith(
                                      color: myColorScheme(context).titleColor,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      widget.child ?? const SizedBox.shrink(),
                      30.43.verticalSpace,
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 29.w),
                        child: CustomButton(
                          isLoading: widget.isLoading ?? false,
                          onPressed: widget.onActionButtonPressed ??
                              () {
                                if (debounceTimer != null) {
                                  debounceTimer!.cancel();
                                }
                                debounceTimer =
                                    Timer(const Duration(milliseconds: 250),
                                        () async {
                                  Navigator.pop(context);
                                });
                              },
                          label: widget.actionButtonText.toString(),
                          btnTextStyle: FontPalette.medium16,
                          width: 350.w,
                          height: 56.h,
                          backgroundColor: buttonBackGroundColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  left: 20.w,
                  right: 20.w,
                  child: widget.headerImage != null
                      ? CircleAvatar(
                          backgroundColor: Colors.transparent,
                          radius: 60.r,
                          child: SvgPicture.asset(widget.headerImage!),
                        )
                      : const SizedBox.shrink(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class AlertDialogWithTextField extends StatelessWidget {
  const AlertDialogWithTextField({
    super.key,
    this.child,
    this.width,
    this.height,
    this.title,
    this.message,
    this.messageTextStyle,
    this.titleTextStyle,
    this.actionButtonText,
    this.insetPadding,
    this.enableCancelBtn = true,
    this.onConfirmButtonPressed,
    this.onCancelButtonPressed,
    this.isLoading,
    this.headerImage,
    this.buttonBackGroundColor = const Color(0xFF063b87),
  });

  final Widget? child;
  final double? width;
  final double? height;
  final EdgeInsets? insetPadding;
  final String? title;
  final String? message;
  final TextStyle? messageTextStyle;
  final TextStyle? titleTextStyle;
  final String? actionButtonText;
  final bool enableCancelBtn;
  final Function()? onConfirmButtonPressed;
  final Function()? onCancelButtonPressed;
  final bool? isLoading;
  final String? headerImage;
  final Color buttonBackGroundColor;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30.r),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: <Widget>[
                BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 5,
                    sigmaY: 5,
                  ),
                  child: Container(
                    color: Colors.transparent,
                  ),
                ),
                Container(
                  width: width ?? context.sw(),
                  padding: EdgeInsets.only(
                    left: 20.w,
                    top: child == null ? 65.h : 43.h,
                    right: 20.w,
                    bottom: 20.h,
                  ),
                  margin: child == null
                      ? EdgeInsets.only(top: 45.h)
                      : EdgeInsets.only(top: 0.h),
                  decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20.r),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        offset: const Offset(0, 10),
                        blurRadius: 10.r,
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      if (child == null)
                        Column(
                          children: [
                            if (headerImage != null) 55.verticalSpace,
                            title != null
                                ? Padding(
                                    padding:
                                        const EdgeInsets.only(bottom: 20.0),
                                    child: Text(
                                      title ?? '',
                                      textAlign: TextAlign.center,
                                      style: titleTextStyle ??
                                          FontPalette.medium18,
                                    ),
                                  )
                                : const SizedBox.shrink(),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 19.w),
                              child: Text(
                                message ?? '',
                                textAlign: TextAlign.center,
                                style: messageTextStyle ?? FontPalette.medium16,
                              ),
                            ),
                          ],
                        ),
                      (child != null)
                          ? Column(
                              children: [
                                if (headerImage != null) 55.verticalSpace,
                                title != null
                                    ? Padding(
                                        padding: const EdgeInsets.only(
                                          bottom: 20.0,
                                        ),
                                        child: Text(
                                          title ?? '',
                                          textAlign: TextAlign.center,
                                          style: titleTextStyle ??
                                              FontPalette.medium18,
                                        ),
                                      )
                                    : const SizedBox.shrink(),
                                Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 19.w),
                                  child: Text(
                                    message ?? '',
                                    textAlign: TextAlign.center,
                                    style: messageTextStyle ??
                                        FontPalette.medium16,
                                  ),
                                ),
                                child!,
                              ],
                            )
                          : const SizedBox.shrink(),
                      30.43.verticalSpace,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: CustomButton(
                              isOutlined: true,
                              isLoading: isLoading ?? false,
                              onPressed: onCancelButtonPressed,
                              label: StringConstants.cancel.tr(),
                              btnTextStyle: FontPalette.semiBold14
                                  .copyWith(color: ColorPalette.primaryVar1),
                              width: 108.w,
                              height: 40.h,
                              borderRadiusUser: 6,
                              backgroundColor: buttonBackGroundColor,
                            ),
                          ),
                          82.horizontalSpace,
                          Expanded(
                            child: CustomButton(
                              isLoading: isLoading ?? false,
                              onPressed: onConfirmButtonPressed,
                              label: actionButtonText.toString(),
                              btnTextStyle: FontPalette.medium14,
                              width: 108.w,
                              height: 40.h,
                              borderRadiusUser: 6,
                              backgroundColor: buttonBackGroundColor,
                            ),
                          ),
                        ],
                      ),
                      30.verticalSpace,
                    ],
                  ),
                ),
                Positioned(
                  left: 20.w,
                  right: 20.w,
                  child: headerImage != null
                      ? CircleAvatar(
                          backgroundColor: Colors.transparent,
                          radius: 60.r,
                          child: SvgPicture.asset(headerImage!),
                        )
                      : const SizedBox.shrink(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
