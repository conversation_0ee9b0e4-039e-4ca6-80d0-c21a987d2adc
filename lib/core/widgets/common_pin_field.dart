import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pinput/pinput.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

import '../validator.dart';

class CommonPinFiledText extends StatefulWidget {
  final TextEditingController? controller;
  final TextEditingController? passwordController;
  final bool? obscureText;
  final String? type;

  const CommonPinFiledText({
    super.key,
    this.controller,
    this.obscureText,
    this.type = 'wallet',
    this.passwordController,
  });

  @override
  CommonPinFiledTextState createState() => CommonPinFiledTextState();
}

class CommonPinFiledTextState extends State<CommonPinFiledText> with Validator {
  final controller = TextEditingController();
  final focusNode = FocusNode();

  @override
  void dispose() {
    controller.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    const length = 6;
    final borderColor =
        myColorScheme(context).primaryColor ?? ColorPalette.primaryColor;
    const errorColor = Color.fromRGBO(255, 234, 238, 1);
    final defaultPinTheme = PinTheme(
      width: 44.w,
      height: 45.h,
      textStyle: FontPalette.semiBold21.copyWith(fontSize: 20.sp),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.r),
        border: Border.all(color: ColorPalette.borderColor),
      ),
    );

    return Pinput(
      pinAnimationType: PinAnimationType.slide,
      separatorBuilder: (index) => SizedBox(width: 20.w),
      length: length,
      controller: widget.controller ?? controller,
      keyboardType:
          const TextInputType.numberWithOptions(decimal: false, signed: false),
      obscureText: widget.obscureText ?? false,
      hapticFeedbackType: HapticFeedbackType.lightImpact,
      showCursor: false,
      errorBuilder: (errorText, pin) => Padding(
        padding: EdgeInsets.only(top: 5.0.h),
        child: Text(
          errorText!,
          style: FontPalette.normal10.copyWith(color: ColorPalette.deniedColor),
        ),
      ),
      inputFormatters: [
        FilteringTextInputFormatter.deny(RegExp(r'[^A-Za-z0-9]+')),
      ],
      focusNode: focusNode,
      defaultPinTheme: defaultPinTheme,
      focusedPinTheme: defaultPinTheme.copyWith(
        decoration: defaultPinTheme.decoration!.copyWith(
          border: Border.all(color: borderColor),
        ),
      ),
      validator: (value) {
        if (widget.type == 'google') {
          return validateGoogleCode(
            widget.controller == null
                ? controller.text
                : widget.controller?.text,
          );
        } else if (widget.type == 'confirmWallet') {
          return validateWalletConfirmPassword(
            widget.passwordController!.text,
            widget.controller == null
                ? controller.text
                : widget.controller?.text,
          );
        } else if (widget.type == 'code') {
          return validateOTPCode(
            widget.controller == null
                ? controller.text
                : widget.controller?.text,
          );
        } else {
          return validateWalletPassword(
            widget.controller == null
                ? controller.text
                : widget.controller?.text,
          );
        }
      },
      errorPinTheme: defaultPinTheme.copyWith(
        decoration: BoxDecoration(
          color: errorColor,
          borderRadius: BorderRadius.circular(5.r),
        ),
      ),
    );
  }
}
