import 'package:flutter/material.dart';

class AppWillPopScope extends StatelessWidget {
  final Widget child;
  final Future<bool> Function()? onWillPop;
  final bool shouldPop;

  const AppWillPopScope({
    super.key,
    required this.child,
    this.onWillPop,
    this.shouldPop = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppWillPopScope(
      onWillPop: () async {
        if (onWillPop != null) {
          return await onWillPop!();
        }
        return shouldPop;
      },
      child: child,
    );
  }
}
