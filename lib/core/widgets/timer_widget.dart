import 'package:flutter/material.dart';
import 'package:flutter_timer_countdown/flutter_timer_countdown.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

class TimerWidget extends StatelessWidget {
  final int? seconds;
  final Function()? onEnd;
  final Color? color;

  const TimerWidget({super.key, this.seconds, this.onEnd, this.color});

  @override
  Widget build(BuildContext context) {
    return TimerCountdown(
      enableDescriptions: false,
      spacerWidth: 1,
      timeTextStyle: FontPalette.medium12.copyWith(
        color: color ?? myColorScheme(context).primaryColor ?? ColorPalette.primaryVar1,
      ),
      colonsTextStyle: FontPalette.medium12.copyWith(
        color: color ?? myColorScheme(context).primaryColor ?? ColorPalette.primaryVar1,
      ),
      format: seconds! <= 60
          ? CountDownTimerFormat.secondsOnly
          : CountDownTimerFormat.minutesSeconds,
      endTime: DateTime.now().add(
        Duration(
          seconds: seconds ?? 0,
        ),
      ),
      onEnd: onEnd,
    );
  }
}
