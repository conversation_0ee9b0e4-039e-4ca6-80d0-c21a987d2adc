import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../models/phone_country/phone_country.dart';
import '../services/phone_country/phone_country_service.dart';
import '../dependency_injection/injectable.dart';

class CustomCountryPicker extends StatefulWidget {
  final PhoneCountry? initialSelection;
  final String? initialSelectionCode;
  final Function(PhoneCountry)? onChanged;
  final Function(PhoneCountry?)? onInit;
  final bool showFlag;
  final bool showDropDownButton;
  final TextStyle? textStyle;
  final EdgeInsets padding;
  final Widget Function(PhoneCountry?)? builder;
  final bool showCountryOnly;
  final bool showOnlyCountryWhenClosed;
  final bool alignLeft;

  const CustomCountryPicker({
    super.key,
    this.initialSelection,
    this.initialSelectionCode,
    this.onChanged,
    this.onInit,
    this.showFlag = true,
    this.showDropDownButton = true,
    this.textStyle,
    this.padding = EdgeInsets.zero,
    this.builder,
    this.showCountryOnly = false,
    this.showOnlyCountryWhenClosed = false,
    this.alignLeft = false,
  });

  @override
  State<CustomCountryPicker> createState() => _CustomCountryPickerState();
}

class _CustomCountryPickerState extends State<CustomCountryPicker> {
  PhoneCountry? _selectedCountry;
  final PhoneCountryService _phoneCountryService = getIt<PhoneCountryService>();

  @override
  void initState() {
    super.initState();
    _initializeCountry();
  }

  Future<void> _initializeCountry() async {
    PhoneCountry? country;

    if (widget.initialSelection != null) {
      country = widget.initialSelection;
    } else if (widget.initialSelectionCode != null) {
      country = await _phoneCountryService.getCountryByCode(widget.initialSelectionCode!);
    }

    country ??= await _phoneCountryService.getDefaultCountry();

    setState(() {
      _selectedCountry = country;
    });

    if (widget.onInit != null) {
      widget.onInit!(country);
    }
  }

  void _showCountryPicker() async {
    final countries = await _phoneCountryService.getCountries();

    if (!mounted) return;

    final selected = await showModalBottomSheet<PhoneCountry>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _CountryPickerBottomSheet(
        countries: countries,
        selectedCountry: _selectedCountry,
      ),
    );

    if (selected != null && selected != _selectedCountry) {
      setState(() {
        _selectedCountry = selected;
      });

      if (widget.onChanged != null) {
        widget.onChanged!(selected);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.builder != null) {
      return GestureDetector(
        onTap: _showCountryPicker,
        child: widget.builder!(_selectedCountry),
      );
    }

    return GestureDetector(
      onTap: _showCountryPicker,
      child: Padding(
        padding: widget.padding,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.showFlag && _selectedCountry != null) ...[
              Text(
                _selectedCountry!.flagEmoji,
                style: TextStyle(fontSize: 20.sp),
              ),
              SizedBox(width: 8.w),
            ],
            if (!widget.showCountryOnly) ...[
              Text(
                _selectedCountry?.phoneCode ?? '+52',
                style: widget.textStyle,
              ),
            ] else ...[
              Text(
                _selectedCountry?.name ?? 'Mexico',
                style: widget.textStyle,
              ),
            ],
            if (widget.showDropDownButton) ...[
              SizedBox(width: 4.w),
              Icon(
                Icons.arrow_drop_down,
                size: 24.w,
                color: Colors.grey.shade600,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class _CountryPickerBottomSheet extends StatefulWidget {
  final List<PhoneCountry> countries;
  final PhoneCountry? selectedCountry;

  const _CountryPickerBottomSheet({
    required this.countries,
    this.selectedCountry,
  });

  @override
  State<_CountryPickerBottomSheet> createState() => _CountryPickerBottomSheetState();
}

class _CountryPickerBottomSheetState extends State<_CountryPickerBottomSheet> {
  final TextEditingController _searchController = TextEditingController();
  List<PhoneCountry> _filteredCountries = [];

  @override
  void initState() {
    super.initState();
    _filteredCountries = widget.countries;
  }

  void _filterCountries(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredCountries = widget.countries;
      } else {
        final lowerQuery = query.toLowerCase();
        _filteredCountries = widget.countries.where((country) {
          return country.name.toLowerCase().contains(lowerQuery) ||
                 country.code.toLowerCase().contains(lowerQuery) ||
                 country.phoneCode.contains(query);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40.w,
            height: 4.h,
            margin: EdgeInsets.symmetric(vertical: 12.h),
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),

          // Title
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
              'Select Country',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          SizedBox(height: 16.h),

          // Search field
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: TextField(
              controller: _searchController,
              onChanged: _filterCountries,
              decoration: InputDecoration(
                hintText: 'Search countries...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 12.h,
                ),
              ),
            ),
          ),

          SizedBox(height: 16.h),

          // Countries list
          Expanded(
            child: ListView.builder(
              itemCount: _filteredCountries.length,
              itemBuilder: (context, index) {
                final country = _filteredCountries[index];
                final isSelected = country.code == widget.selectedCountry?.code;

                return ListTile(
                  leading: Text(
                    country.flagEmoji,
                    style: TextStyle(fontSize: 24.sp),
                  ),
                  title: Text(country.name),
                  trailing: Text(
                    country.phoneCode,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14.sp,
                    ),
                  ),
                  selected: isSelected,
                  onTap: () => Navigator.pop(context, country),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
