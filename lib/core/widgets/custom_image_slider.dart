import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/common_function.dart';
import 'package:sf_app_v2/core/shared/logic/wallet_slider/wallet_slider_cubit.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/common_wallet_slider_item.dart';
import '../../features/home/<USER>/models/balance/balance_model.dart';
import '../constants/string_constants.dart';

class CustomWalletSlider extends StatefulWidget {
  final bool? isLoading;
  final double width;
  final double fullWidth;
  final double height;
  final BalanceData balance;

  const CustomWalletSlider({
    super.key,
    required this.width,
    this.isLoading,
    required this.fullWidth,
    required this.height,
    required this.balance,
  });

  @override
  State<CustomWalletSlider> createState() => _CustomWalletSliderState();
}

class _CustomWalletSliderState extends State<CustomWalletSlider> {
  final CarouselSliderController _controller = CarouselSliderController();

  @override
  void initState() {
    super.initState();
    CommonFunctions.afterInit(_initializeSlider);
  }

  void _initializeSlider() {
    context.read<WalletSliderCubit>().setIndex(0);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _BuildCarouselSlider(
          controller: _controller,
          isLoading: widget.isLoading,
          width: widget.width,
          fullWidth: widget.fullWidth,
          height: widget.height,
          balance: widget.balance,
        ),
        SizedBox(height: 10.h),
        const _BuildIndicatorDots(),
      ],
    );
  }
}

class _BuildCarouselSlider extends StatelessWidget {
  final CarouselSliderController controller;
  final bool? isLoading;
  final double width;
  final double fullWidth;
  final double height;
  final BalanceData balance;

  const _BuildCarouselSlider({
    required this.controller,
    this.isLoading,
    required this.width,
    required this.fullWidth,
    required this.height,
    required this.balance,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: fullWidth,
      child: CarouselSlider(
        carouselController: controller,
        options: CarouselOptions(
          enableInfiniteScroll: false,
          height: height + 8,
          aspectRatio: width / height,
          viewportFraction: 1,
          onPageChanged: (index, reason) => context.read<WalletSliderCubit>().setIndex(index),
        ),
        items: _generateWalletSliderItems(),
      ),
    );
  }

  List<Widget> _generateWalletSliderItems() {
    final titles = [
      StringConstants.fundingWallet,
      StringConstants.tradingWallet,
    ];

    return List.generate(titles.length, (index) {
      return _WalletSliderItem(
        isLoading: isLoading,
        width: width,
        balance: balance,
        title: titles[index],
      );
    });
  }
}

class _WalletSliderItem extends StatelessWidget {
  final bool? isLoading;
  final double width;
  final BalanceData balance;
  final String title;

  const _WalletSliderItem({
    this.isLoading,
    required this.width,
    required this.balance,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return CommonWalletSliderItem(
      isLoading: isLoading,
      width: width,
      balance: balance,
      title: title,
      isCommunity: title == StringConstants.tradingWallet,
    );
  }
}

class _BuildIndicatorDots extends StatelessWidget {
  const _BuildIndicatorDots();

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(2, (index) => _DotIndicator(index: index)),
    );
  }
}

class _DotIndicator extends StatelessWidget {
  final int index;

  const _DotIndicator({required this.index});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<WalletSliderCubit, WalletSliderState, int>(
      selector: (state) => state.currentIndex,
      builder: (_, currentIndex) {
        return Padding(
          padding: EdgeInsets.only(right: 3.h),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20).r,
              color: currentIndex == index ? myColorScheme(context).primaryColor : ColorPalette.greyColor2,
            ),
            width: currentIndex == index ? 26.w : 16.w,
            height: 6.h,
          ),
        );
      },
    );
  }
}
