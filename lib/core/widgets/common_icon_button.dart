import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

class CustomRecIconButton extends StatelessWidget {
  final void Function()? onPressed;
  final double width;
  final double height;
  final Icon icon;
  final bool? isLoading;
  final double? borderRadiusUser;
  final String? label;
  final TextStyle? fontStyle;
  final ButtonStyle? buttonStyle;
  final TextStyle? textStyle;
  final TextStyle? btnTextStyle;
  final double? elevation;
  final bool? isOutlined;

  const CustomRecIconButton({
    super.key,
    this.onPressed,
    required this.width,
    required this.height,
    required this.icon,
    this.label,
    this.isLoading,
    this.borderRadiusUser,
    this.fontStyle,
    this.buttonStyle,
    this.btnTextStyle,
    this.isOutlined,
    this.elevation,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: height.w,
          width: width.w,
          child: ElevatedButton(
            onPressed: onPressed ?? () {},
            style: ElevatedButton.styleFrom(
              side: (isOutlined ?? false)
                  ? BorderSide(
                      width: 2.r,
                      color: ColorPalette.primaryColor,
                    )
                  : null,
              backgroundColor: (isOutlined ?? false)
                  ? Colors.transparent
                  : ColorPalette.primaryColor,
              elevation: elevation ?? 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  (borderRadiusUser ?? 10).r,
                ), // <-- Radius
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 10, 0, 10).r,
              child: icon,
            ),
          ),
        ),
        if (label != null)
          Padding(
            padding: const EdgeInsets.fromLTRB(0, 4, 0, 0).r,
            child: Text(
              label ?? '',
              style: FontPalette.medium10,
            ),
          ),
      ],
    );
  }
}

class CustomOutlinedIconButton extends StatelessWidget {
  final void Function()? onPressed;
  final double width;
  final IconData icon;
  final bool? isLoading;
  final double? borderRadiusUser;
  final TextStyle? fontStyle;
  final ButtonStyle? buttonStyle;
  final TextStyle? textStyle;
  final TextStyle? btnTextStyle;
  final double? elevation;
  final bool? isOutlined;

  const CustomOutlinedIconButton({
    super.key,
    this.onPressed,
    required this.width,
    required this.icon,
    this.isLoading,
    this.borderRadiusUser,
    this.fontStyle,
    this.buttonStyle,
    this.btnTextStyle,
    this.isOutlined,
    this.elevation,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      radius: width.w,
      backgroundColor: ColorPalette.primaryColor,
      child: CircleAvatar(
        radius: (width - 1).w,
        backgroundColor: Colors.white,
        child: GestureDetector(
          child: Icon(icon, size: width.w, color: ColorPalette.primaryColor),
          onTap: () => {},
        ),
      ),
    );
  }
}

class CustomCircularIconButton extends StatelessWidget {
  final void Function()? onPressed;
  final double width;
  final IconData icon;
  final bool? isLoading;
  final double? borderRadiusUser;
  final TextStyle? fontStyle;
  final ButtonStyle? buttonStyle;
  final TextStyle? textStyle;
  final TextStyle? btnTextStyle;
  final double? elevation;
  final bool? isOutlined;

  const CustomCircularIconButton({
    super.key,
    this.onPressed,
    required this.width,
    required this.icon,
    this.isLoading,
    this.borderRadiusUser,
    this.fontStyle,
    this.buttonStyle,
    this.btnTextStyle,
    this.isOutlined,
    this.elevation,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      radius: width.w / 2,
      backgroundColor: ColorPalette.primaryColor,
      child: Material(
        shape: const CircleBorder(),
        color: Colors.transparent,
        clipBehavior: Clip.hardEdge,
        child: InkWell(
          splashColor:
              myColorScheme(context).primaryColor ?? ColorPalette.primaryColor,
          onTap: onPressed,
          child: SizedBox(
            width: width.w,
            height: width.w,
            child: Icon(icon, size: width.w / 2, color: Colors.white),
          ),
        ),
      ),
    );
  }
}

class CustomIconSvgButton extends StatelessWidget {
  final void Function()? onPressed;
  final double width;
  final String icon;
  final bool? isLoading;
  final double? borderRadiusUser;
  final Color? backgroundColor;
  final TextStyle? fontStyle;
  final ButtonStyle? buttonStyle;
  final TextStyle? textStyle;
  final TextStyle? btnTextStyle;
  final double? elevation;
  final bool? isOutlined;
  final Color? color;

  const CustomIconSvgButton({
    super.key,
    this.onPressed,
    required this.width,
    required this.icon,
    this.isLoading,
    this.backgroundColor,
    this.borderRadiusUser,
    this.fontStyle,
    this.buttonStyle,
    this.btnTextStyle,
    this.color,
    this.isOutlined,
    this.elevation,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Bounceable(
      onTap: () {
        HapticFeedback.lightImpact();
        (onPressed ?? () {})();
      },
      child: SizedBox(
        width: width,
        height: width,
        child: Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                (backgroundColor ?? ColorPalette.mainColor).withValues(alpha: 0.8),
                (backgroundColor ?? ColorPalette.mainColor),
              ],
              center: Alignment.center,
              radius: 1.0,
            ),
          ),
          child: Material(
            shape: const CircleBorder(),
            color: Colors.transparent,
            clipBehavior: Clip.hardEdge,
            child: Padding(
              padding: EdgeInsets.all(backgroundColor != null ? width / 4 : 0),
              child: color == null
                  ? SvgPicture.asset(
                      icon,
                    )
                  : SvgPicture.asset(icon, color: color),
            ),
          ),
        ),
      ),
    );
  }
}
