import 'package:flutter/material.dart';
import 'package:slide_countdown/slide_countdown.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';

class OtpTimer extends StatefulWidget {
  final double width;
  final TextStyle? textStyle;
  final double height;
  final Color? color;
  final int? seconds;
  final Function()? onEnd;

  const OtpTimer({
    super.key,
    required this.width,
    this.color,
    this.textStyle,
    required this.height,
    this.seconds,
    this.onEnd,
  });

  @override
  State<OtpTimer> createState() => _OtpTimerState();
}

class _OtpTimerState extends State<OtpTimer> {
  final defaultPadding =
      const EdgeInsets.symmetric(horizontal: 10, vertical: 5);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SlideCountdown(
        shouldShowDays: (_) => false,
        shouldShowHours: (_) => false,
        slideDirection: SlideDirection.none,
        decoration: const BoxDecoration(),
        style: widget.textStyle ??
            FontPalette.bold28
                .copyWith(color: widget.color ?? ColorPalette.white),
        separatorStyle: widget.textStyle ??
            FontPalette.bold28
                .copyWith(color: widget.color ?? ColorPalette.white),
        showZeroValue: true,
        padding: defaultPadding,
        duration: Duration(seconds: widget.seconds!),
        // onDone: widget.onEnd,
        onChanged: (v) =>
            v.inSeconds == 1 && widget.onEnd != null ? widget.onEnd!() : () {},
      ),
    );
  }
}
