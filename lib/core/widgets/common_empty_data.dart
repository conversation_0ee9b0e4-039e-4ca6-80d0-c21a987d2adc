import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

import '../constants/string_constants.dart';

class CommonEmpty extends StatelessWidget {
  final double? topPadding;
  const CommonEmpty({super.key, this.topPadding});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: topPadding ?? 0.1.sh,
        ),
        SizedBox(
          width: 72.w,
          height: 72.w,
          child: SvgPicture.asset('assets/svg/empty.svg'),
        ),
        Text(
          StringConstants.empty.tr(),
          style: TextStyle(
            color: myColorScheme(context).viewAllColor?.withValues(alpha: 0.5),
          ),
        ),
      ],
    );
  }
}
