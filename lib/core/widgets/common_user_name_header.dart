import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/common_function.dart' show CommonFunctions;
import 'package:sf_app_v2/core/constants/assets.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/utils/shared_preference_helper.dart';
import 'package:sf_app_v2/core/widgets/language_selector_widget.dart';

class CommonUserNameHeader extends StatefulWidget {
  final String userName;
  final String userId;
  final String? icon1;
  final void Function()? onPressedIcon1;
  final void Function()? onSupport;
  final void Function()? onNotifications;

  const CommonUserNameHeader({
    super.key,
    required this.userName,
    required this.userId,
    this.icon1,
    this.onPressedIcon1,
    this.onSupport,
    this.onNotifications,
  });

  @override
  State<CommonUserNameHeader> createState() => _CommonUserNameHeaderState();
}

class _CommonUserNameHeaderState extends State<CommonUserNameHeader> {
  @override
  void initState() {
    super.initState();
    CommonFunctions.afterInit(_initialFunction);
  }

  _initialFunction() {
    // context.read<NotificationProvider>().getNotificationList(context);
  }

  Future<bool> _checkSignedIn() async {
    return SharedPreferenceHelper().getIsLoggedIn() ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _checkSignedIn(),
      builder: (context, snapshot) {
        final isSignedIn = (snapshot.data ?? false);

        return Padding(
          padding: EdgeInsets.fromLTRB(20.r, 5.r, 20.r, 10.r),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Left side - Logo or User info
              if (isSignedIn) _user(),
              if (!isSignedIn)
                SvgPicture.asset(
                  Assets.logoSvg,
                  height: 32.h,
                ),

              // Right side - Auth buttons and language selector
              if (!isSignedIn)
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Login button
                    Bounceable(
                      onTap: () =>
                          Navigator.pushNamed(context, routeLoginScreen),
                      child: Container(
                        padding: EdgeInsets.fromLTRB(10, 2.h, 10, 2.h),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey.shade800
                              : Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              LucideIcons.user_round,
                              size: 16,
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? Colors.white.withAlpha(204)
                                  : Colors.black.withAlpha(204),
                            ),
                            const SizedBox(width: 5),
                            SizedBox(
                              height: 28.h,
                              child: Center(
                                child: Text(
                                  StringConstants.login.tr(),
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 12,
                                    color: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? Colors.white.withAlpha(204)
                                        : Colors.black.withAlpha(204),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    // Signup button
                    Bounceable(
                      onTap: () => Navigator.pushNamed(
                        context,
                        routeRegistrationScreen,
                        arguments: true,
                      ),
                      child: Container(
                        padding: EdgeInsets.fromLTRB(10, 2.h, 10, 2.h),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.grey.shade800
                              : Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              LucideIcons.user_round_plus,
                              size: 16,
                              color: Theme.of(context).brightness ==
                                      Brightness.dark
                                  ? Colors.white.withAlpha(204)
                                  : Colors.black.withAlpha(204),
                            ),
                            const SizedBox(width: 5),
                            SizedBox(
                              height: 28.h,
                              child: Center(
                                child: Text(
                                  StringConstants.signUp.tr(),
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 12,
                                    color: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? Colors.white.withAlpha(204)
                                        : Colors.black.withAlpha(204),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(width: 10.w),
                    // Language selector
                    LanguageSelectorWidget(
                      fontSize: 25.sp,
                      heroTag: 'flag_${context.locale.languageCode}',
                    ),
                  ],
                ),
              if (isSignedIn) _actions(),
            ],
          ),
        );
      },
    );
  }

  _user() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _buildAvatar(),
        SizedBox(width: 12.w),
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'ID: ${widget.userId}',
                    style: FontPalette.medium14.copyWith(
                      color: myColorScheme(context).titleColor,
                    ),
                  ),
                ],
              ),
              3.verticalSpace,
              Text(
                widget.userName,
                style: FontPalette.normal12.copyWith(
                  color: myColorScheme(context).titleColor?.withAlpha(150),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAvatar() {
    return SvgPicture.asset(
      Assets.logoSvg,
      height: 32.h,
      width: 32.w,
    );
  }

  _actions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        LanguageSelectorWidget(
          fontSize: 18.sp,
          heroTag: 'flag_${context.locale.languageCode}',
        ),
        // const SizedBox(width: 5),
        // Bounceable(
        //   onTap: widget.onSupport ?? () {},
        //   child: SvgPicture.asset(
        //     Assets.iconSupport,
        //     width: 20.w,
        //     height: 32.h,
        //   ),
        // ),
        // const SizedBox(width: 5),
        // BlocSelector<NotificationCubit, NotificationState, bool?>(
        //   selector: (state) => state.isUnreadAvailable,
        //   builder: (_, value) {
        //     return Stack(
        //       children: [
        //         Bounceable(
        //           onTap: widget.onNotifications ?? () {},
        //           child: SvgPicture.asset(
        //             Assets.iconNotif,
        //             width: 20.w,
        //             height: 32.h,
        //           ),
        //         ),
        //         if (value ?? false)
        //           Container(
        //             width: 10.w,
        //             height: 10.w,
        //             decoration: const BoxDecoration(
        //               color: Colors.red,
        //               shape: BoxShape.circle,
        //             ),
        //           ),
        //       ],
        //     );
        //   },
        // ),
      ],
    );
  }
}
