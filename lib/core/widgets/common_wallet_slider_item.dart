import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/extention.dart';

import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

import '../../features/home/<USER>/models/balance/balance_model.dart';
import '../constants/assets.dart';
import '../constants/string_constants.dart';
import 'common_shimmer.dart';

/// A reusable wallet slider item widget that displays balance information.
class CommonWalletSliderItem extends StatelessWidget {
  final bool? isLoading;
  final double width;
  final double? height;
  final BalanceData balance;
  final String title;
  final bool isCommunity;

  const CommonWalletSliderItem({
    super.key,
    required this.width,
    required this.balance,
    required this.title,
    this.isLoading = false,
    this.height,
    this.isCommunity = false,
  });

  /// Builds the community balance section with available and locked balances.
  Widget _buildCommunityBalanceSection(
      double availableBalance, double lockedBalance) {
    return Container(
      constraints: BoxConstraints(maxWidth: 200.w),
      padding: EdgeInsets.all(8.r),
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(40),
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBalanceRow(
             StringConstants.availableFunds.tr(),
            _formatCurrency(availableBalance).toCurrency(),
          ),
          SizedBox(height: 2.h),
          _buildBalanceRow(
             StringConstants.unavailableFunds.tr(),
            _formatCurrency(lockedBalance).toCurrency(),
          ),
        ],
      ),
    );
  }

  /// Builds a single balance row with responsive text sizing
  Widget _buildBalanceRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
          child: AutoSizeText(
            label,
            style: FontPalette.normal12.copyWith(
              color: Colors.white,
              fontSize: 12.sp,
            ),
            maxLines: 1,
            minFontSize: 8,
          ),
        ),
        SizedBox(width: 4.w),
        Flexible(
          child: AutoSizeText(
            value,
            style: FontPalette.medium12.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            minFontSize: 8,
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  /// Formats a number as currency with commas and two decimal places
  String _formatCurrency(double value) {
    // Format with commas and fixed decimal places
    final formatter = NumberFormat("#,##0.00", "en_US");
    return formatter.format(value);
  }

  @override
  Widget build(BuildContext context) {
    // Calculate the price based on wallet type
    final double price = isCommunity
        ? (balance.community?.availableBalance ?? 0) +
            (balance.community?.lockedBalance ?? 0)
        : double.parse(balance.cash ?? '0');

    // Get the available and locked balances for community wallet
    final double availableBalance = balance.community?.availableBalance ?? 0;
    final double lockedBalance = balance.community?.lockedBalance ?? 0;

    // Get the primary color from theme
    final Color primaryColor =
        myColorScheme(context).primaryColor ?? ColorPalette.mainColor;

    return Container(
      width: width,
      height: height ?? (isCommunity ? 250 : 208),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20).r,
        color: primaryColor,
      ),
      child: Stack(
        children: [
          // Background circles decoration
          Positioned(
            right: -width.w / 4,
            child: SizedBox(
              width: width.w / 2,
              height: width.w / 2,
              child: SvgPicture.asset(Assets.circles),
            ),
          ),
          // Content
          Padding(
            padding: EdgeInsets.only(
                left: 24.r, right: 24.r, top: 14.r, bottom: 14.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Wallet title
                Row(
                  children: [
                    Text(
                      title.tr(),
                      style:
                          FontPalette.semiBold20.copyWith(color: Colors.white),
                    ),
                    const Spacer(),
                    SvgPicture.asset(
                      isCommunity ? Assets.tradingIcon : Assets.fundingIcon,
                      width: 36.r,
                      height: 36.r,
                      colorFilter: const ColorFilter.mode(
                        Colors.white,
                        BlendMode.srcIn,
                      ),
                    ),
                  ],
                ),

                // Total balance
                isLoading == true
                    ? CommonShimmer(
                        br: 8.r,
                        width: 150.r,
                        height: 28.r,
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Main balance amount
                          Flexible(
                            flex: isCommunity ? 2 : 1,
                            child: AutoSizeText(
                              _formatCurrency(price).toCurrency(),
                              style: FontPalette.bold20.copyWith(
                                color: Colors.white,
                              ),
                              maxLines: 1,
                              minFontSize: 14,
                            ),
                          ),

                          // Community wallet additional details
                          if (isCommunity && isLoading != true) ...[
                            SizedBox(width: 12.w),
                            _buildCommunityBalanceSection(
                                availableBalance, lockedBalance),
                          ],
                        ],
                      ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
