import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';

enum ModernLoadingStyle {
  circular,
  dots,
  pulse,
  wave,
  gradient,
}

class ModernLoading extends StatefulWidget {
  final ModernLoadingStyle style;
  final double size;
  final Color? color;
  final String? text;
  final bool showText;
  final Duration duration;

  const ModernLoading({
    super.key,
    this.style = ModernLoadingStyle.circular,
    this.size = 50,
    this.color,
    this.text,
    this.showText = false,
    this.duration = const Duration(milliseconds: 1200),
  });

  @override
  State<ModernLoading> createState() => _ModernLoadingState();
}

class _ModernLoadingState extends State<ModernLoading>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final loadingColor = widget.color ?? 
        (isDark ? ColorPalette.primaryColorDark : ColorPalette.primaryColor);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return _buildLoadingWidget(loadingColor);
          },
        ),
        if (widget.showText && widget.text != null) ...[
          SizedBox(height: 16.h),
          Text(
            widget.text!,
            style: FontPalette.bodyMedium.copyWith(
              color: isDark ? ColorPalette.titleColorDark : ColorPalette.titleColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  Widget _buildLoadingWidget(Color color) {
    switch (widget.style) {
      case ModernLoadingStyle.circular:
        return _buildCircularLoading(color);
      case ModernLoadingStyle.dots:
        return _buildDotsLoading(color);
      case ModernLoadingStyle.pulse:
        return _buildPulseLoading(color);
      case ModernLoadingStyle.wave:
        return _buildWaveLoading(color);
      case ModernLoadingStyle.gradient:
        return _buildGradientLoading(color);
    }
  }

  Widget _buildCircularLoading(Color color) {
    return SizedBox(
      width: widget.size.w,
      height: widget.size.h,
      child: CircularProgressIndicator(
        strokeWidth: 3,
        valueColor: AlwaysStoppedAnimation<Color>(color),
        backgroundColor: color.withValues(alpha: 0.2),
      ),
    );
  }

  Widget _buildDotsLoading(Color color) {
    return SizedBox(
      width: widget.size.w,
      height: widget.size.h / 4,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(3, (index) {
          return AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              final delay = index * 0.2;
              final animationValue = (_animation.value - delay).clamp(0.0, 1.0);
              final scale = sin(animationValue * pi) * 0.5 + 0.5;
              
              return Transform.scale(
                scale: scale,
                child: Container(
                  width: widget.size.w / 6,
                  height: widget.size.w / 6,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                  ),
                ),
              );
            },
          );
        }),
      ),
    );
  }

  Widget _buildPulseLoading(Color color) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        final scale = sin(_animation.value * pi) * 0.3 + 0.7;
        final opacity = sin(_animation.value * pi) * 0.5 + 0.5;
        
        return Transform.scale(
          scale: scale,
          child: Container(
            width: widget.size.w,
            height: widget.size.h,
            decoration: BoxDecoration(
              color: color.withValues(alpha: opacity),
              shape: BoxShape.circle,
            ),
          ),
        );
      },
    );
  }

  Widget _buildWaveLoading(Color color) {
    return SizedBox(
      width: widget.size.w,
      height: widget.size.h / 2,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(5, (index) {
          return AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              final delay = index * 0.1;
              final animationValue = (_animation.value - delay).clamp(0.0, 1.0);
              final height = sin(animationValue * pi * 2) * 0.5 + 0.5;
              
              return Container(
                width: widget.size.w / 8,
                height: (widget.size.h / 2) * height,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(2.r),
                ),
              );
            },
          );
        }),
      ),
    );
  }

  Widget _buildGradientLoading(Color color) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.rotate(
          angle: _animation.value * 2 * pi,
          child: Container(
            width: widget.size.w,
            height: widget.size.h,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: SweepGradient(
                colors: [
                  color,
                  color.withValues(alpha: 0.1),
                  color,
                ],
                stops: const [0.0, 0.5, 1.0],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Full screen loading overlay
class ModernLoadingOverlay extends StatelessWidget {
  final ModernLoadingStyle style;
  final String? text;
  final Color? backgroundColor;
  final bool dismissible;

  const ModernLoadingOverlay({
    super.key,
    this.style = ModernLoadingStyle.circular,
    this.text,
    this.backgroundColor,
    this.dismissible = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Material(
      color: backgroundColor ?? 
          (isDark ? Colors.black.withValues(alpha: 0.8) : Colors.white.withValues(alpha: 0.8)),
      child: WillPopScope(
        onWillPop: () async => dismissible,
        child: Center(
          child: Container(
            padding: EdgeInsets.all(32.r),
            decoration: BoxDecoration(
              color: isDark ? ColorPalette.cardColorDark : ColorPalette.cardColor,
              borderRadius: BorderRadius.circular(20.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: ModernLoading(
              style: style,
              size: 60,
              text: text,
              showText: text != null,
            ),
          ),
        ),
      ),
    );
  }

  static void show(
    BuildContext context, {
    ModernLoadingStyle style = ModernLoadingStyle.circular,
    String? text,
    Color? backgroundColor,
    bool dismissible = false,
  }) {
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      builder: (context) => ModernLoadingOverlay(
        style: style,
        text: text,
        backgroundColor: backgroundColor,
        dismissible: dismissible,
      ),
    );
  }

  static void hide(BuildContext context) {
    Navigator.of(context).pop();
  }
}

// Shimmer loading for lists
class ModernShimmerList extends StatelessWidget {
  final int itemCount;
  final double itemHeight;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const ModernShimmerList({
    super.key,
    this.itemCount = 5,
    this.itemHeight = 80,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return ListView.separated(
      padding: padding,
      itemCount: itemCount,
      separatorBuilder: (context, index) => SizedBox(height: 12.h),
      itemBuilder: (context, index) {
        return Container(
          height: itemHeight.h,
          margin: margin,
          decoration: BoxDecoration(
            color: isDark ? ColorPalette.cardColorDark : ColorPalette.cardColor,
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: ModernLoading(
            style: ModernLoadingStyle.pulse,
            size: itemHeight,
          ),
        );
      },
    );
  }
}
