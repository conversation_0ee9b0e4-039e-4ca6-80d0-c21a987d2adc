// ignore_for_file: overridden_fields

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

import '../constants/enums.dart';

class CommonDropdown extends StatefulWidget {
  final double? height;
  final Widget? prefixIcon;
  final String? labelText;
  final String? hintText;
  final AccountInfoType? hintType;
  final String? Function(String?)? validator;
  final Function(String)? onChanged;
  final bool enableObscure;
  final bool? isEditable;
  final bool? enabled;
  final bool showCustomSuffixBox;
  final String? customSuffixText;
  final String? initialValue;
  final Color? customSuffixBackgroundColor;
  final Function()? onSuffixIconTap;
  final Widget? suffixIcon;
  final TextCapitalization? textCapitalization;
  final bool? autofocus;
  final Color? borderColor;
  final bool enableSpacing;
  final Color? fillColor;
  final int? status;
  final AutovalidateMode autovalidateMode;
  final List<DropdownItem> items;
  final Function(DropdownItem)? onItemSelected;

  const CommonDropdown({
    super.key,
    this.height,
    this.prefixIcon,
    this.labelText,
    this.hintText,
    this.hintType,
    this.validator,
    this.onChanged,
    this.enableObscure = false,
    this.initialValue,
    this.isEditable,
    this.enabled,
    this.showCustomSuffixBox = false,
    this.enableSpacing = false,
    this.customSuffixText,
    this.customSuffixBackgroundColor = const Color(0xFF063b87),
    this.onSuffixIconTap,
    this.suffixIcon,
    this.textCapitalization,
    this.autofocus,
    this.fillColor,
    this.autovalidateMode = AutovalidateMode.disabled,
    this.status = -1,
    this.borderColor,
    required this.items,
    this.onItemSelected,
  });

  @override
  State<CommonDropdown> createState() => _CommonDropdownState();
}

class _CommonDropdownState extends State<CommonDropdown> {
  late final FocusNode focusNode;
  DropdownItem? _selectedItem;

  @override
  void initState() {
    super.initState();
    focusNode = FocusNode();

    // Set initial value if provided
    if (widget.initialValue != null) {
      _selectedItem = widget.items.firstWhere(
        (item) => item.value == widget.initialValue,
        orElse: () => widget.items.first,
      );
    }

    focusNode.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DropdownButtonFormField<DropdownItem>(
          value: _selectedItem,
          decoration: InputDecoration(
            prefixIcon: widget.prefixIcon != null
                ? Padding(
                    padding: EdgeInsets.all(8.0.r),
                    child: widget.prefixIcon,
                  )
                : null,
            contentPadding: EdgeInsets.symmetric(horizontal: 12.w),
            filled: true,
            fillColor: widget.fillColor ?? Colors.transparent,
            labelText: widget.labelText,
            labelStyle: FontPalette.normal15.copyWith(
              color:
                  myColorScheme(context).textFieldBorderColor ??
                      ColorPalette.primaryColor,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5.r),
              borderSide: BorderSide(
                color: widget.borderColor ??
                    myColorScheme(context).textFieldBorderColor ??
                    ColorPalette.textFieldBorderColor,
                width: 1.0,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5.r),
              borderSide: BorderSide(
                color: widget.borderColor ??
                    myColorScheme(context).textFieldBorderColor ??
                    ColorPalette.textFieldBorderColor,
                width: 1.0,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(5.r),
              borderSide: BorderSide(
                color: widget.borderColor ??
                    myColorScheme(context).textFieldBorderColor ??
                    ColorPalette.textFieldBorderColor,
                width: 1.0,
              ),
            ),
          ),
          hint: Text(
            widget.hintText ?? '',
            style:
                FontPalette.normal12.copyWith(color: ColorPalette.labelColor),
          ),
          icon: Icon(
            Icons.arrow_drop_down,
            color: ColorPalette.primaryColor,
          ),
          items: widget.items.map((DropdownItem item) {
            return DropdownMenuItem<DropdownItem>(
              value: item,
              child: item.child ??
                  Text(
                    item.label,
                    style: FontPalette.normal15,
                  ),
            );
          }).toList(),
          onChanged: (widget.enabled ?? true)
              ? (DropdownItem? item) {
                  if (item != null) {
                    setState(() {
                      _selectedItem = item;
                    });
                    if (widget.onItemSelected != null) {
                      widget.onItemSelected!(item);
                    }
                    if (widget.onChanged != null) {
                      widget.onChanged!(item.value);
                    }
                  }
                }
              : null,
          isExpanded: true,
          selectedItemBuilder: (BuildContext context) {
            return widget.items.map<Widget>((DropdownItem item) {
              return item.child ??
                  Text(
                    item.label,
                    style: FontPalette.normal15,
                  );
            }).toList();
          },
        ),
      ],
    );
  }
}

class DropdownItem {
  final String value;
  final String label;
  final Widget? child;

  DropdownItem({
    required this.value,
    required this.label,
    this.child,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DropdownItem && other.value == value;
  }

  @override
  int get hashCode => value.hashCode;
}
