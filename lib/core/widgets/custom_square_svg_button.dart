import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

class CustomSquareSvgButton extends StatelessWidget {
  final void Function()? onPressed;
  final double width;
  final double height;
  final String svg;
  final bool isLoading;
  final double? borderRadiusUser;
  final String? label;
  final TextStyle? fontStyle;
  final ButtonStyle? buttonStyle;
  final TextStyle? textStyle;
  final TextStyle? btnTextStyle;
  final double? elevation;
  final bool? isOutlined;

  const CustomSquareSvgButton({
    super.key,
    this.onPressed,
    required this.width,
    required this.height,
    required this.svg,
    this.label,
    this.isLoading = false,
    this.borderRadiusUser,
    this.fontStyle,
    this.buttonStyle,
    this.btnTextStyle,
    this.isOutlined,
    this.elevation,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: height,
          width: width,
          child: ElevatedButton(
            onPressed: isLoading ? () {} : onPressed ?? () {},
            style: ElevatedButton.styleFrom(
              minimumSize: Size.zero,
              padding: const EdgeInsets.all(10).r,
              side: (isOutlined ?? false)
                  ? BorderSide(
                      width: 2.r,
                      color: myColorScheme(context).primaryColor ??
                          ColorPalette.primaryColor,
                    )
                  : null,
              backgroundColor: isLoading
                  ? ColorPalette.greyColor2
                  : (isOutlined ?? false)
                      ? Colors.transparent
                      : myColorScheme(context).primaryColor,
              elevation: elevation ?? 0,
              shadowColor: Colors.transparent,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  (borderRadiusUser ?? 10).r,
                ), // <-- Radius
              ),
            ),
            child: SizedBox(
              height: height.w,
              width: width.w,
              child: SvgPicture.asset(svg),
            ),
          ),
        ),
      ],
    );
  }
}
