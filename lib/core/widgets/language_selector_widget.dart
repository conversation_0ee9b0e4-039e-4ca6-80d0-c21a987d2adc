import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/dependency_injection/injectable.dart';
import 'package:sf_app_v2/core/models/Locale/locale_model.dart';
import 'package:sf_app_v2/core/services/language/language_repository.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

/// A reusable widget for language selection
class LanguageSelectorWidget extends StatelessWidget {
  final double? fontSize;
  final String? heroTag;

  const LanguageSelectorWidget({
    super.key,
    this.fontSize,
    this.heroTag,
  });

  @override
  Widget build(BuildContext context) {
    final languageService = getIt<LanguageRepository>();
    final currentLocale = languageService.getCurrentLocale(context);
    final flagEmoji = languageService.getFlagEmoji(context, currentLocale);

    return Bounceable(
      onTap: () => showLanguageSelection(context),
      child: Text(
        flagEmoji,
        style: TextStyle(fontSize: fontSize ?? 18.sp),
      ),
    );
  }

  showLanguageSelection(BuildContext context) {
    final languageService = getIt<LanguageRepository>();
    final currentLocale = languageService.getCurrentLocale(context);

    return showModalBottomSheet(
      context: context,
      elevation: 2,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black.withValues(alpha: 0.5),
      builder: (BuildContext context) {
        return _LanguageSelectionBottomSheet(
          currentLocale: currentLocale,
          languageService: languageService,
        );
      },
    );
  }
}

class _LanguageSelectionBottomSheet extends StatelessWidget {
  final Locale currentLocale;
  final LanguageRepository languageService;

  const _LanguageSelectionBottomSheet({
    required this.currentLocale,
    required this.languageService,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.7,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHandleBar(),
          _buildHeader(context),
          _buildDivider(context),
          _buildLanguageList(context),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  Widget _buildHandleBar() {
    return Container(
      margin: EdgeInsets.only(top: 12.r),
      width: 40.w,
      height: 4.h,
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(10.r),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16.r, 16.r, 16.r, 8.r),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Select Language',
            style: FontPalette.bold13.copyWith(
              fontSize: 18.sp,
              color: myColorScheme(context).primaryColor,
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(
              Icons.close,
              color: Colors.grey,
              size: 20.r,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Divider(
      color: myColorScheme(context).borderColor?.withValues(alpha: 0.3) ??
          Colors.grey.withValues(alpha: 0.3),
    );
  }

  Widget _buildLanguageList(BuildContext context) {
    final supportedLocales = languageService.getSupportedLocales(context);

    return Flexible(
      child: ListView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
        itemCount: supportedLocales.length,
        itemBuilder: (context, index) {
          final locale = supportedLocales[index];
          final isSelected =
              languageService.isLocaleSelected(context, locale.locale);

          return _buildLanguageItem(context, locale, isSelected);
        },
      ),
    );
  }

  Widget _buildLanguageItem(
      BuildContext context, LocaleModel locale, bool isSelected) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: EdgeInsets.symmetric(vertical: 4.h),
      decoration: BoxDecoration(
        color: isSelected
            ? (myColorScheme(context).primaryColor?.withValues(alpha: 0.1) ??
                Colors.blue.withValues(alpha: 0.1))
            : Colors.transparent,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12.r),
          onTap: () => _onLanguageSelected(context, locale.locale),
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 12.r, horizontal: 8.r),
            child: Row(
              children: [
                _buildFlag(context, locale.locale),
                SizedBox(width: 16.w),
                _buildLanguageName(context, locale.locale, isSelected),
                if (isSelected) _buildCheckIcon(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFlag(BuildContext context, Locale locale) {
    return Hero(
      tag: 'flag_${locale.languageCode}',
      child: Text(
        languageService.getFlagEmoji(context, locale),
        style: TextStyle(fontSize: 18.sp),
      ),
    );
  }

  Widget _buildLanguageName(
      BuildContext context, Locale locale, bool isSelected) {
    return Expanded(
      child: Text(
        languageService.getLanguageName(locale),
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontSize: 16.sp,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: isSelected
                  ? myColorScheme(context).primaryColor
                  : myColorScheme(context).titleColor,
            ),
      ),
    );
  }

  Widget _buildCheckIcon(BuildContext context) {
    return Icon(
      Icons.check_circle,
      color: myColorScheme(context).primaryColor,
      size: 24.r,
    );
  }

  Future<void> _onLanguageSelected(BuildContext context, Locale locale) async {
    try {
      await languageService.setLocale(context, locale);
      if (context.mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      debugPrint('Error setting locale: $e');
    }
  }
}
