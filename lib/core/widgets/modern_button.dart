import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';

enum ModernButtonStyle {
  primary,
  secondary,
  glass,
  neumorphism,
  gradient,
  outline,
}

class ModernButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ModernButtonStyle style;
  final double? width;
  final double? height;
  final IconData? icon;
  final bool isLoading;
  final bool isEnabled;
  final Color? customColor;
  final List<Color>? gradientColors;
  final double borderRadius;
  final EdgeInsets? padding;
  final TextStyle? textStyle;

  const ModernButton({
    super.key,
    required this.text,
    this.onPressed,
    this.style = ModernButtonStyle.primary,
    this.width,
    this.height,
    this.icon,
    this.isLoading = false,
    this.isEnabled = true,
    this.customColor,
    this.gradientColors,
    this.borderRadius = 16,
    this.padding,
    this.textStyle,
  });

  @override
  State<ModernButton> createState() => _ModernButtonState();
}

class _ModernButtonState extends State<ModernButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (widget.isEnabled && !widget.isLoading) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (widget.isEnabled && !widget.isLoading) {
      setState(() => _isPressed = false);
      _animationController.reverse();
      widget.onPressed?.call();
    }
  }

  void _handleTapCancel() {
    if (widget.isEnabled && !widget.isLoading) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _handleTapDown,
            onTapUp: _handleTapUp,
            onTapCancel: _handleTapCancel,
            child: Container(
              width: widget.width,
              height: widget.height ?? 56.h,
              padding: widget.padding ?? EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
              decoration: _buildDecoration(isDark),
              child: _buildContent(isDark),
            ),
          ),
        );
      },
    );
  }

  BoxDecoration _buildDecoration(bool isDark) {
    switch (widget.style) {
      case ModernButtonStyle.primary:
        return _buildPrimaryDecoration(isDark);
      case ModernButtonStyle.secondary:
        return _buildSecondaryDecoration(isDark);
      case ModernButtonStyle.glass:
        return _buildGlassDecoration(isDark);
      case ModernButtonStyle.neumorphism:
        return _buildNeumorphismDecoration(isDark);
      case ModernButtonStyle.gradient:
        return _buildGradientDecoration(isDark);
      case ModernButtonStyle.outline:
        return _buildOutlineDecoration(isDark);
    }
  }

  BoxDecoration _buildPrimaryDecoration(bool isDark) {
    final color = widget.customColor ?? 
        (isDark ? ColorPalette.primaryColorDark : ColorPalette.primaryColor);
    
    return BoxDecoration(
      color: widget.isEnabled ? color : ColorPalette.buttonColorDisabled,
      borderRadius: BorderRadius.circular(widget.borderRadius.r),
      boxShadow: widget.isEnabled ? [
        BoxShadow(
          color: color.withValues(alpha: 0.3),
          blurRadius: 12,
          offset: const Offset(0, 6),
        ),
        BoxShadow(
          color: color.withValues(alpha: 0.1),
          blurRadius: 24,
          offset: const Offset(0, 12),
        ),
      ] : null,
    );
  }

  BoxDecoration _buildSecondaryDecoration(bool isDark) {
    return BoxDecoration(
      color: isDark ? ColorPalette.cardColorDark : ColorPalette.cardColor,
      borderRadius: BorderRadius.circular(widget.borderRadius.r),
      border: Border.all(
        color: isDark ? ColorPalette.borderColorDark : ColorPalette.borderColor,
        width: 1.5,
      ),
      boxShadow: [
        BoxShadow(
          color: isDark ? ColorPalette.shadowColorDark : ColorPalette.shadowColor,
          blurRadius: 8,
          offset: const Offset(0, 4),
        ),
      ],
    );
  }

  BoxDecoration _buildGlassDecoration(bool isDark) {
    return BoxDecoration(
      color: isDark ? ColorPalette.glassColorDark : ColorPalette.glassColor,
      borderRadius: BorderRadius.circular(widget.borderRadius.r),
      border: Border.all(
        color: isDark ? ColorPalette.glassBorderDark : ColorPalette.glassBorder,
        width: 1,
      ),
      boxShadow: [
        BoxShadow(
          color: (isDark ? Colors.black : Colors.white).withValues(alpha: 0.1),
          blurRadius: 20,
          offset: const Offset(0, 8),
        ),
      ],
    );
  }

  BoxDecoration _buildNeumorphismDecoration(bool isDark) {
    final bgColor = isDark ? ColorPalette.cardColorDark : ColorPalette.cardColor;
    
    return BoxDecoration(
      color: bgColor,
      borderRadius: BorderRadius.circular(widget.borderRadius.r),
      boxShadow: _isPressed ? [
        BoxShadow(
          color: isDark ? ColorPalette.neumorphismDarkShadowDark : ColorPalette.neumorphismDarkShadow,
          blurRadius: 8,
          offset: const Offset(4, 4),
        ),
      ] : [
        BoxShadow(
          color: isDark ? ColorPalette.neumorphismLightShadowDark : ColorPalette.neumorphismLightShadow,
          blurRadius: 12,
          offset: const Offset(-6, -6),
        ),
        BoxShadow(
          color: isDark ? ColorPalette.neumorphismDarkShadowDark : ColorPalette.neumorphismDarkShadow,
          blurRadius: 12,
          offset: const Offset(6, 6),
        ),
      ],
    );
  }

  BoxDecoration _buildGradientDecoration(bool isDark) {
    final colors = widget.gradientColors ?? 
        (isDark ? ColorPalette.primaryGradientDark : ColorPalette.primaryGradient);
    
    return BoxDecoration(
      gradient: LinearGradient(
        colors: colors,
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(widget.borderRadius.r),
      boxShadow: [
        BoxShadow(
          color: colors.first.withValues(alpha: 0.3),
          blurRadius: 12,
          offset: const Offset(0, 6),
        ),
      ],
    );
  }

  BoxDecoration _buildOutlineDecoration(bool isDark) {
    final color = widget.customColor ?? 
        (isDark ? ColorPalette.primaryColorDark : ColorPalette.primaryColor);
    
    return BoxDecoration(
      color: Colors.transparent,
      borderRadius: BorderRadius.circular(widget.borderRadius.r),
      border: Border.all(
        color: widget.isEnabled ? color : ColorPalette.buttonColorDisabled,
        width: 2,
      ),
    );
  }

  Widget _buildContent(bool isDark) {
    if (widget.isLoading) {
      return Center(
        child: SizedBox(
          width: 20.w,
          height: 20.h,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              _getTextColor(isDark),
            ),
          ),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.icon != null) ...[
          Icon(
            widget.icon,
            color: _getTextColor(isDark),
            size: 20.sp,
          ),
          SizedBox(width: 8.w),
        ],
        Flexible(
          child: Text(
            widget.text,
            style: widget.textStyle ?? FontPalette.semiBold14.copyWith(
              color: _getTextColor(isDark),
            ),
            textAlign: TextAlign.center,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Color _getTextColor(bool isDark) {
    if (!widget.isEnabled) {
      return isDark ? ColorPalette.greyColor4Dark : ColorPalette.greyColor4;
    }

    switch (widget.style) {
      case ModernButtonStyle.primary:
      case ModernButtonStyle.gradient:
        return ColorPalette.white;
      case ModernButtonStyle.secondary:
      case ModernButtonStyle.glass:
      case ModernButtonStyle.neumorphism:
        return isDark ? ColorPalette.titleColorDark : ColorPalette.titleColor;
      case ModernButtonStyle.outline:
        return widget.customColor ?? 
            (isDark ? ColorPalette.primaryColorDark : ColorPalette.primaryColor);
    }
  }
}
