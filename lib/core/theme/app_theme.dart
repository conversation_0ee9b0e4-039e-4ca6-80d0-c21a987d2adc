import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: ColorPalette.primaryColor,
      dividerColor: Colors.transparent,
      colorScheme: ColorScheme.light(
        primary: ColorPalette.primaryColor,
        secondary: ColorPalette.accentPrimary,
        surface: ColorPalette.cardColor,
        error: ColorPalette.errorColor,
        onPrimary: ColorPalette.white,
        onSecondary: ColorPalette.white,
        onSurface: ColorPalette.titleColor,
        onError: ColorPalette.white,
      ),
      extensions: <ThemeExtension<MyColorScheme>>[
        MyColorScheme.lightScheme,
      ],
      appBarTheme: AppBarTheme(
        backgroundColor: ColorPalette.cardColor,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
        shadowColor: ColorPalette.shadowColor,
        iconTheme: IconThemeData(color: ColorPalette.appBarIconColor),
        titleTextStyle: FontPalette.semiBold18.copyWith(
          color: ColorPalette.titleColor,
        ),
      ),
      scaffoldBackgroundColor: ColorPalette.backgroundColor,
      textTheme: textLightTheme,
      primaryTextTheme: textLightTheme,
      fontFamily: FontPalette.themeFont,
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: ColorPalette.primaryColor,
          textStyle: FontPalette.medium14,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorPalette.primaryColor,
          foregroundColor: ColorPalette.white,
          elevation: 8,
          shadowColor: ColorPalette.primaryColor.withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          textStyle: FontPalette.semiBold14,
        ),
      ),
      textSelectionTheme: TextSelectionThemeData(
        cursorColor: ColorPalette.primaryColor,
        selectionColor: ColorPalette.primaryColor.withValues(alpha: 0.3),
        selectionHandleColor: ColorPalette.primaryColor,
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: ColorPalette.cardColor,
        elevation: 20,
        selectedItemColor: ColorPalette.primaryColor,
        unselectedItemColor: ColorPalette.greyColor4,
        type: BottomNavigationBarType.fixed,
      ),
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: ColorPalette.cardColor,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
        ),
        elevation: 16,
      ),
      bottomAppBarTheme: BottomAppBarTheme(
        color: ColorPalette.cardColor,
        elevation: 20,
        shadowColor: ColorPalette.shadowColor,
      ),
      iconButtonTheme: IconButtonThemeData(
        style: IconButton.styleFrom(
          foregroundColor: ColorPalette.iconColor,
          backgroundColor: Colors.transparent,
        ),
      ),
      dialogBackgroundColor: ColorPalette.cardColor,
      dialogTheme: DialogTheme(
        backgroundColor: ColorPalette.cardColor,
        surfaceTintColor: Colors.transparent,
        iconColor: ColorPalette.iconColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        elevation: 24,
      ),
      iconTheme: IconThemeData(color: ColorPalette.iconColor),
      cardTheme: CardTheme(
        color: ColorPalette.cardColor,
        surfaceTintColor: Colors.transparent,
        elevation: 8,
        shadowColor: ColorPalette.shadowColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: ColorPalette.primaryColorDark,
      dividerColor: Colors.transparent,
      colorScheme: ColorScheme.dark(
        primary: ColorPalette.primaryColorDark,
        secondary: ColorPalette.accentPrimaryDark,
        surface: ColorPalette.cardColorDark,
        error: ColorPalette.errorColorDark,
        onPrimary: ColorPalette.whiteDark,
        onSecondary: ColorPalette.whiteDark,
        onSurface: ColorPalette.titleColorDark,
        onError: ColorPalette.whiteDark,
      ),
      extensions: <ThemeExtension<MyColorScheme>>[
        MyColorScheme.darkScheme,
      ],
      appBarTheme: AppBarTheme(
        backgroundColor: ColorPalette.cardColorDark,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
        shadowColor: ColorPalette.shadowColorDark,
        iconTheme: IconThemeData(color: ColorPalette.appBarIconColorDark),
        titleTextStyle: FontPalette.semiBold18.copyWith(
          color: ColorPalette.titleColorDark,
        ),
      ),
      scaffoldBackgroundColor: ColorPalette.backgroundColorDark,
      textTheme: textDarkTheme,
      primaryTextTheme: textDarkTheme,
      fontFamily: FontPalette.themeFont,
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: ColorPalette.primaryColorDark,
          textStyle: FontPalette.medium14,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorPalette.primaryColorDark,
          foregroundColor: ColorPalette.whiteDark,
          elevation: 8,
          shadowColor: ColorPalette.primaryColorDark.withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          textStyle: FontPalette.semiBold14,
        ),
      ),
      textSelectionTheme: TextSelectionThemeData(
        cursorColor: ColorPalette.primaryColorDark,
        selectionColor: ColorPalette.primaryColorDark.withValues(alpha: 0.3),
        selectionHandleColor: ColorPalette.primaryColorDark,
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: ColorPalette.cardColorDark,
        elevation: 20,
        selectedItemColor: ColorPalette.primaryColorDark,
        unselectedItemColor: ColorPalette.greyColor4Dark,
        type: BottomNavigationBarType.fixed,
      ),
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: ColorPalette.cardColorDark,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
        ),
        elevation: 16,
      ),
      bottomAppBarTheme: BottomAppBarTheme(
        color: ColorPalette.cardColorDark,
        elevation: 20,
        shadowColor: ColorPalette.shadowColorDark,
      ),
      iconButtonTheme: IconButtonThemeData(
        style: IconButton.styleFrom(
          foregroundColor: ColorPalette.iconColorDark,
          backgroundColor: Colors.transparent,
        ),
      ),
      dialogBackgroundColor: ColorPalette.cardColorDark,
      dialogTheme: DialogTheme(
        backgroundColor: ColorPalette.cardColorDark,
        surfaceTintColor: Colors.transparent,
        iconColor: ColorPalette.iconColorDark,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        elevation: 24,
      ),
      iconTheme: IconThemeData(color: ColorPalette.iconColorDark),
      cardTheme: CardTheme(
        color: ColorPalette.cardColorDark,
        surfaceTintColor: Colors.transparent,
        elevation: 8,
        shadowColor: ColorPalette.shadowColorDark,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  static TextTheme get textDarkTheme {
    return Typography.englishLike2018.apply(
      fontSizeFactor: 0.8.sp,
      bodyColor: ColorPalette.titleColorDark,
      fontFamily: FontPalette.themeFont,
    );
  }

  static TextTheme get textLightTheme {
    return Typography.englishLike2018.apply(
      fontSizeFactor: 0.8.sp,
      bodyColor: ColorPalette.titleColor,
      fontFamily: FontPalette.themeFont,
    );
  }
}
