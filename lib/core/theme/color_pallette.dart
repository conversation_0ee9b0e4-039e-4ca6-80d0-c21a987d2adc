import 'package:flutter/material.dart';

class ColorPalette {
  // Modern Cyber Finance Theme - Primary Colors
  static const mainColor = Color(0xFF0066FF); // Deep Electric Blue
  static const accentColor = Color(0xFF00E5FF); // Neon Cyan
  static const gradientStart = Color(0xFF0052CC); // Darker Blue
  static const gradientEnd = Color(0xFF0080FF); // Lighter Blue

  // Modern Primary Colors
  static Color get primaryColor => mainColor;
  static Color get primaryColorDark => const Color(0xFF0052CC);

  // Accent & Secondary Colors
  static Color get accentPrimary => accentColor;
  static Color get accentPrimaryDark => const Color(0xFF00BCD4);

  static Color get primaryVar1 => mainColor;
  static Color get primaryVar1Dark => const Color(0xFF0052CC);

  static Color get primaryVar2 => const Color(0xFF4285F4); // Google Blue
  static Color get primaryVar2Dark => const Color(0xFF1976D2);

  static Color get primaryVar3 => const Color(0xFF6366F1); // Indigo
  static Color get primaryVar3Dark => const Color(0xFF4F46E5);

  static Color get primaryVar4 => const Color(0xFF8B5CF6); // Purple
  static Color get primaryVar4Dark => const Color(0xFF7C3AED);

  // Modern Text Field Colors
  static Color get textFieldBorderColor => const Color(0xFFE2E8F0);
  static Color get textFieldBorderColorDark => const Color(0xFF334155);

  // Icon Colors
  static Color get iconColor => const Color(0xFF64748B);
  static Color get iconColorDark => const Color(0xFF94A3B8);

  static Color get iconBackgroundColor => const Color(0xFFF1F5F9);
  static Color get iconBackgroundColorDark => const Color(0xFF1E293B);

  static Color get iconBackgroundColor2 => const Color(0xFFDDD6FE);
  static Color get iconBackgroundColor2Dark => const Color(0xFF312E81);

  // Fee & Transaction Colors
  static Color get feeColor => const Color(0xFF059669); // Emerald
  static Color get feeColorDark => const Color(0xFF10B981);

  // Shimmer & Loading Colors
  static Color get shimmerColor => const Color(0xFFF8FAFC);
  static Color get shimmerColorDark => const Color(0xFF0F172A);

  // Shadow Colors
  static Color get shadowColor => const Color(0x1A000000);
  static Color get shadowColorDark => const Color(0x40000000);

  // Secondary Colors
  static Color get secondaryColor => const Color(0xFFF1F5F9);
  static Color get secondaryColorDark => const Color(0xFF334155);

  // Modern Background Colors
  static Color get backgroundColor => const Color(0xFFFAFBFC); // Ultra Light Gray
  static Color get backgroundColorDark => const Color(0xFF0A0E1A); // Deep Navy

  // White & Black Colors
  static Color get white => const Color(0xFFFFFFFF);
  static Color get whiteDark => const Color(0xFFF8FAFC);

  static Color get black => const Color(0xFF0F172A);
  static Color get blackDark => const Color(0xFF020617);

  static Color get primaryBlack => const Color(0xFF1E293B);
  static Color get primaryBlackDark => const Color(0xFF0F172A);

  // Modern Status Colors
  static Color get successColor => const Color(0xFF00C896); // Vibrant Green
  static Color get successColorDark => const Color(0xFF059669);

  static Color get warningColor => const Color(0xFFFF6B35); // Electric Orange
  static Color get warningColorDark => const Color(0xFFEA580C);

  static Color get errorColor => const Color(0xFFFF3366); // Neon Red
  static Color get errorColorDark => const Color(0xFFDC2626);

  static Color get infoColor => accentColor; // Neon Cyan
  static Color get infoColorDark => const Color(0xFF0891B2);

  // Modern Grey Scale
  static Color get greyColor1 => const Color(0xFFF8FAFC); // Lightest
  static Color get greyColor1Dark => const Color(0xFF1E293B);

  static Color get greyColor2 => const Color(0xFFF1F5F9);
  static Color get greyColor2Dark => const Color(0xFF334155);

  static Color get greyColor3 => const Color(0xFFE2E8F0);
  static Color get greyColor3Dark => const Color(0xFF475569);

  static Color get greyColor4 => const Color(0xFFCBD5E1);
  static Color get greyColor4Dark => const Color(0xFF64748B);

  static Color get greyColor5 => const Color(0xFF94A3B8);
  static Color get greyColor5Dark => const Color(0xFF94A3B8);

  static Color get greyColor6 => const Color(0xFF64748B);
  static Color get greyColor6Dark => const Color(0xFFCBD5E1);

  // Light Grey Variants
  static Color get lightGrey => const Color(0xFFF8FAFC);
  static Color get lightGreyDark => const Color(0xFF1E293B);

  static Color get lightGrey2 => const Color(0xFFF1F5F9);
  static Color get lightGrey2Dark => const Color(0xFF334155);

  static Color get lightGrey3 => const Color(0xFFE2E8F0);
  static Color get lightGrey3Dark => const Color(0xFF475569);

  static Color get lightGrey4 => const Color(0xFFCBD5E1);
  static Color get lightGrey4Dark => const Color(0xFF64748B);

  static Color get lightGrey6 => const Color(0xFF94A3B8);

  // Text Colors
  static Color get titleColor => const Color(0xFF0F172A);
  static Color get titleColorDark => const Color(0xFFF8FAFC);

  static Color get subTitleColor => const Color(0xFF475569);
  static Color get subTitleColorDark => const Color(0xFF94A3B8);

  static Color get subTitleColor2 => const Color(0xFF64748B);
  static Color get subTitleColor2Dark => const Color(0xFFCBD5E1);

  static Color get labelColor => const Color(0xFF64748B);
  static Color get labelColorDark => const Color(0xFF94A3B8);

  // Secondary Variants
  static Color get secondaryVar1 => const Color(0xFFE2E8F0);
  static Color get secondaryVar1Dark => const Color(0xFF475569);

  // Border Colors
  static Color get borderColor => const Color(0xFFE2E8F0);
  static Color get borderColorDark => const Color(0xFF334155);

  static Color get borderColor2 => const Color(0xFFCBD5E1);
  static Color get borderColor2Dark => const Color(0xFF475569);

  static Color get borderColor3 => const Color(0xFFF1F5F9);
  static Color get borderColor3Dark => const Color(0xFF1E293B);

  // Status Colors
  static Color get pendingColor => warningColor;
  static Color get pendingColorDark => warningColorDark;

  static Color get deniedColor => errorColor;
  static Color get deniedColorDark => errorColorDark;

  static Color get greenColor => successColor;
  static Color get greenColorDark => successColorDark;

  // Crypto & Trading Colors
  static Color get trc => const Color(0xFF00C896); // TRON Green
  static Color get trcDark => const Color(0xFF059669);

  // Skeleton & Loading
  static Color get skeletonColor => const Color(0xFFF1F5F9);
  static Color get skeletonColorDark => const Color(0xFF1E293B);

  static Color get dropShadow => const Color(0x1A000000);
  static Color get dropShadowDark => const Color(0x40000000);

  // Background Variants
  static Color get backgroundColor1 => const Color(0xFFF8FAFC);
  static Color get backgroundColor1Dark => const Color(0xFF0F172A);

  static Color get backgroundColor2 => const Color(0xFFF1F5F9);
  static Color get backgroundColor2Dark => const Color(0xFF1E293B);

  // Modern Tag Colors
  static Color get tagBlue => const Color(0xFF3B82F6); // Modern Blue
  static Color get tagBlueDark => const Color(0xFF1D4ED8);

  static Color get tagGreen => successColor; // Use modern green
  static Color get tagGreenDark => successColorDark;

  static Color get tagRed => errorColor; // Use modern red
  static Color get tagRedDark => errorColorDark;

  static Color get tagPurple => const Color(0xFF8B5CF6); // Modern Purple
  static Color get tagPurpleDark => const Color(0xFF7C3AED);

  static Color get tagOrange => warningColor; // Use modern orange
  static Color get tagOrangeDark => warningColorDark;

  // Modern Card Colors with Glass Morphism
  static Color get cardColor => const Color(0xFFFFFFFF);
  static Color get cardColorDark => const Color(0xFF1E293B);

  static Color get cardColor2 => const Color(0xFFFAFBFC);
  static Color get cardColor2Dark => const Color(0xFF334155);

  static Color get cardColor3 => const Color(0xFFF8FAFC);
  static Color get cardColor3Dark => const Color(0xFF475569);

  // Glass Morphism Colors
  static Color get glassColor => const Color(0xFFFFFFFF).withValues(alpha: 0.8);
  static Color get glassColorDark => const Color(0xFF1E293B).withValues(alpha: 0.8);

  static Color get glassBorder => const Color(0xFFE2E8F0).withValues(alpha: 0.3);
  static Color get glassBorderDark => const Color(0xFF475569).withValues(alpha: 0.3);

  // App Bar & Navigation
  static Color get appBarIconColor => const Color(0xFF475569);
  static Color get appBarIconColorDark => const Color(0xFF94A3B8);

  static Color get viewAllColor => primaryColor;
  static Color get viewAllColorDark => accentPrimary;

  // Table & List Headers
  static Color get tableHeaderColor => const Color(0xFFF8FAFC);
  static Color get tableHeaderColorDark => const Color(0xFF1E293B);

  // Button States
  static Color get buttonColorDisabled => const Color(0xFFCBD5E1);
  static Color get buttonColorDisabledDark => const Color(0xFF475569);

  // Chat Colors
  static Color get chatSubtitle => const Color(0xFF64748B);
  static Color get chatSubtitleDark => const Color(0xFF94A3B8);

  static Color get chatDividerColor => const Color(0xFFE2E8F0);
  static Color get chatDividerColorDark => const Color(0xFF334155);

  // Gradient Colors
  static List<Color> get primaryGradient => [gradientStart, gradientEnd];
  static List<Color> get primaryGradientDark => [const Color(0xFF0052CC), const Color(0xFF0066FF)];

  static List<Color> get accentGradient => [const Color(0xFF00BCD4), accentColor];
  static List<Color> get accentGradientDark => [const Color(0xFF0891B2), const Color(0xFF00E5FF)];

  static List<Color> get successGradient => [const Color(0xFF059669), successColor];
  static List<Color> get warningGradient => [const Color(0xFFEA580C), warningColor];
  static List<Color> get errorGradient => [const Color(0xFFDC2626), errorColor];

  // Hex Color for backward compatibility
  static String get hexColor1 => '#00C896';

  // Modern Material Color Swatch
  static const MaterialColor materialPrimary = MaterialColor(
    0xFF0066FF,
    <int, Color>{
      50: Color(0xFFEFF6FF),
      100: Color(0xFFDBEAFE),
      200: Color(0xFFBFDBFE),
      300: Color(0xFF93C5FD),
      400: Color(0xFF60A5FA),
      500: Color(0xFF3B82F6),
      600: Color(0xFF2563EB),
      700: Color(0xFF1D4ED8),
      800: Color(0xFF1E40AF),
      900: Color(0xFF1E3A8A),
    },
  );

  // Neumorphism Shadow Colors
  static Color get neumorphismLightShadow => const Color(0xFFFFFFFF);
  static Color get neumorphismDarkShadow => const Color(0xFFD1D5DB);
  static Color get neumorphismLightShadowDark => const Color(0xFF374151);
  static Color get neumorphismDarkShadowDark => const Color(0xFF111827);
}

class HexColor extends Color {
  HexColor(final String hexColor) : super(_getColorFromHex(hexColor));

  static int _getColorFromHex(String hexColor) {
    hexColor = hexColor.toUpperCase().replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return int.parse(hexColor, radix: 16);
  }
}
