import 'package:injectable/injectable.dart';
import 'package:flutter/material.dart';

import 'environment_config.dart';
import 'flavors/base_flavor_config.dart';
import 'feature_configs/payment_feature_config.dart';
import 'feature_configs/investment_feature_config.dart';
import '../models/Locale/locale_model.dart';
import '../constants/app_constants.dart';
import '../constants/enums.dart';

/// 
/// App configuration 
@singleton
class AppConfig {
  final EnvironmentConfig _environmentConfig;
  final FlavorConfig _flavorConfig;
  final PaymentFeatureService _paymentService;
  final InvestmentFeatureService _investmentService;

  AppConfig(
    this._environmentConfig,
    this._flavorConfig,
    this._paymentService,
    this._investmentService,
  );

  // --- Core App Settings ---
  String get appName => _flavorConfig.appName;
  String get baseUrl => _environmentConfig.apiBaseUrl;
  String get marketWsUrl => _environmentConfig.webSocketUrl;
  String get appUrl => _flavorConfig.appUrl;
  String get icon => _flavorConfig.iconPath;
  String get introVideo => _flavorConfig.introVideoPath;
  String get accountType => _flavorConfig.accountType;
  Locale get defaultLocale => _flavorConfig.defaultLocale;
  bool get fetchCommunityRecords => _flavorConfig.fetchCommunityRecords;

  // --- Environment Settings ---
  bool get isDebug => _environmentConfig.isDebug;
  bool get enableLogging => _environmentConfig.enableLogging;
  bool get showDebugInfo => _environmentConfig.showDebugInfo;

  // --- UI Feature Flags ---
  bool get showDebugVersionTag => _flavorConfig.uiConfig.showDebugVersionTag;
  bool get showTradingWallet => _flavorConfig.uiConfig.showTradingWallet;
  bool get showTransferPreview => _flavorConfig.uiConfig.showTransferPreview;
  bool get showBenefitRules => _flavorConfig.uiConfig.showBenefitRules;
  bool get showTransfer => _flavorConfig.uiConfig.showTransfer;
  bool get showPurchaseProductFields => _flavorConfig.uiConfig.showPurchaseProductFields;
  bool get showMentorVipLevel => _flavorConfig.uiConfig.showMentorVipLevel;
  bool get showWithdrawHistory => _flavorConfig.uiConfig.showWithdrawHistory;
  bool get showAppUpdate => _flavorConfig.uiConfig.showAppUpdate;
  MarketColor get marketColor => _flavorConfig.uiConfig.marketColor;
  bool get disableUpperCasePasswordProtection => _flavorConfig.uiConfig.disableUpperCasePasswordProtection;

  // --- Payment Feature Flags ---
  bool get showAddWalletAddress => _flavorConfig.paymentConfig.showAddWalletAddress;
  bool get showDepositeTxHash => _flavorConfig.paymentConfig.showDepositTxHash;

  // --- Investment Feature Flags ---
  bool get showSmartInvestmentPurchasePercentage => _flavorConfig.investmentConfig.enableSmartInvestment;

  // --- Service Delegates ---
  PaymentFeatureService get paymentService => _paymentService;
  InvestmentFeatureService get investmentService => _investmentService;

  // --- Language Support ---
  List<LocaleModel> get supportedLanguages => AppConstants.baseSupportedLanguages;
}
