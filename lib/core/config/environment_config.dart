import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';

part 'environment_config.freezed.dart';

/// Environment-specific configuration
/// 
/// Handles different environments (debug, release) and their specific settings
/// without mixing them with flavor-specific configurations.
@freezed
class EnvironmentConfig with _$EnvironmentConfig {
  const factory EnvironmentConfig({
    required bool isDebug,
    required String apiBaseUrl,
    required String webSocketUrl,
    required bool enableLogging,
    required bool showDebugInfo,
  }) = _EnvironmentConfig;

  /// Debug environment configuration
  static const debug = EnvironmentConfig(
    isDebug: true,
    apiBaseUrl: 'https://api.superfuture.world',
    webSocketUrl: 'wss://api.superfuture.world/ws',
    enableLogging: true,
    showDebugInfo: true,
  );

  /// Production environment configuration
  static const production = EnvironmentConfig(
    isDebug: false,
    apiBaseUrl: 'https://api.superfuture.world',
    webSocketUrl: 'wss://api.superfuture.world/ws',
    enableLogging: false,
    showDebugInfo: false,
  );

  /// Get current environment configuration based on build mode
  static EnvironmentConfig get current => kDebugMode ? debug : production;
}
