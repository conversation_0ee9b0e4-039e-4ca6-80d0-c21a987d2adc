import 'package:freezed_annotation/freezed_annotation.dart';

import '../../constants/enums.dart';

part 'ui_feature_config.freezed.dart';

/// Configuration for UI-related features and toggles
/// 
/// Handles feature flags for UI components, screens, and user interface behavior.
@freezed
class UiFeatureConfig with _$UiFeatureConfig {
  const factory UiFeatureConfig({
    required bool showDebugVersionTag,
    required bool showTradingWallet,
    required bool showTransferPreview,
    required bool showBenefitRules,
    required bool showTransfer,
    required bool showPurchaseProductFields,
    required bool showMentorVipLevel,
    required bool showWithdrawHistory,
    required bool showAppUpdate,
    required MarketColor marketColor,
    required bool disableUpperCasePasswordProtection,
  }) = _UiFeatureConfig;

  /// Default UI feature configuration
  factory UiFeatureConfig.defaultConfig() => const UiFeatureConfig(
    showDebugVersionTag: true,
    showTradingWallet: true,
    showTransferPreview: true,
    showBenefitRules: false,
    showTransfer: true,
    showPurchaseProductFields: false,
    showMentorVipLevel: false,
    showWithdrawHistory: false,
    showAppUpdate: false,
    marketColor: MarketColor.greenUpRedDown,
    disableUpperCasePasswordProtection: false,
  );

  /// Super Future specific UI configuration
  factory UiFeatureConfig.superFuture() => const UiFeatureConfig(
    showDebugVersionTag: true,
    showTradingWallet: true,
    showTransferPreview: true,
    showBenefitRules: false,
    showTransfer: true,
    showPurchaseProductFields: false,
    showMentorVipLevel: false,
    showWithdrawHistory: false,
    showAppUpdate: false,
    marketColor: MarketColor.greenUpRedDown,
    disableUpperCasePasswordProtection: false,
  );
}
