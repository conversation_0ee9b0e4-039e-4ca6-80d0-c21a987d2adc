import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../../features/smart_investment/domain/models/order_rate/order_rate.dart';
import '../../../features/smart_investment/domain/models/purchase_percentage/purchase_percentage.dart';

part 'investment_feature_config.freezed.dart';

/// Configuration for smart investment features
/// 
/// Handles purchase percentages, investment-related feature flags,
/// and investment configuration logic.
@freezed
class InvestmentFeatureConfig with _$InvestmentFeatureConfig {
  const factory InvestmentFeatureConfig({
    required bool enableSmartInvestment,
    required bool useStaticPurchasePercentages,
    required List<PurchasePercentage> staticPurchasePercentages,
  }) = _InvestmentFeatureConfig;

  /// Default investment feature configuration
  factory InvestmentFeatureConfig.defaultConfig() => const InvestmentFeatureConfig(
    enableSmartInvestment: true,
    useStaticPurchasePercentages: true,
    staticPurchasePercentages: _defaultPurchasePercentages,
  );
}

/// Default static purchase percentages
const List<PurchasePercentage> _defaultPurchasePercentages = [
  PurchasePercentage(percentage: '30', isSelected: false, type: 1),
  PurchasePercentage(percentage: '50', isSelected: false, type: 2),
  PurchasePercentage(percentage: '100', isSelected: true, type: 3),
];

/// Investment feature service that provides investment-related functionality
/// 
/// This service encapsulates all investment configuration logic and provides
/// a clean interface for investment-related operations.
@injectable
class InvestmentFeatureService {
  final InvestmentFeatureConfig _config;

  InvestmentFeatureService(this._config);

  /// Determines if API mode should be used for purchase percentages
  bool isApiMode(OrderRateData? apiData) {
    return _config.enableSmartInvestment &&
        !_config.useStaticPurchasePercentages &&
        !_isApiConfigEmpty(apiData);
  }

  /// Checks if the API configuration is empty or invalid
  bool _isApiConfigEmpty(OrderRateData? apiData) {
    return apiData == null ||
        apiData.orderRates == null ||
        apiData.orderRates!.isEmpty;
  }

  /// Gets the available purchase percentages based on configuration
  List<PurchasePercentage> getPurchasePercentages({
    OrderRateData? apiData,
    String? selectedPercentage,
  }) {
    if (isApiMode(apiData)) {
      return _fromApi(apiData!, selectedPercentage);
    }
    return _fromStatic(selectedPercentage);
  }

  /// Gets the default selected purchase percentage
  String getDefaultPercentage({OrderRateData? apiData}) {
    if (isApiMode(apiData)) {
      // Use the highest percentage from API as default
      final max = apiData!.orderRates!.reduce((a, b) => a > b ? a : b);
      return max.toString();
    }
    return '100';
  }

  /// Checks if a specific percentage is enabled for selection
  bool isPercentageEnabled(String percentage, {OrderRateData? apiData}) {
    if (isApiMode(apiData)) {
      final val = int.tryParse(percentage);
      return val != null && apiData!.orderRates!.contains(val);
    }
    return percentage == '100';
  }

  /// Determines whether the app should fetch purchase percentages from the API
  bool get shouldFetchFromApi {
    return _config.enableSmartInvestment && !_config.useStaticPurchasePercentages;
  }

  /// Determines if purchase percentage options should be displayed in the UI
  bool shouldShowPurchasePercentages({OrderRateData? apiConfigData}) {
    return _config.enableSmartInvestment &&
        (_config.useStaticPurchasePercentages || !_isApiConfigEmpty(apiConfigData));
  }

  // --- Internal helpers ---

  List<PurchasePercentage> _fromApi(OrderRateData apiData, String? selected) {
    final defaultPercentage = getDefaultPercentage(apiData: apiData);
    final selectedPercentage = selected ?? defaultPercentage;
    
    return apiData.orderRates!
        .map((rate) => PurchasePercentage(
              percentage: rate.toString(),
              isSelected: rate.toString() == selectedPercentage,
              type: rate,
            ))
        .toList();
  }

  List<PurchasePercentage> _fromStatic(String? selected) {
    final selectedPercentage = selected ?? '100';
    
    return _config.staticPurchasePercentages
        .map((item) => PurchasePercentage(
              percentage: item.percentage,
              isSelected: item.percentage == selectedPercentage,
              type: item.type,
            ))
        .toList();
  }

  /// Configuration getters
  bool get enableSmartInvestment => _config.enableSmartInvestment;
}
