import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:injectable/injectable.dart';

import '../../constants/assets.dart';
import '../../constants/enums.dart';
import '../../models/app_config/payment_type_model.dart';
import '../../../features/wallet/deposit/domain/models/wallet_coin/wallet_coin.dart';

part 'payment_feature_config.freezed.dart';

/// Configuration for payment-related features
/// 
/// Handles payment types, wallet configurations, and payment-related feature flags
/// following the single responsibility principle.
@freezed
class PaymentFeatureConfig with _$PaymentFeatureConfig {
  const factory PaymentFeatureConfig({
    required bool useStaticPaymentTypes,
    required bool showAddWalletAddress,
    required bool showDepositTxHash,
    required List<PaymentTypeModel> staticPaymentTypes,
    required List<PaymentTypeModel> staticWithdrawAddressTypes,
  }) = _PaymentFeatureConfig;

  /// Default payment feature configuration
  factory PaymentFeatureConfig.defaultConfig() => const PaymentFeatureConfig(
    useStaticPaymentTypes: true,
    showAddWalletAddress: false,
    showDepositTxHash: false,
    staticPaymentTypes: _defaultPaymentTypes,
    staticWithdrawAddressTypes: _defaultWithdrawAddressTypes,
  );
}

/// Static payment type configurations
const List<PaymentTypeModel> _defaultPaymentTypes = [
  PaymentTypeModel(
    code: 'TRC20',
    name: 'TRC20 (USDT)',
    networkType: PaymentType.TRC20,
    icon: Assets.trcIcon,
    id: 1,
  ),
  PaymentTypeModel(
    code: 'ERC20',
    name: 'ERC20 (USDT)',
    networkType: PaymentType.ERC20,
    icon: Assets.ercIcon,
    id: 2,
  ),
];

const List<PaymentTypeModel> _defaultWithdrawAddressTypes = [
  PaymentTypeModel(
    code: 'TRC20_USDT',
    name: 'TRC20 (USDT)',
    networkType: PaymentType.TRC20,
    icon: Assets.trcIcon,
    id: 1,
  ),
  PaymentTypeModel(
    code: 'ERC20_USDT',
    name: 'ERC20 (USDT)', 
    networkType: PaymentType.ERC20,
    icon: Assets.ercIcon,
    id: 2,
  ),
  PaymentTypeModel(
    code: 'TRC20_USDC',
    name: 'TRC20 (USDC)',
    networkType: PaymentType.TRC20, 
    icon: Assets.trcIcon,
    id: 3,
  ),
  PaymentTypeModel(
    code: 'ERC20_USDC',
    name: 'ERC20 (USDC)',
    networkType: PaymentType.ERC20,
    icon: Assets.ercIcon,
    id: 4,
  ),
];

/// Payment feature service that provides payment-related functionality
/// 
@injectable
class PaymentFeatureService {
  final PaymentFeatureConfig _config;

  PaymentFeatureService(this._config);

  /// Gets the available payment types based on configuration
  List<PaymentTypeModel> getAvailablePaymentTypes({
    List<WalletCoinData>? apiWalletCoins,
  }) {
    if (_config.useStaticPaymentTypes) {
      return _config.staticPaymentTypes;
    }
    
    final hasApiData = apiWalletCoins?.isNotEmpty ?? false;
    return hasApiData
        ? apiWalletCoins!.map(PaymentTypeModel.fromWalletCoin).toList()
        : _config.staticPaymentTypes;
  }

  /// Gets the default payment type
  PaymentTypeModel? getDefaultPaymentType({
    List<WalletCoinData>? apiWalletCoins,
  }) {
    final availableTypes = getAvailablePaymentTypes(apiWalletCoins: apiWalletCoins);
    return availableTypes.isNotEmpty ? availableTypes.first : null;
  }

  /// Checks if wallet coins API should be called
  bool get shouldFetchWalletCoinsFromApi => !_config.useStaticPaymentTypes;

  /// Gets PaymentType enum from payment code
  PaymentType getPaymentTypeFromCode(String paymentCode) {
    return paymentCode.contains('ERC20')
        ? PaymentType.ERC20
        : PaymentType.TRC20;
  }

  /// Gets static payment types for withdraw address
  List<PaymentTypeModel> getStaticWithdrawAddressTypes() {
    return _config.staticWithdrawAddressTypes;
  }

  /// Configuration getters
  bool get showAddWalletAddress => _config.showAddWalletAddress;
  bool get showDepositTxHash => _config.showDepositTxHash;
}
