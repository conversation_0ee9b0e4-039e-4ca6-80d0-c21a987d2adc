import 'package:flutter/material.dart';

import '../feature_configs/payment_feature_config.dart';
import '../feature_configs/investment_feature_config.dart';
import '../feature_configs/ui_feature_config.dart';
import 'base_flavor_config.dart';

/// Super Future flavor configuration
/// 
/// Defines the specific configuration for the Super Future app variant,
/// including all feature flags and settings specific to this flavor.
class SfFlavorConfig {
  /// Creates the Super Future flavor configuration
  static FlavorConfig create() => FlavorConfig.base(
    appName: 'Super Future',
    appUrl: 'https://superfuture.world/',
    iconPath: 'assets/logo/sf_app/logo.svg',
    introVideoPath: 'assets/splash/sf_app/introVideo.mp4',
    accountType: '3',
    defaultLocale: const Locale('en', 'US'),
    fetchCommunityRecords: true,
    paymentConfig: _createPaymentConfig(),
    investmentConfig: _createInvestmentConfig(),
    uiConfig: _createUiConfig(),
  );

  /// Creates payment configuration for Super Future
  static PaymentFeatureConfig _createPaymentConfig() => 
    PaymentFeatureConfig.defaultConfig().copyWith(
      useStaticPaymentTypes: true,
      showAddWalletAddress: false,
      showDepositTxHash: false,
    );

  /// Creates investment configuration for Super Future
  static InvestmentFeatureConfig _createInvestmentConfig() => 
    InvestmentFeatureConfig.defaultConfig().copyWith(
      enableSmartInvestment: true,
      useStaticPurchasePercentages: true,
    );

  /// Creates UI configuration for Super Future
  static UiFeatureConfig _createUiConfig() => UiFeatureConfig.superFuture();
}
