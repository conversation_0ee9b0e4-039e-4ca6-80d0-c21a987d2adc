import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/material.dart';

import '../feature_configs/payment_feature_config.dart';
import '../feature_configs/investment_feature_config.dart';
import '../feature_configs/ui_feature_config.dart';

part 'base_flavor_config.freezed.dart';

/// Base flavor configuration that defines the structure for all app flavors
/// 
/// This abstract configuration defines the common interface that all flavors
/// must implement, ensuring consistency across different app variants.
@freezed
class FlavorConfig with _$FlavorConfig {
  const factory FlavorConfig({
    required String appName,
    required String appUrl,
    required String iconPath,
    required String introVideoPath,
    required String accountType,
    required Locale defaultLocale,
    required bool fetchCommunityRecords,
    required PaymentFeatureConfig paymentConfig,
    required InvestmentFeatureConfig investmentConfig,
    required UiFeatureConfig uiConfig,
  }) = _FlavorConfig;

  /// Creates a base flavor configuration with sensible defaults
  factory FlavorConfig.base({
    required String appName,
    required String appUrl,
    required String iconPath,
    required String introVideoPath,
    String accountType = '3',
    Locale defaultLocale = const Locale('en', 'US'),
    bool fetchCommunityRecords = true,
    PaymentFeatureConfig? paymentConfig,
    InvestmentFeatureConfig? investmentConfig,
    UiFeatureConfig? uiConfig,
  }) => FlavorConfig(
    appName: appName,
    appUrl: appUrl,
    iconPath: iconPath,
    introVideoPath: introVideoPath,
    accountType: accountType,
    defaultLocale: defaultLocale,
    fetchCommunityRecords: fetchCommunityRecords,
    paymentConfig: paymentConfig ?? PaymentFeatureConfig.defaultConfig(),
    investmentConfig: investmentConfig ?? InvestmentFeatureConfig.defaultConfig(),
    uiConfig: uiConfig ?? UiFeatureConfig.defaultConfig(),
  );
}
