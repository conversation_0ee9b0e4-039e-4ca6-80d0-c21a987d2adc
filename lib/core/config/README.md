# Configuration System Documentation

## Overview

The configuration system provides a flexible, multi-layered approach to managing app settings across different environments and flavors. It uses dependency injection to ensure configurations are available throughout the app.

## Architecture

```
ConfigModule (DI Registration)
├── EnvironmentConfig (Debug/Production)
├── FlavorConfig (App Variants)
│   ├── PaymentFeatureConfig
│   ├── InvestmentFeatureConfig
│   └── UiFeatureConfig
```

## Core Components

### 1. ConfigModule (`lib/core/config/config_module.dart`)

The central dependency injection module that registers all configuration providers:

```dart
@module
abstract class ConfigModule {
  @singleton
  EnvironmentConfig get environmentConfig => EnvironmentConfig.current;
  
  @singleton
  FlavorConfig get flavorConfig => SfFlavorConfig.create();  // set to sf_flavor for now!!!!!
  
  @singleton
  PaymentFeatureConfig get paymentFeatureConfig => flavorConfig.paymentConfig;
  
  @singleton
  InvestmentFeatureConfig get investmentFeatureConfig => flavorConfig.investmentConfig;
  
  @singleton
  UiFeatureConfig get uiFeatureConfig => flavorConfig.uiConfig;
}
```

### 2. Environment Configuration

Automatically switches between debug and production settings:

- **Debug**: Full logging, debug info enabled
- **Production**: Minimal logging, optimized for performance

### 3. Flavor Configuration

Defines app variants with different branding and features:

- **Current**: Super Future (SF) flavor hardcoded
- **Structure**: Each flavor defines app name, URLs, assets, and feature configs

### 4. Feature Configurations

Modular feature flags and settings:

- **PaymentFeatureConfig**: Payment types, wallet settings
- **InvestmentFeatureConfig**: Investment features, smart investment toggles
- **UiFeatureConfig**: UI components, market colors, debug flags

## Current Flavor System

### Super Future Flavor (`SfFlavorConfig`)

```dart
static FlavorConfig create() => FlavorConfig.base(
  appName: 'Super Future',
  appUrl: 'https://superfuture.world/',
  iconPath: 'assets/logo/sf_app/logo.svg',
  introVideoPath: 'assets/splash/sf_app/introVideo.mp4',
  accountType: '3',
  defaultLocale: const Locale('en', 'US'),
  fetchCommunityRecords: true,
  // Feature configurations...
);
```

## Adding New Flavors

### Step 1: Create Flavor Configuration

Create `lib/core/config/flavors/[flavor_name]_flavor_config.dart`:

```dart
class NewFlavorConfig {
  static FlavorConfig create() => FlavorConfig.base(
    appName: 'New App Name',
    appUrl: 'https://newapp.com/',
    iconPath: 'assets/logo/new_app/logo.svg',
    introVideoPath: 'assets/splash/new_app/introVideo.mp4',
    accountType: '1',
    paymentConfig: _createPaymentConfig(),
    investmentConfig: _createInvestmentConfig(),
    uiConfig: _createUiConfig(),
  );
  
  // Custom feature configurations...
}
```

### Step 2: Update ConfigModule

Replace hardcoded flavor selection with dynamic detection:

```dart
@singleton
FlavorConfig get flavorConfig {
  const flavor = String.fromEnvironment('FLAVOR', defaultValue: 'sf');
  
  switch (flavor) {
    case 'new_flavor':
      return NewFlavorConfig.create();
    case 'sf':
    default:
      return SfFlavorConfig.create();
  }
}
```

### Step 3: Build Configuration

Use build-time flavor selection:

```bash
# Super Future flavor
fvm flutter run --dart-define=FLAVOR=sf

# New flavor
fvm flutter run --dart-define=FLAVOR=new_flavor
```

### Step 4: Asset Organization

Organize assets by flavor:

```
assets/
├── logo/
│   ├── sf_app/
│   └── new_app/
└── splash/
    ├── sf_app/
    └── new_app/
```

## Configuration Access

Configurations are injected throughout the app via `get_it`:

```dart
// Access environment config
final envConfig = getIt<EnvironmentConfig>();

// Access flavor config
final flavorConfig = getIt<FlavorConfig>();

// Access feature configs
final paymentConfig = getIt<PaymentFeatureConfig>();
final investmentConfig = getIt<InvestmentFeatureConfig>();
final uiConfig = getIt<UiFeatureConfig>();
```

## Best Practices

1. **Single Responsibility**: Each config handles one concern
2. **Immutable**: Use `@freezed` for immutable configurations
3. **Default Values**: Provide sensible defaults for all settings
4. **Environment Separation**: Keep debug/production settings separate
5. **Feature Flags**: Use boolean flags for feature toggles
6. **Asset Organization**: Group assets by flavor for easy management

## Migration Guide

When adding new configuration options:

1. Add to base configuration class
2. Update all existing flavor implementations
3. Provide default values for backward compatibility
4. Update documentation and examples

## File Structure

```
lib/core/config/
├── config_module.dart              # DI registration
├── environment_config.dart         # Environment settings
├── flavors/
│   ├── base_flavor_config.dart     # Base flavor interface
│   └── sf_flavor_config.dart       # Super Future flavor
└── feature_configs/
    ├── payment_feature_config.dart
    ├── investment_feature_config.dart
    └── ui_feature_config.dart
```