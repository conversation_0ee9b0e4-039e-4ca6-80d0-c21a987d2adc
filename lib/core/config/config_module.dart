import 'package:injectable/injectable.dart';

import 'environment_config.dart';
import 'flavors/base_flavor_config.dart';
import 'flavors/sf_flavor_config.dart';
import 'feature_configs/payment_feature_config.dart';
import 'feature_configs/investment_feature_config.dart';
import 'feature_configs/ui_feature_config.dart';

/// Configuration module for dependency injection
/// 
/// This module registers all configuration-related dependencies
/// and ensures proper initialization of the configuration system.
@module
abstract class ConfigModule {
  
  /// Provides environment configuration
  @singleton
  EnvironmentConfig get environmentConfig => EnvironmentConfig.current;

  /// Provides flavor configuration
  /// 
  /// Currently hardcoded to Super Future flavor.
  /// In a multi-flavor setup, this could be determined by build configuration.
  @singleton
  FlavorConfig get flavorConfig => SfFlavorConfig.create();

  /// Provides payment feature configuration
  @singleton
  PaymentFeatureConfig get paymentFeatureConfig => flavorConfig.paymentConfig;

  /// Provides investment feature configuration
  @singleton
  InvestmentFeatureConfig get investmentFeatureConfig => flavorConfig.investmentConfig;

  /// Provides UI feature configuration
  @singleton
  UiFeatureConfig get uiFeatureConfig => flavorConfig.uiConfig;
}
