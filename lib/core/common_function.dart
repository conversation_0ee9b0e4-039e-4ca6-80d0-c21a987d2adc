// ignore_for_file: use_build_context_synchronously

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
  import 'package:sf_app_v2/core/api/network/models/bad_request.dart';
import 'package:sf_app_v2/core/constants/assets.dart';
import 'package:sf_app_v2/core/constants/keys.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/utils/secure_storage_helper.dart';
import 'package:sf_app_v2/core/utils/shared_preference_helper.dart';
// import 'package:sf_app_v2/features/chat_v2/cubit/chat/chat_cubit.dart';
import 'package:url_launcher/url_launcher.dart';

import '../features/home/<USER>/home/<USER>';
import 'api/network/network.dart';
import 'routes/navigator.dart';

class CommonFunctions {
  static void afterInit(Function function) =>
      SchedulerBinding.instance.addPostFrameCallback((_) => function());

  static showDialogPopUp(
    BuildContext context,
    Widget dialogWidget, {
    bool barrierDismissible = true,
    String? routeName,
  }) =>
      showGeneralDialog(
        barrierDismissible: barrierDismissible,
        context: context,
        barrierLabel: "",
        routeSettings:
            routeName == null ? null : RouteSettings(name: routeName),
        pageBuilder: (ctx, a1, a2) => Container(),
        transitionBuilder: (ctx, a1, a2, child) {
          // var curve = Curves.easeInOut.transform(a1.value);
          return WillPopScope(
            onWillPop: () async {
              // if (barrierDismissible) Navigator.pop(context);
              return barrierDismissible;
            },
            child: dialogWidget,
            // child: Transform.scale(
            //   scale: curve,
            //   child: dialogWidget,
            // ),
          );
        },
        // transitionDuration: const Duration(milliseconds: 300),
      );

  launchEmailURL(url) async {
    String? email;
    if (url.startsWith('mailto:')) {
      email = url.substring('mailto:'.length);
    }
    final Uri emailLaunchUri = Uri(
      path: email,
      scheme: 'mailto',
    );
    if (!await launchUrl(emailLaunchUri)) {
      throw Exception('Could not launch $emailLaunchUri');
    }
    // await canLaunch(url) ? await launch(url) : throw 'Could not launch $url';
  }

  showFlutterToast(String message) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      timeInSecForIosWeb: 1,
      backgroundColor: const Color.fromRGBO(0, 0, 0, 0.5),
      textColor: ColorPalette.white,
      fontSize: 16.0,
    );
  }

  String formatNumberToK(int number) {
    if (number < 1000) {
      return number.toString();
    } else {
      final double value = number / 1000;
      final String roundedValue =
          value.toStringAsFixed(value.truncateToDouble() == value ? 0 : 1);
      return '${roundedValue}k';
    }
  }

  findDaysOfHolding(createTime) {
    DateTime currentDate = DateTime.now();
    String formattedDate =
        DateFormat('yyyy-MM-dd HH:mm:ss').format(currentDate);
    int differenceInDays = DateTime.parse(formattedDate)
        .difference(DateTime.parse(createTime))
        .inDays;
    return differenceInDays;
  }

  ///format price
  String formatPrice(String price) {
    NumberFormat priceFormat = NumberFormat("#,##0.00", "en_US");
    double parsedPrice = double.tryParse(price) ?? 0.0;
    return priceFormat.format(parsedPrice);
  }

  static getIconFromLevel(int level) => switch (level) {
        1 => Assets.level0,
        2 => Assets.level1,
        3 => Assets.level2,
        4 => Assets.level3,
        5 => Assets.level4,
        6 => Assets.level5,
        _ => Assets.level0,
      };

  String formatMarketSymbol(String text) {
    if (text.length < 4) {
      return text;
    }

    String lastFourDigits = text.substring(text.length - 4);
    String remainingDigits = text.substring(0, text.length - 4);

    return '$remainingDigits / $lastFourDigits';
  }

  double roundNumber(double input, {int precision = 2}) => double.parse(
        '$input'.substring(0, '$input'.indexOf('.') + precision + 1),
      );

  dynamic errorMapping(Response? response) {
    final badRequest = <BadRequest>[]; // List to store BadRequest objects
    var errorString = ''; // String to accumulate error messages

    // Check if response and response.data are not null
    if (response?.data is Map) {
      // Check for specific error types
      if (response?.data.containsKey('msg')) {
        final message = response?.data['msg'];
        if (message is String) {
          badRequest.add(BadRequest(error: [message]));
        }
      } else if (response?.data.containsKey('error')) {
        final error = response?.data['error'];
        if (error is String) {
          badRequest.add(BadRequest(error: [error]));
        }
      }
    } else {
      // Handle case where response.data is null or not a Map
      badRequest.add(BadRequest(error: ['Invalid response format']));
    }

    // Construct error string from badRequest list
    for (var element in badRequest) {
      var subString = '';
      element.error?.forEach((sub) {
        subString = '$subString\n$sub';
      });
      if (errorString.isEmpty) {
        errorString = subString;
      } else {
        errorString = '$errorString\n\n$subString';
      }
    }

    return errorString;
  }

  String replaceCharacters(String text) =>
      capitalizeFirstLetter(text.replaceAll(RegExp('[\\W_]+'), ' '));

  String capitalizeFirstLetter(String input) {
    if (input.isEmpty) {
      return input; // Return an empty string if the input is empty.
    }
    return input[0].toUpperCase() + input.substring(1);
  }

  Map<String, dynamic> removeNullValues(Map<String, dynamic> input) =>
      Map.fromEntries(input.entries.where((e) => e.value != null));

  static logoutUser(BuildContext context, {bool isNavToMain = false}) async {
    // context.read<ChatCubit>().logout();
    await SecureStorageHelper().deleteSecureData(LocalStorageKeys.token);
    await SecureStorageHelper().deleteAllSecureData();
    SharedPreferenceHelper().clearAll();
    NetworkProvider.clearCache();

    // // Only reset locale for non-cfroex flavors
    // if (F.appFlavor != Flavor.cfroex) {
    //   context.deleteSaveLocale();
    //   context.resetLocale();
    // }

    context.read<HomeCubit>().clearBalanceData();
    // HttpService().onLogout();
    // global.invitationCode = null;

    if (isNavToMain) {
      Navigator.of(context)
          .pushNamedAndRemoveUntil(routeMainScreen, (route) => false);
      // context.read<ValueNotifier<int>>().value = 0;
    } else {
      Navigator.of(context)
          .pushNamedAndRemoveUntil(routeLoginScreen, (route) => false);
    }
  }

  String getLanguageLocaleCode() {
    final languageCode =
        navigatorKey.currentContext!.locale.countryCode?.toLowerCase();
    return switch (languageCode) {
      'br' => 'pt-BR', // Portuguese (Brazil)
      'fr' => 'fr-FR', // French
      'in' => 'hi-IN', // Hindi
      'jp' => 'ja-JP', // Japanese
      'sa' => 'ar-SA', // Arabic
      'hk' => 'zh-HK', // Chinese (Hong Kong)
      'cn' => 'zh-CN', // Chinese (Simplified)
      'kr' => 'ko-KR', // Korean
      'es' => 'es-ES', // Spanish
      'de' => 'de-DE', // German
      'my' => 'ms-MY', // Malay
      'mx' => 'es-MX', // Spanish (Mexico)
      'it' => 'it-IT', // Italian
      'cs' => 'cs-CZ', // Czech
      'nl' => 'nl-NL', // Dutch
      'no' => 'no-NO', // Norwegian
      'pl' => 'pl-PL', // Polish
      'sv' => 'sv-SE', // Swedish
      'ru' => 'ru-RU', // Russian
      'ua' => 'uk-UA', // Ukrainian
      'lt' => 'lt-LT', // Lithuanian
      'gr' => 'el-GR', // Greek
      'dk' => 'da-DK', // Danish
      'fi' => 'fi-FI', // Finnish
      'hu' => 'hu-HU', // Hungarian
      'ee' => 'et-EE', // Estonian
      'lv' => 'lv-LV', // Latvian
      'sk' => 'sk-SK', // Slovak
      'si' => 'sl-SI', // Slovenian
      'tr' => 'tr-TR', // Turkish
      _ => 'en-US',
    };
  }

  static Future<void> launchUrls(String url) async {
    final urlWithHttps = url.startsWith('https://') ? url : 'https://$url';
    await launchUrl(Uri.parse(urlWithHttps));
  }
}
