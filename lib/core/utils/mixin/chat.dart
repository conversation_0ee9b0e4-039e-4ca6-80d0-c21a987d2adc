import 'package:flutter/material.dart';
import 'package:sf_app_v2/core/routes/navigator.dart';
import '../../dependency_injection/injectable.dart';
import '../../shared/logic/chat_button/chat_button_cubit.dart';

mixin HideFloatButtonRouteAwareMixin<T extends StatefulWidget> on State<T> implements RouteAware {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final modalRoute = ModalRoute.of(context);
    if (modalRoute != null) {
      routeObserver.subscribe(this, modalRoute);
    }
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPop() => getIt<ChatButtonCubit>().toggleChatButton(showChatButton: true);

  @override
  void didPush() => getIt<ChatButtonCubit>().toggleChatButton(showChatButton: false);

  @override
  void didPopNext() {}

  @override
  void didPushNext() {}

}