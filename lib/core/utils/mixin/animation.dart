import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

mixin StaggeredAnimation {
  /// Creates a list of widgets with staggered slide and fade animations
  ///
  /// [children] The list of widgets to animate
  /// [childAnimationBuilder] Optional custom animation builder
  /// [verticalOffset] Vertical offset for slide animation, defaults to 50.0
  List<Widget> staggeredAnimation({
    required List<Widget> children,
    Widget Function(Widget)? childAnimationBuilder,
    double verticalOffset = 50.0,
  }) {
    return AnimationConfiguration.toStaggeredList(
      childAnimationBuilder: childAnimationBuilder ??
          (widget) => SlideAnimation(
                verticalOffset: verticalOffset,
                child: FadeInAnimation(child: widget),
              ),
      children: children,
    );
  }

  /// Creates a list of widgets with staggered list animations
  ///
  /// [items] The list of items to animate
  /// [itemBuilder] Function to build widget for each item
  /// [verticalOffset] Vertical offset for slide animation, defaults to 50.0
  List<Widget> staggeredListAnimation<T>({
    required List<T> items,
    required Widget Function(T, int) itemBuilder,
    double verticalOffset = 50.0,
  }) {
    return AnimationConfiguration.toStaggeredList(
      duration: const Duration(milliseconds: 500),
      childAnimationBuilder: (widget) => SlideAnimation(
        verticalOffset: verticalOffset,
        child: FadeInAnimation(child: widget),
      ),
      children: List.generate(
        items.length,
        (index) => itemBuilder(items[index], index),
      ),
    );
  }
}
