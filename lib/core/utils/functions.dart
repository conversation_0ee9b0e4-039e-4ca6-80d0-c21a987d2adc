import 'package:flutter/material.dart';
import 'package:sf_app_v2/core/constants/app_constants.dart';

String formatString(String input) {
  if (input.trim().contains(' ')) {
    return input
        .split(' ')
        .where((word) => word.isNotEmpty)
        .map((word) => word[0].toUpperCase())
        .join();
  } else {
    return input.toUpperCase();
  }
}

String formatNumber(String value) {
  double? number = double.tryParse(value.replaceAll(',', ''));
  if (number == null) return value;

  if (number >= 1000000) {
    return '${(number / 1000000).toStringAsFixed(2)}M';
  } else if (number >= 1000) {
    return '${(number / 1000).toStringAsFixed(2)}K';
  }
  return number.toStringAsFixed(2);
}

/// Extracts numeric value from currency-prefixed string
/// Example: "USD123.45" -> 123.45
double parseUSDAmount(String usdText) {
  String numericPart = usdText.replaceAll(AppConstants.currencySymbol, '').trim();
  return double.tryParse(numericPart) ?? 0.0;
}

/// Safely extracts numeric value from currency-prefixed string with null safety
/// Returns 0.0 if parsing fails
double safeParseUSDAmount(String? usdText) {
  if (usdText == null || usdText.isEmpty) return 0.0;
  return parseUSDAmount(usdText);
}

Color getPercentageColor(String value) {
  double? number = double.tryParse(value.replaceAll(',', ''));
  if (number == null) return Colors.black87;
  return number < 0 ? Colors.red : Colors.green;
}
