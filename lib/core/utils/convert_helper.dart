import 'package:html/parser.dart';
import 'package:intl/intl.dart';
import 'package:sf_app_v2/core/extention.dart';

class ConvertHelper {
  static formatDate(String dateString) {
    DateTime dateTime = DateTime.parse(dateString);
    DateFormat formatter = DateFormat("MMM d, yyyy h:mm a");
    String formattedDate = formatter.format(dateTime);
    return formattedDate;
  }

  static String formatPriceUsd(double price) {
    String value;
    if (price < 0 && price > -1 || price > 0 && price < 1) {
      value = formatNumberWithFourDecimals(price);
    } else {
      value = formatNumberWithTwoDecimals(price);
    }
    final NumberFormat nfFour = NumberFormat('#,##0.0000', 'en_US');
    final NumberFormat nfTwo = NumberFormat('#,##0.00', 'en_US');
    double val = double.parse(value);
    if (val == 0) {
      return '0.00'.toCurrency();
    } else if (val >= 1) {
      return nfTwo.format(val.abs()).toCurrency();
    } else if (val > 0 && val < 0.0001) {
      return '0.00'.toCurrency();
    } else if (val < 0 && val > -0.0001) {
      return '0.00'.toCurrency();
    } else if (val > 0 && val < 1) {
      return nfFour.format(val.abs()).toCurrency();
    } else if (val < 0 && val > -1) {
      return '-${nfFour.format(val.abs())}'.toCurrency();
    } else if (val <= -1) {
      return '-${nfTwo.format(val.abs())}'.toCurrency();
    } else {
      return '${val.abs()}'.toCurrency();
    }
  }

  //Jan, 1
  static formatDateMonthDay(String dts) {
    final dt = DateTime.parse(dts);
    final m = DateFormat('MMM').format(dt);
    return '$m ${dt.day}';
  }

  //Jan
  static formatMonth(String dts) {
    DateTime dt = DateFormat("yyyy-MM-dd hh:mm:ss").parse(dts);
    final DateFormat fm = DateFormat('MMM');
    return fm.format(dt);
  }

  //2020-01-01
  static formatDateGeneral(String dts) {
    DateTime dt = DateFormat("yyyy-MM-dd hh:mm:ss").parse(dts);
    final DateFormat fm = DateFormat('yyyy-MM-dd');
    return fm.format(dt);
  }

  //00-00-00
  static formatTimeGeneral(String dts) {
    DateTime dt = DateFormat("yyyy-MM-dd hh:mm:ss").parse(dts);
    final DateFormat fm = DateFormat('hh:mm:ss');
    return fm.format(dt);
  }

  static formatDay(String dts) {
    DateTime dt = DateFormat("yyyy-MM-dd hh:mm:ss").parse(dts);
    return dt.day;
  }

  static parseHtmlString(String htmlString) {
    final document = parse(htmlString);
    if (document.body?.text != null) {
      return parse(document.body!.text).documentElement!.text;
    }
    return "";
  }

  static String formatNumberWithTwoDecimals(num number) {
    var truncatedNumber = number.toStringAsFixed(3);
    final decimalIndex = truncatedNumber.indexOf('.');
    if (decimalIndex != -1) {
      final decimalPlaces = truncatedNumber.substring(decimalIndex + 1);
      if (decimalPlaces.length > 2) {
        truncatedNumber = truncatedNumber.substring(0, decimalIndex + 3);
      }
    }
    return truncatedNumber;
  }

  static String formatNumberWithFourDecimals(num number) {
    if ((number > 0 && number < 0.001) || (number < 0 && number > -0.001)) {
      return number.toString();
    }
    var truncatedNumber = number.toStringAsFixed(5);
    final decimalIndex = truncatedNumber.indexOf('.');
    if (decimalIndex != -1) {
      final decimalPlaces = truncatedNumber.substring(decimalIndex + 1);
      if (decimalPlaces.length > 4) {
        truncatedNumber = truncatedNumber.substring(0, decimalIndex + 5);
      }
    }
    return truncatedNumber;
  }
}
