// import 'package:sf_app_v2/core/api/network/network_helper.dart';
// import 'package:sf_app_v2/core/common_function.dart';
// import 'package:sf_app_v2/core/constants/enums.dart';
// import 'package:sf_app_v2/core/routes/navigator.dart';
// import 'package:sf_app_v2/core/utils/log.dart';
// import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

// import '../services/tencent_push/tencent_push_service.dart';

// class TencentIMUtils {
//   static final timCoreInstance = TIMUIKitCore.getInstance();

//   static Future<void> initTencent({
//     required int sdkappid,
//     required String userid,
//     required String usersig,
//     required Function() onLoginSuccess,
//     required Function(int code, String error) onConnectFailed,
//   }) async {
//     await timCoreInstance.init(
//       sdkAppID: sdkappid,
//       loglevel: LogLevelEnum.V2TIM_LOG_ALL,
//       listener: V2TimSDKListener(
//         onConnectFailed: onConnectFailed,
//         onConnectSuccess: onLoginSuccess,
//         onConnecting: () {
//           logDev("", 'onConnecting', special: true);
//         },
//         onKickedOffline: () {
//           NetworkHelper.handleMessage(
//             'You have been logged out. Please login again.',
//             navigatorKey.currentContext!,
//             type: HandleTypes.customDialog,
//             snackBarType: SnackBarType.error,
//             onTap: () {
//               CommonFunctions.logoutUser(
//                 navigatorKey.currentContext!,
//                 isNavToMain: true,
//               );
//             },
//           );
//         },
//         onSelfInfoUpdated: (V2TimUserFullInfo info) {
//           logDev(info, 'onSelfInfoUpdated', special: true);
//         },
//         onUserSigExpired: () {
//           logDev("", 'onUserSigExpired', special: true);
//         },
//       ),
//     );
//     final res = await timCoreInstance.login(userID: userid, userSig: usersig);
//     TencentPushService().initialize();
//     if (res.desc == "ok") {
//       logDev("${res.toJson()}", "LOGIN SUCCESS", api: true);
//       onLoginSuccess();
//     } else {
//       logDev(res.toJson(), 'tencent login failed', error: true);
//     }
//   }

//   static Future<void> logoutTencent({
//     required Function() onLogoutSuccess,
//   }) async {
//     final res = await timCoreInstance.logout();
//     if (res.desc == "ok") {
//       logDev("${res.toJson()}", "LOGOUT SUCCESS", api: true);
//       onLogoutSuccess();
//     } else {
//       logDev(res.toJson(), 'tencent logout failed', error: true);
//     }
//   }
// }
