//singleton for secure storage
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorageHelper {
  static final SecureStorageHelper _secureStorageHelper =
      SecureStorageHelper._internal();

  factory SecureStorageHelper() {
    return _secureStorageHelper;
  }

  SecureStorageHelper._internal();

  final _storage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
  );

  Future<void> writeSecureData(String key, String value) async {
    await _storage.write(
      key: key,
      value: value,
    );
  }

  Future<String?> readSecureData(String key) async {
    return await _storage.read(key: key);
  }

  Future<void> deleteSecureData(String key) async {
    await _storage.delete(key: key);
  }

  Future<void> deleteAllSecureData() async {
    await _storage.deleteAll();
  }
}
