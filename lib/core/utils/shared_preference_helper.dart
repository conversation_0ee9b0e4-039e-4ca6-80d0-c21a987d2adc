import 'dart:developer';

import 'package:sf_app_v2/core/constants/keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferenceHelper {
  static late SharedPreferences? preference;

  //instance
  Future getInit() async {
    preference = await SharedPreferences.getInstance();
  }

  //get keys
  getKeys() {
    return preference?.getKeys();
  }

  //remove by key
  remove(key) {
    preference!.remove(key);
  }

  // write by key
  writeData(String key, String value) async {
    await preference!.setString(key, value);
    log('pref writeData $value');
  }

  // write bool by key
  writeBoolData(String key, bool value) async {
    await preference!.setBool(key, value);
  }

  // read bool by key
  readBoolData(String key) {
    bool? value = preference!.getBool(key);
    log('$value');
    return value;
  }

  // read by key
  readData(String key) {
    String? value = preference!.getString(key);
    log('$value');
    return value;
  }

  // delete by key
  deleteData(String key) async {
    await preference!.remove(key);
  }

  //clear all data
  clearAll() async {
    await preference!.clear();
  }

  //check logged in
  bool? getIsLoggedIn() {
    bool? value = preference!.getBool(SharedPreferencesKeys.isLoggedIn);
    if (value != null) {
      return value;
    } else {
      return false;
    }
  }

  bool? isAccountStatusLogged() {
    bool? value =
        preference?.getBool(SharedPreferencesKeys.isAccountStatusLogged);
    if (value != null) {
      return value;
    } else {
      return false;
    }
  }

  bool? getIsWalletPasswordSet() {
    bool? value =
        preference!.getBool(SharedPreferencesKeys.isWalletPasswordSet);
    if (value != null) {
      return value;
    } else {
      return false;
    }
  }

  bool? getIsOnBoard() {
    bool? value = preference!.getBool(SharedPreferencesKeys.isOnBoard);
    if (value != null) {
      return value;
    } else {
      return false;
    }
  }
}
