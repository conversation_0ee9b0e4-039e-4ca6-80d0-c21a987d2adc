import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/news/logic/news/news_cubit.dart';
import 'package:sf_app_v2/features/news/domain/models/news_update_model.dart';
import '../../../core/dependency_injection/injectable.dart';
import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';

class NewsDetailsScreen extends StatelessWidget {
  final int id;

  const NewsDetailsScreen({super.key, required this.id});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
      ),
      body: BlocProvider(
        create: (context) => getIt<NewsCubit>()..getNewsUpdate(id: id),
        child: BlocBuilder<NewsCubit, NewsState>(
          builder: (context, state) {
            if (state.newsFetchStatus == DataStatus.loading) {
              return _buildLoadingIndicator();
            } else if (state.newsFetchStatus == DataStatus.failed) {
              return _buildErrorState(state.error);
            } else if (state.newsItemData == null) {
              return _buildEmptyState();
            }

            return _buildContent(state.newsItemData!, context);
          },
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildErrorState(String? error) {
    return Center(child: Text(error ?? 'An error occurred'));
  }

  Widget _buildEmptyState() {
    return const Center(child: Text('No news available'));
  }

  Widget _buildContent(NewsItemData newsItem, BuildContext context) {
    return SafeArea(
      child: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTitle(newsItem),
            _buildDate(newsItem),
            _buildHeroImage(newsItem),
            _buildContentText(newsItem, context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeroImage(NewsItemData newsItem) {
    return Hero(
      tag: newsItem.id,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        width: 1.sw,
        height: 260.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.r),
          image: DecorationImage(
            image: NetworkImage(newsItem.imgUrl),
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }

  Widget _buildDate(NewsItemData newsItem) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        DateFormat('yyyy-MM-dd HH:mm:ss').format(newsItem.createTime),
        style: FontPalette.normal14,
      ),
    );
  }

  Widget _buildTitle(NewsItemData newsItem) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        newsItem.title,
        style: FontPalette.bold18,
        maxLines: 3,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildContentText(NewsItemData newsItem, BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: HtmlWidget(
         newsItem.content,
      ),
    );
  }
}
