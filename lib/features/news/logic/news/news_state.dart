part of 'news_cubit.dart';

class NewsState extends Equatable {
  final DataStatus newsFetchStatus;
  final NewsItemData? newsItemData;
  final String? error;
  const NewsState({
    this.newsFetchStatus = DataStatus.idle,
    this.newsItemData,
    this.error,
  });

  @override
  List<Object?> get props => [newsFetchStatus, newsItemData];

  NewsState copyWith({
    DataStatus? newsFetchStatus,
    NewsItemData? newsItemData,
    String? error,
  }) {
    return NewsState(
      newsFetchStatus: newsFetchStatus ?? this.newsFetchStatus,
      newsItemData: newsItemData ?? this.newsItemData,
      error: error ?? this.error,
    );
  }
}
