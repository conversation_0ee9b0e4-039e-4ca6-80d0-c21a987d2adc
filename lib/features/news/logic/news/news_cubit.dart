import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';

import '../../domain/models/news_update_model.dart';
import '../../domain/repository/news_repository.dart';

part 'news_state.dart';

@injectable
class NewsCubit extends Cubit<NewsState> {
  final NewsRepository _newsService;
  NewsCubit(this._newsService) : super(const NewsState());

  Future<void> getNewsUpdate({required int id}) async {
    emit(state.copyWith(newsFetchStatus: DataStatus.loading));
    try {
      final result = await _newsService.getNewsUpdate(id: id);
      if (result.data != null) {
        emit(
          state.copyWith(
            newsItemData: result.data?.data,
            newsFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            newsFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } catch (e) {
      emit(state.copyWith(newsFetchStatus: DataStatus.failed));
    }
  }
}
