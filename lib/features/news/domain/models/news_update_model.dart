import 'package:freezed_annotation/freezed_annotation.dart';

part 'news_update_model.freezed.dart';
part 'news_update_model.g.dart';

@freezed
class NewsItem with _$NewsItem {
  const factory NewsItem({
    required int code,
    required NewsItemData data,
    required String msg,
  }) = _NewsItem;

  factory NewsItem.fromJson(Map<String, dynamic> json) =>
      _$NewsItemFromJson(json);
}

@freezed
class NewsItemData with _$NewsItemData {
  const factory NewsItemData({
    required String content,
    required String coverUrl,
    required DateTime createTime,
    required int id,
    required String imgUrl,
    dynamic modifiedTime,
    required String remark,
    required int status,
    required String title,
    required int type,
  }) = _NewsItemData;

  factory NewsItemData.fromJson(Map<String, dynamic> json) =>
      _$NewsItemDataFromJson(json);
}
