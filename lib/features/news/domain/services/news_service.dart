import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/endpoint/api_endpoints.dart';
import '../../../../core/api/network/network.dart';
import '../../../../core/models/result.dart';
import '../models/news_update_model.dart';
import '../repository/news_repository.dart';

/// Service class that implements the NewsRepository interface to handle news-related API calls
@Injectable(as: NewsRepository)
class NewsService implements NewsRepository {
  /// Fetches a specific news update item by ID
  /// [id] - ID of the news item to fetch
  /// Returns a [ResponseResult] containing [NewsItem] on success
  @override
  Future<ResponseResult<NewsItem>> getNewsUpdate({required int id}) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.newsUpdateItem}?id=$id',
        options: Options(
          headers: {'auth': false},
        ),
        force: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: NewsItem.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get balance');
      }
    } on DioException catch (e) {
      log(e.error.toString());
      return ResponseResult(error: e.error.toString());
    }
  }
}
