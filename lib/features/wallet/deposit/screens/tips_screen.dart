import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

import '../../../../core/constants/string_constants.dart';
import '../../../../core/theme/font_pallette.dart';

class TipsBottomSheet extends StatelessWidget {
  const TipsBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      decoration: BoxDecoration(
        color: myColorScheme(context).backgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    Icons.close,
                    color: myColorScheme(context).titleColor,
                    size: 24.r,
                  ),
                ),
              ],
            ),
          ),
          // Divider
          Divider(
            height: 1.h,
            color: myColorScheme(context).titleColor?.withValues(alpha: 0.1),
          ),
          // Content
          Flexible(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    StringConstants.whatIsATransactionHash.tr(),
                    style: FontPalette.bold16.copyWith(
                      color: myColorScheme(context).titleColor,
                    ),
                  ),
                  12.verticalSpace,
                  Text(
                    StringConstants.whatIsATransactionHashDescription.tr(),
                    style: FontPalette.normal14.copyWith(
                      color: myColorScheme(context).titleColor,
                    ),
                  ),
                  16.verticalSpace,
                  Text(
                    StringConstants.erc20_title.tr(),
                    style: FontPalette.bold14.copyWith(
                      color: myColorScheme(context).titleColor,
                    ),
                  ),
                  8.verticalSpace,
                  Text(
                    StringConstants.erc20_description.tr(),
                    style: FontPalette.normal14.copyWith(
                      color: myColorScheme(context).titleColor,
                    ),
                  ),
                  16.verticalSpace,
                  Text(
                    StringConstants.trc20_title.tr(),
                    style: FontPalette.bold14.copyWith(
                      color: myColorScheme(context).titleColor,
                    ),
                  ),
                  8.verticalSpace,
                  Text(
                    StringConstants.trc20_description.tr(),
                    style: FontPalette.normal14.copyWith(
                      color: myColorScheme(context).titleColor,
                    ),
                  ),
                  16.verticalSpace,
                  Text(
                    StringConstants.transactionHashDescription.tr(),
                    style: FontPalette.normal14.copyWith(
                      color: myColorScheme(context).titleColor,
                    ),
                  ),
                  8.verticalSpace,
                  Text(
                    StringConstants.transactionHashDescription2.tr(),
                    style: FontPalette.normal14.copyWith(
                      color: myColorScheme(context).titleColor,
                    ),
                  ),
                  20.verticalSpace,
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12.r),
                      child: Image.asset(
                        'assets/images/tips.jpg',
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            height: 300.h,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: myColorScheme(context).cardColor,
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  size: 48.r,
                                  color: myColorScheme(context).primaryColor,
                                ),
                                16.verticalSpace,
                                Text(
                                  'Unable to load tips image',
                                  style: TextStyle(
                                    color: myColorScheme(context).titleColor,
                                    fontSize: 16.sp,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Bottom padding for safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom + 16.h),
        ],
      ),
    );
  }
}
