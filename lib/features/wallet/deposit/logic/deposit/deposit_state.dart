part of 'deposit_cubit.dart';

class DepositState extends Equatable {
  final DataStatus depositFetchStatus;
  final DepositWalletData? depositWalletData;
  final DataStatus walletRechargeFetchStatus;
  final DataStatus createPayOrderFetchStatus;
  final WalletRechargeAddressData? walletRechargeAddress;
  final DataStatus walletCoinsFetchStatus;
  final List<WalletCoinData>? walletCoins;  
  final PaymentTypeModel? paymentType;
  final int? otpTimer;

  final String? error;

  const DepositState({
    this.depositWalletData,
    this.walletRechargeAddress,
    this.paymentType,
    this.depositFetchStatus = DataStatus.idle,
    this.walletRechargeFetchStatus = DataStatus.idle,
    this.createPayOrderFetchStatus = DataStatus.idle,
    this.walletCoinsFetchStatus = DataStatus.idle,
    this.walletCoins,
    this.otpTimer,
    this.error,
  });

  @override
  List<Object?> get props => [
        depositWalletData,
        walletRechargeAddress,
        paymentType,
        depositFetchStatus,
        walletRechargeFetchStatus,
        otpTimer,
        error,
        createPayOrderFetchStatus,
        walletCoinsFetchStatus,
        walletCoins,
      ];

  DepositState copyWith({
    DepositWalletData? depositWalletData,
    WalletRechargeAddressData? walletRechargeAddress,
    PaymentTypeModel? paymentType,
    DataStatus? depositFetchStatus,
    DataStatus? walletRechargeFetchStatus,
    int? otpTimer,
    String? error,
    DataStatus? createPayOrderFetchStatus,
    DataStatus? walletCoinsFetchStatus,
    List<WalletCoinData>? walletCoins,
  }) {
    return DepositState(
      depositWalletData: depositWalletData ?? this.depositWalletData,
      walletRechargeAddress:
          walletRechargeAddress ?? this.walletRechargeAddress,
      paymentType: paymentType ?? this.paymentType,
      depositFetchStatus: depositFetchStatus ?? this.depositFetchStatus,
      walletRechargeFetchStatus:
          walletRechargeFetchStatus ?? this.walletRechargeFetchStatus,
      otpTimer: otpTimer ?? this.otpTimer,
      error: error ?? this.error,
      createPayOrderFetchStatus:
          createPayOrderFetchStatus ?? this.createPayOrderFetchStatus,
      walletCoinsFetchStatus:
          walletCoinsFetchStatus ?? this.walletCoinsFetchStatus,
      walletCoins: walletCoins ?? this.walletCoins,
    );
  }
}
