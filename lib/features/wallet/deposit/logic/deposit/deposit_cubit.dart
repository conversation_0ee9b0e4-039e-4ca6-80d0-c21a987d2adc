import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/wallet/deposit/domain/models/deposit_balance/deposit_balance_model.dart';
import 'package:sf_app_v2/features/wallet/deposit/domain/repository/deposit_repository.dart';

import '../../../../../core/config/feature_configs/payment_feature_config.dart';
import '../../../../../core/dependency_injection/injectable.dart';
import '../../../../../core/models/app_config/payment_type_model.dart';
import '../../domain/models/wallet_coin/wallet_coin.dart';
import '../../domain/models/wallet_recharge/wallet_recharge_address.dart';

part 'deposit_state.dart';

@injectable
class DepositCubit extends Cubit<DepositState> {
  final DepositRepository _depositService;
  DepositCubit(this._depositService) : super(const DepositState());

  Future<void> fetchFundingBalance() async {
    emit(state.copyWith(depositFetchStatus: DataStatus.loading));
    try {
      final result = await _depositService.depositCash();
      if (result.data != null) {
        emit(
          state.copyWith(
            depositFetchStatus: DataStatus.success,
            depositWalletData: result.data?.data,
          ),
        );
      } else {
        emit(
          state.copyWith(
            depositFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          depositFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> fetchWalletRechargeAddress(PaymentTypeModel paymentType) async {
    // For SIS flavor, ensure wallet coins are loaded before proceeding
    final paymentService = getIt<PaymentFeatureService>();
    if (paymentService.shouldFetchWalletCoinsFromApi) {
      if (state.walletCoinsFetchStatus != DataStatus.success ||
          state.walletCoins == null ||
          state.walletCoins!.isEmpty) {
        // Don't proceed if wallet coins are not loaded for SIS flavor
        return;
      }
    }

    emit(
      state.copyWith(
        walletRechargeFetchStatus: DataStatus.loading,
        otpTimer: 1,
      ),
    );
    try {
      final result = await _depositService.walletRechargeAddress(
        paymentType: paymentType.code,
      );
      if (result.data != null) {
        emit(
          state.copyWith(
            otpTimer: 1800,
            walletRechargeFetchStatus: DataStatus.success,
            walletRechargeAddress: result.data?.data,
          ),
        );
      } else {
        emit(
          state.copyWith(
            otpTimer: 1800,
            walletRechargeFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          otpTimer: 1800,
          walletRechargeFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  void setPaymentType(
    PaymentTypeModel paymentType,
  ) {
    // For SIS flavor, ensure wallet coins are loaded before proceeding
    final paymentService = getIt<PaymentFeatureService>();
    if (paymentService.shouldFetchWalletCoinsFromApi) {
      if (state.walletCoinsFetchStatus != DataStatus.success ||
          state.walletCoins == null ||
          state.walletCoins!.isEmpty) {
        // Don't proceed if wallet coins are not loaded for SIS flavor
        return;
      }
      emit(state.copyWith(paymentType: paymentType));
      fetchWalletRechargeAddress(paymentType);
      return;
    }

    // Extract network type from payment code using PaymentService
    emit(state.copyWith(paymentType: paymentType));
    fetchWalletRechargeAddress(paymentType);
  }

  Future<void> createPayOrder({
    required String trxHash,
  }) async {
    emit(state.copyWith(createPayOrderFetchStatus: DataStatus.loading));
    try {
      var response = await _depositService.createPayOrder(
        address: state.walletRechargeAddress?.address ?? '',
        tokenStandard: state.paymentType?.code ?? 'TRC20',
        trxHash: trxHash.trim(),
      );
      if (response.data == true) {
        emit(
          state.copyWith(
            createPayOrderFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            createPayOrderFetchStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          createPayOrderFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> fetchWalletCoins() async {
    emit(state.copyWith(walletCoinsFetchStatus: DataStatus.loading));
    try {
      var response = await _depositService.walletCoins();
      if (response.data != null) {
        emit(
          state.copyWith(
              walletCoinsFetchStatus: DataStatus.success,
              walletCoins: response.data?.data),
        );

        // Auto-set default payment type after wallet coins are loaded for SIS flavor
        _setDefaultPaymentTypeAfterWalletCoinsLoad();
      } else {
        emit(
          state.copyWith(
            walletCoinsFetchStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          walletCoinsFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  /// Sets the default payment type after wallet coins are loaded for SIS flavor
  void _setDefaultPaymentTypeAfterWalletCoinsLoad() {
    final paymentService = getIt<PaymentFeatureService>();
    if (paymentService.shouldFetchWalletCoinsFromApi &&
        state.walletCoins != null &&
        state.walletCoins!.isNotEmpty) {
      final defaultPaymentType = paymentService.getDefaultPaymentType(
        apiWalletCoins: state.walletCoins,
      );

      if (defaultPaymentType != null) {
        emit(state.copyWith(paymentType: defaultPaymentType));
        fetchWalletRechargeAddress(defaultPaymentType);
      }
    }
  }

  /// Sets the default payment type for non-SIS flavors
  void setDefaultPaymentTypeNonSis() {
    final paymentService = getIt<PaymentFeatureService>();
    if (!paymentService.shouldFetchWalletCoinsFromApi) {
      final defaultPaymentType = paymentService.getDefaultPaymentType();
      if (defaultPaymentType != null) {
        emit(state.copyWith(paymentType: defaultPaymentType));
        fetchWalletRechargeAddress(defaultPaymentType);
      }
    }
  }
}
