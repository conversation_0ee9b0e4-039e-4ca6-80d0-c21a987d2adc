
// To parse this JSON data, do
//
//     final walletCoin = walletCoinFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'wallet_coin.freezed.dart';
part 'wallet_coin.g.dart';

WalletCoin walletCoinFromJson( str) => WalletCoin.fromJson((str));

String walletCoinToJson(WalletCoin data) => json.encode(data.toJson());

@freezed
class WalletCoin with _$WalletCoin {
    const factory WalletCoin({
        int? code,
        List<WalletCoinData>? data,
        String? msg,
    }) = _WalletCoin;

    factory WalletCoin.fromJson(Map<String, dynamic> json) => _$WalletCoinFromJson(json);
}

@freezed
class WalletCoinData with _$WalletCoinData {
    const factory WalletCoinData({
        String? name,
        String? code,
        int? id,
    }) = _WalletCoinData;

    factory WalletCoinData.fromJson(Map<String, dynamic> json) => _$WalletCoinDataFromJson(json);
}
