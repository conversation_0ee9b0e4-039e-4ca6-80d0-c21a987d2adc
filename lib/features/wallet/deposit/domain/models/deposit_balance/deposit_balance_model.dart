import 'package:freezed_annotation/freezed_annotation.dart';

part 'deposit_balance_model.freezed.dart';
part 'deposit_balance_model.g.dart';

@freezed
class DepositBalanceModel with _$DepositBalanceModel {
  const factory DepositBalanceModel({
    required int code,
    required DepositWalletData data,
    required String msg,
  }) = _DepositBalanceModel;

  factory DepositBalanceModel.fromJson(Map<String, dynamic> json) =>
      _$DepositBalanceModelFromJson(json);
}

@freezed
class DepositWalletData with _$DepositWalletData {
  const factory DepositWalletData({
    required String balance,
  }) = _DepositWalletData;

  factory DepositWalletData.fromJson(Map<String, dynamic> json) =>
      _$DepositWalletDataFromJson(json);
}
