import 'package:freezed_annotation/freezed_annotation.dart';

part 'wallet_recharge_address.freezed.dart';
part 'wallet_recharge_address.g.dart';

@freezed
class WalletRechargeAddress with _$WalletRechargeAddress {
  const factory WalletRechargeAddress({
    required int code,
    required WalletRechargeAddressData data,
    required String msg,
  }) = _WalletRechargeAddress;

  factory WalletRechargeAddress.fromJson(Map<String, dynamic> json) =>
      _$WalletRechargeAddressFromJson(json);
}

@freezed
class WalletRechargeAddressData with _$WalletRechargeAddressData {
  const factory WalletRechargeAddressData({
    required String address,
    @J<PERSON><PERSON><PERSON>(name: "qrcode") required String qrCode,
    required int timestamp,
    required String token,
  }) = _WalletRechargeAddressData;

  factory WalletRechargeAddressData.fromJson(Map<String, dynamic> json) =>
      _$WalletRechargeAddressDataFromJson(json);
}
