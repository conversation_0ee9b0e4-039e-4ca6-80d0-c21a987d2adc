import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/api/endpoint/api_endpoints.dart';
import 'package:sf_app_v2/core/api/network/network.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/core/config/feature_configs/payment_feature_config.dart';
import 'package:sf_app_v2/core/dependency_injection/injectable.dart';

import 'package:sf_app_v2/features/wallet/deposit/domain/models/deposit_balance/deposit_balance_model.dart';
import 'package:sf_app_v2/features/wallet/deposit/domain/models/wallet_recharge/wallet_recharge_address.dart';

import '../models/wallet_coin/wallet_coin.dart';
import '../repository/deposit_repository.dart';

/// Service class that implements [DepositRepository] to handle deposit-related operations
@Injectable(as: DepositRepository)
class DepositService implements DepositRepository {
  /// Fetches the deposit cash wallet balance
  ///
  /// Returns [ResponseResult] containing [DepositBalanceModel] on success
  @override
  Future<ResponseResult<DepositBalanceModel>> depositCash() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.cashWallet,
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
            data: DepositBalanceModel.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch deposit balance');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches the wallet recharge address for a given payment type
  ///
  /// [paymentType] - Type of payment to get recharge address for
  /// Returns [ResponseResult] containing [WalletRechargeAddress] on success
  @override
  Future<ResponseResult<WalletRechargeAddress>> walletRechargeAddress({
    required dynamic paymentType,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.walletRechargeAddress(paymentType),
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
            data: WalletRechargeAddress.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch wallet recharge address');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Creates a new payment order
  ///
  /// [address] - Wallet address for payment
  /// [tokenStandard] - Token standard to use
  /// [trxHash] - Transaction hash
  /// Returns [ResponseResult] containing boolean success status
  @override
  Future<ResponseResult<bool>> createPayOrder({
    required String address,
    required String tokenStandard,
    required String trxHash,
  }) async {
    try {
      final response = await NetworkProvider().post(
        ApiEndpoints.createPayOrder,
        data: {
          'address': address,
          'tokenStandard': tokenStandard,
          'trxHash': trxHash
        },
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to create pay order');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  /// Fetches available wallet coins (SIS flavor only)
  ///
  /// Returns [ResponseResult] containing [WalletCoin] on success
  /// Returns error if not in SIS flavor
  @override
  Future<ResponseResult<WalletCoin>> walletCoins() async {
    // Only make API call for SIS flavor
    final paymentService = getIt<PaymentFeatureService>();
    if (!paymentService.shouldFetchWalletCoinsFromApi) {
      return ResponseResult(
        error: 'Wallet coins API is only available for SIS flavor',
      );
    }

    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.walletCoins,
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          // Transform the API response to match our model structure
          final transformedResponse = {
            'code': response.data['code'],
            'data': response.data['data']['walletCoins'], // Extract walletCoins array
            'msg': response.data['msg'],
          };
          return ResponseResult(data: WalletCoin.fromJson(transformedResponse));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch wallet coins');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }
}
