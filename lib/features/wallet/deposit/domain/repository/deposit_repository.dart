import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/wallet/deposit/domain/models/deposit_balance/deposit_balance_model.dart';
import 'package:sf_app_v2/features/wallet/deposit/domain/models/wallet_recharge/wallet_recharge_address.dart';

import '../models/wallet_coin/wallet_coin.dart';

abstract class DepositRepository {
  const DepositRepository();

  Future<ResponseResult<DepositBalanceModel>> depositCash();
  Future<ResponseResult<WalletRechargeAddress>> walletRechargeAddress({
    required dynamic paymentType,
  });
   Future<ResponseResult<bool>> createPayOrder({
    required String address,
    required String tokenStandard,
    required String trxHash,
  });
  Future<ResponseResult<WalletCoin>> walletCoins();
}
