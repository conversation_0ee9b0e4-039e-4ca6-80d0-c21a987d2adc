import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';

class DepositScreenShimmer extends StatelessWidget {
  const DepositScreenShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Column(
              children: [
                CommonShimmer(
                  width: 377.w,
                  height: 205.h,
                  br: 20.r,
                ),
                34.verticalSpace,
                CommonShimmer(
                  width: 377.w,
                  height: 56.h,
                  br: 12.r,
                ),
              ],
            ),
          ),
          const Spacer(),
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(32.r),
                topLeft: Radius.circular(32.r),
              ),
              color: myColorScheme(context).cardColor,
            ),
            child: Column(
              children: [
                30.verticalSpace,
                CommonShimmer(
                  width: 150.r,
                  height: 150.r,
                  br: 5.r,
                ),
                20.verticalSpace,
                CommonShimmer(
                  width: 330.w,
                  height: 50.h,
                  br: 10.r,
                ),
                30.verticalSpace,
                CommonShimmer(
                  width: 1.sw,
                  height: 60.h,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
