import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

class CustomCurrencyDropDown<T> extends StatefulWidget {
  final List<CustomCurrencyDropDownMenuItem> items;
  final Function onChanged;
  final String hintText;
  final double borderRadius;
  final double borderWidth;
  final int defaultSelectedIndex;
  final bool enabled;

  const CustomCurrencyDropDown({
    required this.items,
    required this.onChanged,
    this.hintText = "",
    this.borderRadius = 0,
    this.borderWidth = 1,
    this.defaultSelectedIndex = -1,
    super.key,
    this.enabled = true,
  });

  @override
  CustomCurrencyDropDownState createState() => CustomCurrencyDropDownState();
}

class CustomCurrencyDropDownState extends State<CustomCurrencyDropDown>
    with WidgetsBindingObserver {
  bool _isOpen = false, _isAnyItemSelected = false, _isReverse = false;
  late OverlayEntry _overlayEntry;
  late RenderBox? _renderBox;
  String? _itemSelectedTitle;
  late Offset dropDownOffset;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          dropDownOffset = getOffset();
        });
      }
      if (widget.defaultSelectedIndex > -1) {
        if (widget.defaultSelectedIndex < widget.items.length) {
          if (mounted) {
            setState(() {
              _isAnyItemSelected = true;
              _itemSelectedTitle =
                  widget.items[widget.defaultSelectedIndex].title;
                  
              widget.onChanged(widget.items[widget.defaultSelectedIndex].value);
            });
          }
        }
      }
    });
    WidgetsBinding.instance.addObserver(this);
  }

  void _addOverlay() {
    if (mounted) {
      setState(() {
        _isOpen = true;
      });
    }

    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry);
  }

  void _removeOverlay() {
    if (mounted) {
      setState(() {
        _isOpen = false;
      });
      _overlayEntry.remove();
    }
  }

  @override
  dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  OverlayEntry _createOverlayEntry() {
    _renderBox = context.findRenderObject() as RenderBox?;

    var size = _renderBox!.size;

    dropDownOffset = getOffset();

    return OverlayEntry(
      maintainState: false,
      builder: (context) => Align(
        alignment: Alignment.center,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: dropDownOffset,
          child: SizedBox(
            width: size.width,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment:
                  _isReverse ? MainAxisAlignment.end : MainAxisAlignment.start,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: Container(
                    constraints: BoxConstraints(
                      maxWidth: size.width,
                      maxHeight: MediaQuery.of(context).size.height * 0.4, // Max 40% of screen height
                    ),
                    decoration: BoxDecoration(
                      color: myColorScheme(context).cardColor,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.all(
                        Radius.circular(widget.borderRadius),
                      ),
                      child: Material(
                        elevation: 0,
                        shadowColor: Colors.grey,
                        child: ListView(
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          children: widget.items
                              .map(
                                (item) => GestureDetector(
                                  child: Padding(
                                    padding: const EdgeInsets.all(0),
                                    child: item.child,
                                  ),
                                  onTap: () {
                                    if (mounted) {
                                      setState(() {
                                        _isAnyItemSelected = true;
                                        _itemSelectedTitle = item.title;
                                        _removeOverlay();
                                        widget.onChanged(item.value);
                                      });
                                    }
                                  },
                                ),
                              )
                              .toList(),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Offset getOffset() {
    RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    double y = renderBox!.localToGlobal(Offset.zero).dy;
    double spaceAvailable = _getAvailableSpace(y + renderBox.size.height);

    // Calculate estimated dropdown height based on items
    double estimatedDropdownHeight = _calculateEstimatedDropdownHeight();

    if (spaceAvailable > estimatedDropdownHeight) {
      _isReverse = false;
      return Offset(0, renderBox.size.height);
    } else {
      _isReverse = true;
      return Offset(
        0,
        renderBox.size.height - (estimatedDropdownHeight + renderBox.size.height),
      );
    }
  }

  double _calculateEstimatedDropdownHeight() {
    // Estimate height based on number of items
    // Assuming each item is approximately 48-56 pixels high
    double itemHeight = 52.0;
    double padding = 20.0; // Top padding
    double maxHeight = MediaQuery.of(context).size.height * 0.4;

    double estimatedHeight = (widget.items.length * itemHeight) + padding;
    return estimatedHeight > maxHeight ? maxHeight : estimatedHeight;
  }

  double _getAvailableSpace(double offsetY) {
    double safePaddingTop = MediaQuery.of(context).padding.top;
    double safePaddingBottom = MediaQuery.of(context).padding.bottom;

    double screenHeight =
        MediaQuery.of(context).size.height - safePaddingBottom - safePaddingTop;

    return screenHeight - offsetY;
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: GestureDetector(
        onTap: widget.enabled
            ? () {
                _isOpen ? _removeOverlay() : _addOverlay();
              }
            : null,
        child: Container(
          decoration: _getDecoration(),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Flexible(
                flex: 3,
                child: _isAnyItemSelected
                    ? Padding(
                        padding: const EdgeInsets.only(left: 20.0),
                        child: Text(
                          _itemSelectedTitle?.split(' ')[0] ?? '',
                          style:
                              FontPalette.bold18.copyWith(color: Colors.white),
                        ),
                      )
                    : Padding(
                        padding:
                            const EdgeInsets.only(left: 4.0), // change it here
                        child: Text(
                          widget.hintText,
                          maxLines: 1,
                          overflow: TextOverflow.clip,
                        ),
                      ),
              ),
              Flexible(
                flex: 1,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(0, 0, 10, 0).r,
                  child: const Icon(
                    Icons.arrow_drop_down,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _getDecoration() {
    if (_isOpen && !_isReverse) {
      return BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(widget.borderRadius),
          topRight: Radius.circular(
            widget.borderRadius,
          ),
        ),
      );
    } else if (_isOpen && _isReverse) {
      return BoxDecoration(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(widget.borderRadius),
          bottomRight: Radius.circular(
            widget.borderRadius,
          ),
        ),
      );
    } else if (!_isOpen) {
      return BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(widget.borderRadius)),
      );
    }
  }
}

class CustomCurrencyDropDownMenuItem<T> extends StatelessWidget {
  final T value;
  final Widget child;
  final String title;

  const CustomCurrencyDropDownMenuItem({
    super.key,
    required this.value,
    required this.child,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink();
  }
}
