import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/models/app_config/payment_type_model.dart';

import '../../../../core/theme/font_pallette.dart';
import '../../../../core/theme/my_color_scheme.dart';

class PaymentTypeSelectionBottomSheet extends StatelessWidget {
  final List<PaymentTypeModel> items;
  final Function(PaymentTypeModel) onPaymentTypeSelected;
  final PaymentTypeModel? selectedPaymentType;

  const PaymentTypeSelectionBottomSheet({
    super.key,
    required this.items,
    required this.onPaymentTypeSelected,
    this.selectedPaymentType,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context),
          _buildDivider(context),
          _buildPaymentTypeList(context),
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(
              Icons.close,
              color: myColorScheme(context).titleColor,
              size: 24.r,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Divider(
      height: 1.h,
      color: myColorScheme(context).titleColor?.withValues(alpha: 0.1),
    );
  }

  Widget _buildPaymentTypeList(BuildContext context) {
    return Flexible(
      child: ListView.separated(
        shrinkWrap: true,
        padding: EdgeInsets.symmetric(vertical: 8.h),
        itemCount: items.length,
        separatorBuilder: (context, index) => Divider(
          height: 1.h,
          color: myColorScheme(context).titleColor?.withValues(alpha: 0.05),
        ),
        itemBuilder: (context, index) {
          final item = items[index];
          final isSelected = selectedPaymentType?.id == item.id;

          return InkWell(
            onTap: () {
              onPaymentTypeSelected(item);
              Navigator.pop(context);
            },
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
              color: isSelected
                ? myColorScheme(context).primaryColor?.withValues(alpha: 0.1)
                : Colors.transparent,
              child: Row(
                children: [
                  12.horizontalSpace,
                  Expanded(
                    child: Text(
                      item.name,
                      style: FontPalette.medium16.copyWith(
                        color: isSelected
                          ? myColorScheme(context).primaryColor
                          : myColorScheme(context).titleColor,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: myColorScheme(context).primaryColor,
                      size: 20.r,
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
