part of 'records_cubit.dart';

class RecordsState extends Equatable {
  final List<RecordData> depositRecords;
  final List<RecordData> profitRecords;
  final List<RecordData> communityRecords;
  final List<RecordData> collectionRecords;
  final List<RecordData> bonusRecords;
  final List<RecordData> fundingRecords;
  final DataStatus depositRecordsFetchStatus;
  final DataStatus profitRecordsFetchStatus;
  final DataStatus communityRecordsFetchStatus;
  final DataStatus collectionRecordsFetchStatus;
  final DataStatus bonusRecordsFetchStatus;
  final DataStatus fundingRecordsFetchStatus;
  final int? totalDepositRecords;
  final int? totalProfitRecords;
  final int? totalCommunityRecords;
  final int? totalCollectionRecords;
  final int? totalBonusRecords;
  final int? totalFundingRecords;
  final int pageNumDepositRecords;
  final int pageNumProfitRecords;
  final int pageNumCommunityRecords;
  final int pageNumCollectionRecords;
  final int pageNumBonusRecords;
  final int pageNumFundingRecords;
  final String? error;

  const RecordsState({
    this.depositRecords = const [],
    this.profitRecords = const [],
    this.communityRecords = const [],
    this.collectionRecords = const [],
    this.bonusRecords = const [],
    this.fundingRecords = const [],
    this.totalDepositRecords,
    this.totalProfitRecords,
    this.totalCommunityRecords,
    this.totalCollectionRecords,
    this.totalBonusRecords,
    this.totalFundingRecords,
    this.pageNumDepositRecords = 0,
    this.pageNumProfitRecords = 0,
    this.pageNumCommunityRecords = 0,
    this.pageNumCollectionRecords = 0,
    this.pageNumBonusRecords = 0,
    this.pageNumFundingRecords = 0,
    this.depositRecordsFetchStatus = DataStatus.idle,
    this.profitRecordsFetchStatus = DataStatus.idle,
    this.communityRecordsFetchStatus = DataStatus.idle,
    this.collectionRecordsFetchStatus = DataStatus.idle,
    this.bonusRecordsFetchStatus = DataStatus.idle,
    this.fundingRecordsFetchStatus = DataStatus.idle,
    this.error,
  });

  @override
  List<Object?> get props => [
        depositRecords,
        profitRecords,
        communityRecords,
        collectionRecords,
        fundingRecords,
        depositRecordsFetchStatus,
        profitRecordsFetchStatus,
        communityRecordsFetchStatus,
        collectionRecordsFetchStatus,
        fundingRecordsFetchStatus,
        error,
        bonusRecords,
        bonusRecordsFetchStatus,
        totalBonusRecords,
        pageNumBonusRecords,
        totalCollectionRecords,
        pageNumCollectionRecords,
        totalCommunityRecords,
        pageNumCommunityRecords,
        totalProfitRecords,
        pageNumProfitRecords,
        totalDepositRecords,
        pageNumDepositRecords,
        totalFundingRecords,
        pageNumFundingRecords,
      ];

  RecordsState copyWith({
    List<RecordData>? depositRecords,
    List<RecordData>? profitRecords,
    List<RecordData>? communityRecords,
    List<RecordData>? collectionRecords,
    List<RecordData>? bonusRecords,
    List<RecordData>? fundingRecords,
    DataStatus? depositRecordsFetchStatus,
    DataStatus? profitRecordsFetchStatus,
    DataStatus? communityRecordsFetchStatus,
    DataStatus? collectionRecordsFetchStatus,
    DataStatus? bonusRecordsFetchStatus,
    DataStatus? fundingRecordsFetchStatus,
    int? totalDepositRecords,
    int? totalProfitRecords,
    int? totalCommunityRecords,
    int? totalCollectionRecords,
    int? totalBonusRecords,
    int? totalFundingRecords,
    int? pageNumDepositRecords,
    int? pageNumProfitRecords,
    int? pageNumCommunityRecords,
    int? pageNumCollectionRecords,
    int? pageNumBonusRecords,
    int? pageNumFundingRecords,
    String? error,
  }) {
    return RecordsState(
      depositRecords: depositRecords ?? this.depositRecords,
      profitRecords: profitRecords ?? this.profitRecords,
      communityRecords: communityRecords ?? this.communityRecords,
      collectionRecords: collectionRecords ?? this.collectionRecords,
      bonusRecords: bonusRecords ?? this.bonusRecords,
      fundingRecords: fundingRecords ?? this.fundingRecords,
      depositRecordsFetchStatus:
          depositRecordsFetchStatus ?? this.depositRecordsFetchStatus,
      profitRecordsFetchStatus:
          profitRecordsFetchStatus ?? this.profitRecordsFetchStatus,
      communityRecordsFetchStatus:
          communityRecordsFetchStatus ?? this.communityRecordsFetchStatus,
      collectionRecordsFetchStatus:
          collectionRecordsFetchStatus ?? this.collectionRecordsFetchStatus,
      bonusRecordsFetchStatus:
          bonusRecordsFetchStatus ?? this.bonusRecordsFetchStatus,
      fundingRecordsFetchStatus:
          fundingRecordsFetchStatus ?? this.fundingRecordsFetchStatus,
      totalDepositRecords: totalDepositRecords ?? this.totalDepositRecords,
      totalProfitRecords: totalProfitRecords ?? this.totalProfitRecords,
      totalCommunityRecords:
          totalCommunityRecords ?? this.totalCommunityRecords,
      totalCollectionRecords:
          totalCollectionRecords ?? this.totalCollectionRecords,
      totalBonusRecords: totalBonusRecords ?? this.totalBonusRecords,
      totalFundingRecords: totalFundingRecords ?? this.totalFundingRecords,
      pageNumDepositRecords:
          pageNumDepositRecords ?? this.pageNumDepositRecords,
      pageNumProfitRecords: pageNumProfitRecords ?? this.pageNumProfitRecords,
      pageNumCommunityRecords:
          pageNumCommunityRecords ?? this.pageNumCommunityRecords,
      pageNumCollectionRecords:
          pageNumCollectionRecords ?? this.pageNumCollectionRecords,
      pageNumBonusRecords: pageNumBonusRecords ?? this.pageNumBonusRecords,
      pageNumFundingRecords: pageNumFundingRecords ?? this.pageNumFundingRecords,
      error: error ?? this.error,
    );
  }
}
