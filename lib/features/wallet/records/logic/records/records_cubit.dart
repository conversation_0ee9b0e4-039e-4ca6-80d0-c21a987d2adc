import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/wallet/records/domain/models/records_model.dart';
import 'package:sf_app_v2/features/wallet/records/domain/repository/records_repository.dart';

part 'records_state.dart';

@injectable
class RecordsCubit extends Cubit<RecordsState> {
  RecordsCubit(this._recordsService) : super(const RecordsState());
  final RecordsRepository _recordsService;

  Future<void> getDepositRecords({
    int page = 1,
    bool isLoadMore = false,
  }) async {
    if (!isLoadMore) {
      emit(state.copyWith(depositRecordsFetchStatus: DataStatus.loading));
    }
    try {
      final result =
          await _recordsService.getRecords(pageNum: page, type: 'deposit');
      if (result.data != null) {
        if (isLoadMore) {
          emit(
            state.copyWith(
              depositRecords: [
                ...state.depositRecords,
                ...result.data?.data?.list ?? [],
              ],
              pageNumDepositRecords: result.data?.data?.pageNum ?? 0,
              totalDepositRecords: result.data?.data?.total ?? 0,
            ),
          );
        } else {
          emit(
            state.copyWith(
              depositRecordsFetchStatus: DataStatus.success,
              depositRecords: result.data?.data?.list ?? [],
              pageNumDepositRecords: result.data?.data?.pageNum ?? 0,
              totalDepositRecords: result.data?.data?.total ?? 0,
            ),
          );
        }
        log('depositRecords: ${state.depositRecords.length}');
      } else {
        emit(
          state.copyWith(
            depositRecordsFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          depositRecordsFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> getProfitRecords({int page = 1, bool isLoadMore = false}) async {
    if (!isLoadMore) {
      emit(state.copyWith(profitRecordsFetchStatus: DataStatus.loading));
    }
    try {
      final result =
          await _recordsService.getRecords(pageNum: page, type: 'profit');
      if (result.data != null) {
        if (isLoadMore) {
          emit(
            state.copyWith(
              profitRecords: [
                ...state.profitRecords,
                ...result.data?.data?.list ?? [],
              ],
              pageNumProfitRecords: result.data?.data?.pageNum ?? 0,
              totalProfitRecords: result.data?.data?.total ?? 0,
            ),
          );
        } else {
          emit(
            state.copyWith(
              profitRecordsFetchStatus: DataStatus.success,
              profitRecords: result.data?.data?.list ?? [],
              pageNumProfitRecords: result.data?.data?.pageNum ?? 0,
              totalProfitRecords: result.data?.data?.total ?? 0,
            ),
          );
        }
        log('profitRecords: ${state.profitRecords.length}');
      } else {
        emit(
          state.copyWith(
            profitRecordsFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          profitRecordsFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> getCommunityRecords({
    int page = 1,
    bool isLoadMore = false,
  }) async {
    if (!isLoadMore) {
      emit(state.copyWith(communityRecordsFetchStatus: DataStatus.loading));
    }
    try {
      final result =
          await _recordsService.getRecords(pageNum: page, type: 'community');
      if (result.data != null) {
        if (isLoadMore) {
          emit(
            state.copyWith(
              communityRecords: isLoadMore
                  ? [
                      ...state.communityRecords,
                      ...result.data?.data?.list ?? [],
                    ]
                  : result.data?.data?.list ?? [],
              pageNumCommunityRecords: result.data?.data?.pageNum ?? 0,
              totalCommunityRecords: result.data?.data?.total ?? 0,
            ),
          );
        } else {
          emit(
            state.copyWith(
              communityRecordsFetchStatus: DataStatus.success,
              communityRecords: result.data?.data?.list ?? [],
              pageNumCommunityRecords: result.data?.data?.pageNum ?? 0,
              totalCommunityRecords: result.data?.data?.total ?? 0,
            ),
          );
        }
        log('communityRecords: ${state.communityRecords.length}');
      } else {
        emit(
          state.copyWith(
            communityRecordsFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          communityRecordsFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> getCollectionRecords({
    int page = 1,
    bool isLoadMore = false,
  }) async {
    if (!isLoadMore) {
      emit(state.copyWith(collectionRecordsFetchStatus: DataStatus.loading));
    }
    try {
      final result =
          await _recordsService.getRecords(pageNum: page, type: 'collection');
      if (result.data != null) {
        if (isLoadMore) {
          emit(
            state.copyWith(
              collectionRecords: isLoadMore
                  ? [
                      ...state.collectionRecords,
                      ...result.data?.data?.list ?? [],
                    ]
                  : result.data?.data?.list ?? [],
              pageNumCollectionRecords: result.data?.data?.pageNum ?? 0,
              totalCollectionRecords: result.data?.data?.total ?? 0,
            ),
          );
        } else {
          emit(
            state.copyWith(
              collectionRecordsFetchStatus: DataStatus.success,
              collectionRecords: result.data?.data?.list ?? [],
              pageNumCollectionRecords: result.data?.data?.pageNum ?? 0,
              totalCollectionRecords: result.data?.data?.total ?? 0,
            ),
          );
        }
        log('collectionRecords: ${state.collectionRecords.length}');
      } else {
        emit(
          state.copyWith(
            collectionRecordsFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          collectionRecordsFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> getBonusRecords({int page = 1, bool isLoadMore = false}) async {
    if (!isLoadMore) {
      emit(state.copyWith(bonusRecordsFetchStatus: DataStatus.loading));
    }
    try {
      final result =
          await _recordsService.getRecords(pageNum: page, type: 'bonus');
      if (result.data != null) {
        if (isLoadMore) {
          emit(
            state.copyWith(
              bonusRecords: isLoadMore
                  ? [...state.bonusRecords, ...result.data?.data?.list ?? []]
                  : result.data?.data?.list ?? [],
              pageNumBonusRecords: result.data?.data?.pageNum ?? 0,
              totalBonusRecords: result.data?.data?.total ?? 0,
            ),
          );
        } else {
          emit(
            state.copyWith(
              bonusRecordsFetchStatus: DataStatus.success,
              bonusRecords: result.data?.data?.list ?? [],
              pageNumBonusRecords: result.data?.data?.pageNum ?? 0,
              totalBonusRecords: result.data?.data?.total ?? 0,
            ),
          );
        }
        log('bonusRecords: ${state.bonusRecords.length}');
      } else {
        emit(
          state.copyWith(
            bonusRecordsFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          bonusRecordsFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }


   Future<void> getFundingRecords({
    int page = 1,
    bool isLoadMore = false,
  }) async {
    if (!isLoadMore) {
      emit(state.copyWith(fundingRecordsFetchStatus: DataStatus.loading));
    }
    try {
      final result =
          await _recordsService.getRecords(pageNum: page, type: 'cash');
      if (result.data != null) {
        if (isLoadMore) {
          emit(
            state.copyWith(
              fundingRecords: [
                ...state.fundingRecords,
                ...result.data?.data?.list ?? [],
              ],
              pageNumFundingRecords: result.data?.data?.pageNum ?? 0,
              totalFundingRecords: result.data?.data?.total ?? 0,
            ),
          );
        } else {
          emit(
            state.copyWith(
              fundingRecordsFetchStatus: DataStatus.success,
              fundingRecords: result.data?.data?.list ?? [],
              pageNumFundingRecords: result.data?.data?.pageNum ?? 0,
              totalFundingRecords: result.data?.data?.total ?? 0,
            ),
          );
        }
        log('fundingRecords: ${state.fundingRecords.length}');
      } else {
        emit(
          state.copyWith(
            fundingRecordsFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          fundingRecordsFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }
}
