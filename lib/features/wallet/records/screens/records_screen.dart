import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:sf_app_v2/core/config/app_config.dart';
import 'package:sf_app_v2/core/dependency_injection/injectable.dart';
import 'package:sf_app_v2/core/widgets/pagination_widget.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/wallet/wallet_cubit.dart';
import 'package:sf_app_v2/features/home/<USER>/home/<USER>';
import 'package:sf_app_v2/features/wallet/records/domain/models/records_model.dart';
import 'package:sf_app_v2/features/wallet/records/logic/records/records_cubit.dart';

import '../../../../core/common_function.dart';
import '../../../../core/constants/enums.dart';
import '../../../../core/constants/string_constants.dart';
import '../../../../core/theme/font_pallette.dart';
import '../../../../core/theme/my_color_scheme.dart';
import '../../../../core/utils/mixin/animation.dart';
import '../../../../core/widgets/common_appbar.dart';
import '../../../../core/widgets/common_empty_data.dart';
import '../../../../core/widgets/common_shimmer.dart';
import '../widgets/records_item.dart';
import '../widgets/wallet_slider_records.dart';

class RecordScreen extends StatefulWidget {
  const RecordScreen({super.key});

  @override
  State<RecordScreen> createState() => _RecordScreenState();
}

class _RecordScreenState extends State<RecordScreen> with AutomaticKeepAliveClientMixin, StaggeredAnimation {
  var onTap = false;
  var onCopied = false;
  final controller = TextEditingController();
  final emailController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    CommonFunctions.afterInit(_initialFunction);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  _initialFunction() {
    final recordsCubit = context.read<RecordsCubit>();
    recordsCubit.getFundingRecords();
    // Only fetch community records if not SIS flavor
    if (getIt<AppConfig>().fetchCommunityRecords) {
      recordsCubit.getCommunityRecords();
    }
  }

  _manageRefresh() {
    context.read<HomeCubit>().getBalance();
    int walletSliderIndex = context.read<WalletCubit>().state.recordSliderIndex;
    switch (walletSliderIndex) {
      case 0:
        context.read<RecordsCubit>().getFundingRecords();
        break;
      case 1:
        // Only fetch community records if not SIS flavor
        if (getIt<AppConfig>().fetchCommunityRecords) {
          context.read<RecordsCubit>().getCommunityRecords();
        }
        break;
      default:
    }
  }

  Widget _manageWidgetWalletList({
    required int walletSliderIndex,
    required DataStatus fundingRecordsFetchStatus,
    required DataStatus communityRecordsFetchStatus,
    required List<RecordData>? fundingRecords,
    required List<RecordData>? communityRecords,
  }) {
    switch (walletSliderIndex) {
      case 0:
        if (fundingRecordsFetchStatus == DataStatus.success) {
          return _CommonWalletListData(
            recordsData: fundingRecords ?? [],
          );
        } else if (fundingRecordsFetchStatus == DataStatus.loading) {
          return const _CommonRecordListShimmer();
        } else {
          return const _CommonRecordListShimmer();
        }
      case 1:
        if (communityRecordsFetchStatus == DataStatus.success) {
          return _CommonWalletListData(
            recordsData: communityRecords ?? [],
          );
        } else if (communityRecordsFetchStatus == DataStatus.loading) {
          return const _CommonRecordListShimmer();
        } else {
          return const _CommonRecordListShimmer();
        }

      default:
        return const _CommonRecordListShimmer();
    }
  }

  bool _managePagination({
    required DataStatus fundingRecordsFetchStatus,
    required DataStatus communityRecordsFetchStatus,
  }) {
    int walletSliderIndex = context.read<WalletCubit>().state.recordSliderIndex;
    switch (walletSliderIndex) {
      case 0:
        return fundingRecordsFetchStatus == DataStatus.loading;
      case 1:
        return communityRecordsFetchStatus == DataStatus.loading;
      default:
        return false;
    }
  }

  bool _manageNext({
    required List<RecordData>? fundingRecords,
    required List<RecordData>? communityRecords,
    required int? totalFundingRecords,
    required int? totalCommunityRecords,
  }) {
    int walletSliderIndex = context.read<WalletCubit>().state.recordSliderIndex;
    switch (walletSliderIndex) {
      case 0:
        return (fundingRecords?.length ?? 0) <= (totalFundingRecords ?? 0);
      case 1:
        return (communityRecords?.length ?? 0) <= (totalCommunityRecords ?? 0);
      default:
        return false;
    }
  }

  bool _onPagination(
    ScrollEndNotification notification,
    (int?, int?) pageNum,
    (List<RecordData>?, List<RecordData>?) records,
    (int?, int?) totalRecords,
  ) {
    int walletSliderIndex = context.read<WalletCubit>().state.recordSliderIndex;
    switch (walletSliderIndex) {
      case 0:
        if ((records.$1?.length ?? 0) == (totalRecords.$1 ?? 0)) {
          return false;
        }
        context.read<RecordsCubit>().getFundingRecords(page: (pageNum.$1 ?? 0) + 1, isLoadMore: true);
        break;
      case 1:
        // Only fetch community records if not SIS flavor
        if (getIt<AppConfig>().fetchCommunityRecords) {
          if ((records.$2?.length ?? 0) == (totalRecords.$2 ?? 0)) {
            return false;
          }
          context.read<RecordsCubit>().getCommunityRecords(page: (pageNum.$2 ?? 0) + 1, isLoadMore: true);
        }
        break;

      default:
        return false;
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      body: SafeArea(
        child: AnimationLimiter(
          child: RefreshIndicator(
            onRefresh: () async {
              _manageRefresh();
            },
            child: BlocBuilder<RecordsCubit, RecordsState>(
              builder: (context, state) {
                return PaginationWidget(
                  isPaginating: _managePagination(
                    fundingRecordsFetchStatus: state.fundingRecordsFetchStatus,
                    communityRecordsFetchStatus: state.communityRecordsFetchStatus,
                  ),
                  next: _manageNext(
                    fundingRecords: state.fundingRecords,
                    communityRecords: state.communityRecords,
                    totalFundingRecords: state.totalFundingRecords,
                    totalCommunityRecords: state.totalCommunityRecords,
                  ),
                  onPagination: (notification) => _onPagination(
                    notification,
                    (
                      state.pageNumFundingRecords,
                      state.pageNumCommunityRecords,
                    ),
                    (
                      state.fundingRecords,
                      state.communityRecords,
                    ),
                    (
                      state.totalFundingRecords,
                      state.totalCommunityRecords,
                    ),
                  ),
                  child: CustomScrollView(
                    controller: _scrollController,
                    shrinkWrap: true,
                    physics: const BouncingScrollPhysics(
                      parent: AlwaysScrollableScrollPhysics(),
                    ),
                    slivers: [
                      CommonSliverAppBar(
                        buildContext: context,
                        enableNavBack: true,
                        titleWidget: Text(
                          StringConstants.transactionsHistory.tr(),
                          style: FontPalette.semiBold19.copyWith(
                            color: myColorScheme(context).titleColor,
                          ),
                        ),
                        centerTitleText: true,
                      ),
                      SliverToBoxAdapter(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: staggeredAnimation(
                            children: [
                              10.verticalSpace,
                              BlocBuilder<HomeCubit, HomeState>(
                                builder: (context, homeState) {
                                  final balanceData = homeState.balanceData?.data;
                                  return balanceData != null
                                      ? WalletSliderRecords(
                                          fullWidth: 1.sw,
                                          width: 377.w,
                                          height: 121.h,
                                          balance: balanceData,
                                        )
                                      : const SizedBox.shrink();
                                },
                              ),
                              25.verticalSpace,
                            ],
                          ),
                        ),
                      ),
                      SliverList(
                        delegate: SliverChildListDelegate([
                          BlocBuilder<WalletCubit, WalletState>(
                            builder: (context, walletState) {
                              return _manageWidgetWalletList(
                                walletSliderIndex: walletState.recordSliderIndex,
                                fundingRecordsFetchStatus: state.fundingRecordsFetchStatus,
                                communityRecordsFetchStatus: state.communityRecordsFetchStatus,
                                fundingRecords: state.fundingRecords,
                                communityRecords: state.communityRecords,
                              );
                            },
                          ),
                        ]),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class _CommonWalletListData extends StatelessWidget {
  final List<RecordData> recordsData;

  const _CommonWalletListData({required this.recordsData});

  @override
  Widget build(BuildContext context) {
    return recordsData.isEmpty
        ? const Center(child: CommonEmpty())
        : ListView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemCount: recordsData.length,
            itemBuilder: (context, i) {
              var item = recordsData[i];
              return Padding(
                padding: const EdgeInsets.fromLTRB(24, 0, 24, 0).w,
                child: RecordsItem(
                  index: i,
                  width: 377,
                  title: item.title ?? '',
                  price1: item.amount ?? 0,
                  price2: item.balance ?? 0,
                  date: DateFormat('yyyy-MM-dd   HH:mm:ss').format(item.createTime ?? DateTime.now()),
                  showSub: item.fundDirection == FundDirection.sub,
                ),
              );
            },
          );
  }
}

class _CommonRecordListShimmer extends StatelessWidget {
  const _CommonRecordListShimmer();

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: 3,
      itemBuilder: (context, i) {
        return Padding(
          padding: const EdgeInsets.fromLTRB(24, 10, 24, 0).w,
          child: CommonShimmer(
            br: 15.r,
            width: 377.w,
            height: 100.h,
          ),
        );
      },
    );
  }
}
