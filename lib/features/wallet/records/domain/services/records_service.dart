import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/api/endpoint/api_endpoints.dart';
import 'package:sf_app_v2/core/api/network/network.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/wallet/records/domain/models/records_model.dart';

import '../repository/records_repository.dart';

/// Service class that implements [RecordsRepository] to handle wallet records operations
@Injectable(as: RecordsRepository)
class RecordsService implements RecordsRepository {
  /// Fetches wallet records from the API
  /// 
  /// Parameters:
  /// - [pageNum] The page number for pagination
  /// - [type] The type of records to fetch
  ///
  /// Returns a [ResponseResult] containing either:
  /// - [RecordsModel] with the fetched records data on success
  /// - Error message string on failure
  @override
  Future<ResponseResult<RecordsModel>> getRecords({
    required int pageNum,
    required String type,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.recordsWallet(type, pageNum),
         isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: RecordsModel.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get online service');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
