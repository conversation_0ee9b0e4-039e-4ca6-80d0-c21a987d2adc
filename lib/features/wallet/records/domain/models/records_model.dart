import 'package:freezed_annotation/freezed_annotation.dart';

part 'records_model.freezed.dart';
part 'records_model.g.dart';

@freezed
class RecordsModel with _$RecordsModel {
  const factory RecordsModel({
    int? code,
    RecordListData? data,
    String? msg,
  }) = _RecordsModel;

  factory RecordsModel.fromJson(Map<String, dynamic> json) =>
      _$RecordsModelFromJson(json);
}

@freezed
class RecordListData with _$RecordListData {
  const factory RecordListData({
    List<RecordData>? list,
    int? pageNum,
    int? pageSize,
    int? total,
  }) = _RecordListData;

  factory RecordListData.fromJson(Map<String, dynamic> json) =>
      _$RecordListDataFromJson(json);
}

@freezed
class RecordData with _$RecordData {
  const factory RecordData({
    double? amount,
    double? balance,
    DateTime? createTime,
    FundDirection? fundDirection,
    String? title,
  }) = _RecordData;

  factory RecordData.fromJson(Map<String, dynamic> json) =>
      _$RecordDataFromJson(json);
}

enum FundDirection {
  @JsonValue('SUB')
  sub,
  @JsonValue('ADD')
  add,
}
