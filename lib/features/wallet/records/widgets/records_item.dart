import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../../core/theme/font_pallette.dart';
import '../../../../core/theme/my_color_scheme.dart';
import '../../../../core/utils/convert_helper.dart';

class RecordsItem extends StatelessWidget {
  final double width;
  final String title;
  final double price1;
  final double price2;
  final String date;
  final int index;
  final bool showSub;

  const RecordsItem({
    super.key,
    required this.width,
    required this.title,
    required this.price1,
    required this.price2,
    required this.date,
    required this.index,
    required this.showSub,
  });

  @override
  Widget build(BuildContext context) {
    return AnimationConfiguration.staggeredList(
      position: index,
      duration: const Duration(milliseconds: 375),
      child: SlideAnimation(
        verticalOffset: 50.0,
        child: FadeInAnimation(
          child: SizedBox(
            width: width.r,
            child: Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.0).r,
              ),
              elevation: 4,
              shadowColor: Colors.black12,
              child: Padding(
                padding: const EdgeInsets.all(20).r,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: FontPalette.medium14,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          ConvertHelper.formatPriceUsd(
                            showSub ? -price1 : price1,
                          ),
                          style: FontPalette.semiBold18.copyWith(
                            color: myColorScheme(context).primaryColor,
                          ),
                        ),
                        Text(
                          ConvertHelper.formatPriceUsd(price2),
                          style: FontPalette.semiBold20.copyWith(
                            color: myColorScheme(context).titleColor,
                          ),
                        ),
                      ],
                    ),
                    Text(date, style: FontPalette.normal12),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
