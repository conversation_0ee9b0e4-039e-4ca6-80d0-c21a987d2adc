import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/wallet/withdraw/domain/models/collection_balance%20/collection_balance_model.dart';
import 'package:sf_app_v2/features/wallet/withdraw/domain/models/collection_withdraw/collection_withdraw_model.dart';
import 'package:sf_app_v2/features/wallet/withdraw/domain/models/expend_address/expend_address_model.dart';

import '../models/withdraw_history/withdraw_history.dart';

abstract class WithdrawRepository {
  const WithdrawRepository();

  Future<ResponseResult<FundingBalanceModel>> fundingBalance();
  Future<ResponseResult<ExpendAddressModel>> expendedAddress();
  Future<ResponseResult<CollectionWithdrawModel>> collectionWithdrawFee({
    required String amount,
  });
  Future<ResponseResult<bool>> withdraw({
    required String amount,
    required String payPassword,
    required String address,
  });
  Future<ResponseResult<WithdrawHistory>> getWithdrawHistory({
    required int pageNum,
    required int pageSize,
    required int status,
  });
}
