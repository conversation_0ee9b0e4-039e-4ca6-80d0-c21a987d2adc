import 'package:freezed_annotation/freezed_annotation.dart';

part 'collection_withdraw_model.freezed.dart';
part 'collection_withdraw_model.g.dart';

@freezed
class CollectionWithdrawModel with _$CollectionWithdrawModel {
  const factory CollectionWithdrawModel({
    required int code,
    required CollectionWithdrawData data,
    required String msg,
  }) = _CollectionWithdrawModel;

  factory CollectionWithdrawModel.fromJson(Map<String, dynamic> json) =>
      _$CollectionWithdrawModelFromJson(json);
}

@freezed
class CollectionWithdrawData with _$CollectionWithdrawData {
  const factory CollectionWithdrawData({
    required double amount,
  }) = _CollectionWithdrawData;

  factory CollectionWithdrawData.fromJson(Map<String, dynamic> json) =>
      _$CollectionWithdrawDataFromJson(json);
}
