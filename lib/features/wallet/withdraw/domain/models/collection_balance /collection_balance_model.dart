import 'package:freezed_annotation/freezed_annotation.dart';

part 'collection_balance_model.freezed.dart';
part 'collection_balance_model.g.dart';

@freezed
class FundingBalanceModel with _$FundingBalanceModel {
  const factory FundingBalanceModel({
    required int code,
    required FundingBalanceData data,
    required String msg,
  }) = _FundingBalanceModel;

  factory FundingBalanceModel.fromJson(Map<String, dynamic> json) =>
      _$FundingBalanceModelFromJson(json);
}

@freezed
class FundingBalanceData with _$FundingBalanceData {
  const factory FundingBalanceData({
    required String balance,
  }) = _FundingBalanceData;

  factory FundingBalanceData.fromJson(Map<String, dynamic> json) =>
      _$FundingBalanceDataFromJson(json);
}
