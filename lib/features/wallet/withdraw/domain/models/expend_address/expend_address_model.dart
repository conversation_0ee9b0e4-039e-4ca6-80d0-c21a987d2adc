import 'package:freezed_annotation/freezed_annotation.dart';

part 'expend_address_model.freezed.dart';
part 'expend_address_model.g.dart';

@freezed
class ExpendAddressModel with _$ExpendAddressModel {
  const factory ExpendAddressModel({
    required int code,
    required List<ExpendAddressData> data,
    required String msg,
  }) = _ExpendAddressModel;

  factory ExpendAddressModel.fromJson(Map<String, dynamic> json) =>
      _$ExpendAddressModelFromJson(json);
}

@freezed
class ExpendAddressData with _$ExpendAddressData {
  const factory ExpendAddressData({
    required String address,
    required int id,
    required String name,
    required String type,
  }) = _ExpendAddressData;

  factory ExpendAddressData.fromJson(Map<String, dynamic> json) =>
      _$ExpendAddressDataFromJson(json);
}
