import 'package:freezed_annotation/freezed_annotation.dart';

part 'collection_supervisor_model.freezed.dart';
part 'collection_supervisor_model.g.dart';

@freezed
class CollectionSupervisorModel with _$CollectionSupervisorModel {
  const factory CollectionSupervisorModel({
    required int code,
    required CollectionSupervisorData data,
    required String msg,
  }) = _CollectionSupervisorModel;

  factory CollectionSupervisorModel.fromJson(Map<String, dynamic> json) =>
      _$CollectionSupervisorModelFromJson(json);
}

@freezed
class CollectionSupervisorData with _$CollectionSupervisorData {
  const factory CollectionSupervisorData({
    required String email,
    required String username,
  }) = _CollectionSupervisorData;

  factory CollectionSupervisorData.fromJson(Map<String, dynamic> json) =>
      _$CollectionSupervisorDataFromJson(json);
}
