import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'withdraw_history.freezed.dart';
part 'withdraw_history.g.dart';

WithdrawHistory withdrawHistoryFromJson( str) => WithdrawHistory.fromJson((str));

String withdrawHistoryToJson(WithdrawHistory data) => json.encode(data.toJson());

@freezed
class WithdrawHistory with _$WithdrawHistory {
    const factory WithdrawHistory({
        int? code,
        WithdrawHistoryList? data,
        String? msg,
    }) = _WithdrawHistory;

    factory WithdrawHistory.fromJson(Map<String, dynamic> json) => _$WithdrawHistoryFromJson(json);
}

@freezed
class WithdrawHistoryList with _$WithdrawHistoryList {
    const factory WithdrawHistoryList({
        List<WithdrawHistoryItem>? list,
        int? pageNum,
        int? pageSize,
        int? total,
    }) = _WithdrawHistoryList;

    factory WithdrawHistoryList.from<PERSON>son(Map<String, dynamic> json) => _$WithdrawHistoryListFromJson(json);
}

@freezed
class WithdrawHistoryItem with _$WithdrawHistoryItem {
    const factory WithdrawHistoryItem({
        String? address,
        int? amount,
        String? completeTime,
        String? createTime,
        int? status,
        int? userId,
    }) = _WithdrawHistoryItem;

    factory WithdrawHistoryItem.fromJson(Map<String, dynamic> json) => _$WithdrawHistoryItemFromJson(json);
}
