import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/api/endpoint/api_endpoints.dart';
import 'package:sf_app_v2/core/api/network/network.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/wallet/withdraw/domain/models/collection_balance%20/collection_balance_model.dart';
import 'package:sf_app_v2/features/wallet/withdraw/domain/models/collection_withdraw/collection_withdraw_model.dart';
import 'package:sf_app_v2/features/wallet/withdraw/domain/models/expend_address/expend_address_model.dart';
import '../models/withdraw_history/withdraw_history.dart';
import '../repository/withdraw_repository.dart';

/// Service class that implements [WithdrawRepository] to handle withdrawal related operations
@Injectable(as: WithdrawRepository)
class WithdrawService implements WithdrawRepository {
  /// Fetches collection withdraw fee details for a given amount
  ///
  /// [amount] The withdrawal amount to calculate fees for
  /// Returns [ResponseResult] containing [CollectionWithdrawModel] with fee details or error
  @override
  Future<ResponseResult<CollectionWithdrawModel>> collectionWithdrawFee({
    required String amount,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.collectionWithdraw,
        data: {
          'amount': amount,
        },
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
            data: CollectionWithdrawModel.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch collection withdraw');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches the expended address details for the user's profile
  ///
  /// Returns [ResponseResult] containing [ExpendAddressModel] with address details or error
  @override
  Future<ResponseResult<ExpendAddressModel>> expendedAddress() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.profileExpendAddress,
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
            data: ExpendAddressModel.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch expended address');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches the funding wallet balance for the user
  ///
  /// Returns [ResponseResult] containing [FundingBalanceModel] with balance details or error
  @override
  Future<ResponseResult<FundingBalanceModel>> fundingBalance() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.fundingWalletBalance,
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
            data: FundingBalanceModel.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch withdraw balance');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Processes a withdrawal request
  ///
  /// [amount] The amount to withdraw
  /// [payPassword] The payment password for authorization
  /// [address] The withdrawal address ID
  /// Returns [ResponseResult] containing boolean success status or error
  @override
  Future<ResponseResult<bool>> withdraw({
    required String amount,
    required String payPassword,
    required String address,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.withdraw,
        data: {
          'amount': amount,
          'payPassword': payPassword,
          'addressId': address,
        },
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to withdraw');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches the withdrawal history with pagination
  ///
  /// Parameters:
  /// - [pageNum] The page number to fetch
  /// - [pageSize] Number of records per page
  /// - [status] The status of the withdrawal ([0]: [Pending], [1]: [Processing], [2]: [Success], [3]: [Rejected])
  /// Returns a [ResponseResult] containing:
  /// - Success: [WithdrawHistory] model with withdrawal records
  /// - Error: Error message as string
  ///
  /// Throws [DioException] for network related errors
  @override
  Future<ResponseResult<WithdrawHistory>> getWithdrawHistory({
    required int pageNum,
    required int pageSize,
    required int status,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.withdrawHistory}?pageNum=$pageNum&pageSize=$pageSize&status=$status',
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: WithdrawHistory.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get withdraw history');
      }
    } on Error catch (e) {
      return ResponseResult(error: e.toString());
    }
  }
}
