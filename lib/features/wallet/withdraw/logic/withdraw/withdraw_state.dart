part of 'withdraw_cubit.dart';

class WithdrawState extends Equatable {
  final DataStatus fundingBalanceFetchStatus;
  final DataStatus expendedAddressFetchStatus;
  final DataStatus collectionWithdrawFeeFetchStatus;
  final DataStatus withdrawHistoryFetchStatus;
  final DataStatus withdrawFetchStatus;
  final FundingBalanceData? fundingBalanceData;
  final ExpendAddressModel? expendedAddressData;
  final CollectionWithdrawData? collectionWithdrawData;
  final WithdrawHistory? withdrawHistory;
  final Map<int, WithdrawHistory> withdrawHistoryByStatus;
  final int? selectedAddress;
  final int? currentSheetIndex;
  final String? error;
  final double? withdrawalFee;
  final String? amount;
  const WithdrawState({
    this.fundingBalanceFetchStatus = DataStatus.idle,
    this.expendedAddressFetchStatus = DataStatus.idle,
    this.collectionWithdrawFeeFetchStatus = DataStatus.idle,
    this.withdrawFetchStatus = DataStatus.idle,
    this.withdrawHistoryFetchStatus = DataStatus.idle,
    this.fundingBalanceData,
    this.expendedAddressData,
    this.collectionWithdrawData,
    this.withdrawHistory,
    this.withdrawHistoryByStatus = const {},
    this.selectedAddress,
    this.error,
    this.withdrawalFee,
    this.currentSheetIndex = 0,
    this.amount,
  });

  WithdrawState copyWith({
    DataStatus? fundingBalanceFetchStatus,
    DataStatus? expendedAddressFetchStatus,
    DataStatus? collectionWithdrawFeeFetchStatus,
    DataStatus? withdrawFetchStatus,
    DataStatus? withdrawHistoryFetchStatus,
    FundingBalanceData? fundingBalanceData,
    ExpendAddressModel? expendedAddressData,
    CollectionWithdrawData? collectionWithdrawData,
    WithdrawHistory? withdrawHistory,
    Map<int, WithdrawHistory>? withdrawHistoryByStatus,
    int? selectedAddress,
    int? currentSheetIndex,
    bool? clearSelectedAddress,
    bool? bottomSheetActive,
    bool? resetError,
    String? error,
    double? withdrawalFee,
    String? amount,
  }) {
    return WithdrawState(
      fundingBalanceFetchStatus:
          fundingBalanceFetchStatus ?? this.fundingBalanceFetchStatus,
      expendedAddressFetchStatus:
          expendedAddressFetchStatus ?? this.expendedAddressFetchStatus,
      collectionWithdrawFeeFetchStatus: collectionWithdrawFeeFetchStatus ??
          this.collectionWithdrawFeeFetchStatus,
      withdrawFetchStatus: withdrawFetchStatus ?? this.withdrawFetchStatus,
      withdrawHistoryFetchStatus:
          withdrawHistoryFetchStatus ?? this.withdrawHistoryFetchStatus,
      fundingBalanceData: fundingBalanceData ?? this.fundingBalanceData,
      expendedAddressData: expendedAddressData ?? this.expendedAddressData,
      collectionWithdrawData:
          collectionWithdrawData ?? this.collectionWithdrawData,
      withdrawHistory: withdrawHistory ?? this.withdrawHistory,
      withdrawHistoryByStatus: withdrawHistoryByStatus ?? this.withdrawHistoryByStatus,
      selectedAddress: clearSelectedAddress == true
          ? null
          : selectedAddress ?? this.selectedAddress,
      withdrawalFee: withdrawalFee ?? this.withdrawalFee,
      error: resetError == true ? null : error ?? this.error,
      currentSheetIndex: currentSheetIndex ?? this.currentSheetIndex,
      amount: amount ?? this.amount,
    );
  }

  @override
  List<Object?> get props => [
        fundingBalanceFetchStatus,
        expendedAddressFetchStatus,
        collectionWithdrawFeeFetchStatus,
        withdrawFetchStatus,
        withdrawHistoryFetchStatus,
        fundingBalanceData,
        expendedAddressData,
        collectionWithdrawData,
        withdrawHistory,
        withdrawHistoryByStatus,
        selectedAddress,
        error,
        withdrawalFee,
        currentSheetIndex,
        amount,
      ];
}
