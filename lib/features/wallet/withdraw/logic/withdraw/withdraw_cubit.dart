import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/wallet/withdraw/domain/models/collection_balance%20/collection_balance_model.dart';
import 'package:sf_app_v2/features/wallet/withdraw/domain/models/collection_withdraw/collection_withdraw_model.dart';
import 'package:sf_app_v2/features/wallet/withdraw/domain/models/expend_address/expend_address_model.dart';
import 'package:sf_app_v2/features/wallet/withdraw/domain/repository/withdraw_repository.dart';

import '../../domain/models/withdraw_history/withdraw_history.dart';
part 'withdraw_state.dart';

@injectable
class WithdrawCubit extends Cubit<WithdrawState> {
  final WithdrawRepository _withdrawService;

  WithdrawCubit(this._withdrawService) : super(const WithdrawState());

  Future<void> fetchFundingBalance() async {
    emit(state.copyWith(fundingBalanceFetchStatus: DataStatus.loading));
    try {
      final result = await _withdrawService.fundingBalance();
      if (result.data != null) {
        emit(
          state.copyWith(
            fundingBalanceFetchStatus: DataStatus.success,
            fundingBalanceData: result.data?.data,
          ),
        );
      } else {
        emit(
          state.copyWith(
            fundingBalanceFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          fundingBalanceFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> fetchExpendAddress() async {
    emit(state.copyWith(expendedAddressFetchStatus: DataStatus.loading));
    try {
      final result = await _withdrawService.expendedAddress();
      if (result.data != null) {
        emit(
          state.copyWith(
            expendedAddressFetchStatus: DataStatus.success,
            expendedAddressData: result.data,
          ),
        );
      } else {
        emit(
          state.copyWith(
            expendedAddressFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          expendedAddressFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> collectionWithdrawFee({required String amount}) async {
    emit(
      state.copyWith(
        amount: amount,
        collectionWithdrawFeeFetchStatus: DataStatus.loading,
      ),
    );
    try {
      final result =
          await _withdrawService.collectionWithdrawFee(amount: amount);
      if (result.data != null) {
        emit(
          state.copyWith(
            collectionWithdrawFeeFetchStatus: DataStatus.success,
            collectionWithdrawData: result.data?.data,
            withdrawalFee: double.parse(amount.toString()) -
                (result.data?.data.amount ?? 0),
          ),
        );
      } else {
        emit(
          state.copyWith(
            collectionWithdrawFeeFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          collectionWithdrawFeeFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> withdraw({
    required String amount,
    required String payPassword,
  }) async {
    emit(state.copyWith(withdrawFetchStatus: DataStatus.loading));
    try {
      final result = await _withdrawService.withdraw(
        amount: amount,
        payPassword: payPassword,
        address: state.selectedAddress.toString(),
      );
      if (result.data != null) {
        emit(
          state.copyWith(
            withdrawFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            withdrawFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          withdrawFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  void setSelectedAddress(int id) => emit(state.copyWith(selectedAddress: id));

  void setShowError(String? error) {
    if (error == null) emit(state.copyWith(resetError: true));
    emit(state.copyWith(error: error));
  }

  void clearSelectedAddress() =>
      emit(state.copyWith(clearSelectedAddress: true));

  void resetWithdraw() {
    emit(
      state.copyWith(
        currentSheetIndex: 0,
        bottomSheetActive: false,
        withdrawFetchStatus: DataStatus.idle,
      ),
    );
  }

  void resetSlider() => emit(
        state.copyWith(
          currentSheetIndex: 0,
        ),
      );

  void setCurrentSheetIndex(int index) =>
      emit(state.copyWith(currentSheetIndex: index));

  /// Gets withdraw history with status filter
  ///
  /// Status codes:
  /// - 0: Pending
  /// - 1: Processing
  /// - 2: Success
  /// - 3: Rejected
  ///

  Future<void> getWithdrawHistory(
      {int page = 1, bool isLoadMore = false, required int status}) async {
    emit(state.copyWith(withdrawHistoryFetchStatus: DataStatus.loading));
    try {
      final result = await _withdrawService.getWithdrawHistory(
        pageNum: page,
        pageSize: 10,
        status: status,
      );

      if (result.data != null) {
        // Get current data for this status
        final currentStatusData = state.withdrawHistoryByStatus[status];
        final List<WithdrawHistoryItem> currentList =
            currentStatusData?.data?.list ?? [];
        final List<WithdrawHistoryItem> newList = result.data?.data?.list ?? [];

        // Combine lists if loading more, otherwise use just the new list
        final List<WithdrawHistoryItem> withdrawHistoryList =
            isLoadMore ? [...currentList, ...newList] : newList;

        // Create the response with the appropriate list
        final withdrawHistoryResponse = WithdrawHistory(
          code: result.data?.code ?? 0,
          data: WithdrawHistoryList(
            list: withdrawHistoryList,
            pageNum: result.data?.data?.pageNum ?? 0,
            pageSize: result.data?.data?.pageSize ?? 0,
            total: result.data?.data?.total ?? 0,
          ),
          msg: result.data?.msg ?? '',
        );

        // Update the map with new data for this status
        final updatedHistoryByStatus = Map<int, WithdrawHistory>.from(state.withdrawHistoryByStatus);
        updatedHistoryByStatus[status] = withdrawHistoryResponse;

        emit(state.copyWith(
          withdrawHistoryFetchStatus: DataStatus.success,
          withdrawHistory: withdrawHistoryResponse, // Keep for backward compatibility
          withdrawHistoryByStatus: updatedHistoryByStatus,
        ));
      } else {
        emit(state.copyWith(
          withdrawHistoryFetchStatus: DataStatus.failed,
          error: result.error,
        ));
      }
    } on Exception catch (e) {
      emit(state.copyWith(
        withdrawHistoryFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }
}
