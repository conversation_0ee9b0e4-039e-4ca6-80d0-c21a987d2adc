import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/common_function.dart';
import 'package:sf_app_v2/core/config/app_config.dart';
import 'package:sf_app_v2/core/dependency_injection/injectable.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/utils/functions.dart';
import 'package:sf_app_v2/core/validator.dart';
import 'package:sf_app_v2/core/widgets/common_empty_data.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';
import 'package:sf_app_v2/core/widgets/custom_num_pad.dart';
import 'package:sf_app_v2/features/home/<USER>/models/balance/balance_model.dart';
import 'package:sf_app_v2/features/home/<USER>/home/<USER>';
import 'package:sf_app_v2/features/wallet/withdraw/widgets/name_box_drawer.dart';
import '../../../../core/api/network/network_helper.dart';
import '../../../../core/constants/assets.dart';
import '../../../../core/constants/enums.dart';
import '../../../../core/widgets/common_wallet_slider_item.dart';
import '../logic/withdraw/withdraw_cubit.dart';
import '../widgets/bottom_sheet_wallet.dart';

class WithdrawScreen extends StatefulWidget {
  const WithdrawScreen({super.key});

  @override
  State<WithdrawScreen> createState() => _WithdrawScreenState();
}

class _WithdrawScreenState extends State<WithdrawScreen> with Validator {
  final TextEditingController controller = TextEditingController();
  final _buttonEnabledNotifier = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    CommonFunctions.afterInit(_initialFunction);
  }

  void _initialFunction() {
    final withdrawCubit = context.read<WithdrawCubit>();
    withdrawCubit
      ..fetchFundingBalance()
      ..fetchExpendAddress()
      ..setShowError(null);
    controller.text = '0'.toCurrencyWithSymbol();
    controller.addListener(_onTextChange);
  }

  void _onTextChange() {
    if (controller.text.isNotEmpty) {
      double amount = parseUSDAmount(controller.text);
      _buttonEnabledNotifier.value = amount >= 1;
    }
  }

  void _onSubmitWithdraw(WithdrawState state) {
    context.read<WithdrawCubit>().collectionWithdrawFee(
        amount: parseUSDAmount(controller.text).toString());
  }

  void _showWithdrawBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BottomSheetWallet(
        textEditingController: controller,
        onClose: () {
          Navigator.pop(context);
          context.read<WithdrawCubit>().resetWithdraw();
        },
      ),
    );
  }

  Widget _buildErrorRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircleAvatar(
          backgroundColor: myColorScheme(context).deniedColor,
          radius: 5.r,
          child: Text(
            "!",
            style: TextStyle(
              color: myColorScheme(context).titleColor,
              fontWeight: FontWeight.w500,
              fontSize: 7.sp,
            ),
          ),
        ),
        7.horizontalSpace,
        BlocBuilder<WithdrawCubit, WithdrawState>(builder: (context, state) {
          return Text(
            state.error!,
            style: FontPalette.normal10.copyWith(
              color: myColorScheme(context).deniedColor,
            ),
          );
        }),
      ],
    );
  }

  Widget _buildAmountField() {
    return Container(
      width: 285.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(100.r)),
        boxShadow: [
          BoxShadow(
            color: myColorScheme(context)
                    .appBarIconColor
                    ?.withValues(alpha: 0.5) ??
                Colors.black12,
          ),
          BoxShadow(
            color: myColorScheme(context).cardColor ?? Colors.white,
            spreadRadius: -1,
            blurRadius: 6,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.zero,
        child: Center(
          child: TextField(
            enabled: false,
            decoration: const InputDecoration(border: InputBorder.none),
            controller: controller,
            textAlign: TextAlign.center,
            showCursor: false,
            style: FontPalette.bold40.copyWith(
              color: myColorScheme(context).titleColor ?? Colors.black,
            ),
            keyboardType: TextInputType.none,
          ),
        ),
      ),
    );
  }

  Widget _buildAddAddressButton() {
    return TextButton(
      onPressed: () {
        Navigator.pushNamed(
          context,
          routeAddAddressScreen,
          arguments: {'items': []},
        );
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            StringConstants.addWithdrawalAddress.tr(),
            style: FontPalette.medium12.copyWith(
              color: myColorScheme(context).primaryColor,
            ),
          ),
          4.horizontalSpace,
          Icon(
            Icons.add_box_rounded,
            color: myColorScheme(context).primaryColor,
            size: 16.r,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          title: Text(
            StringConstants.withdraw.tr(),
            style: FontPalette.semiBold20.copyWith(
              color: myColorScheme(context).titleColor,
            ),
            textAlign: TextAlign.center,
          ),
          centerTitle: true,
          leading: IconButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              context.read<WithdrawCubit>().clearSelectedAddress();
              Navigator.pop(context);
            },
            icon: Icon(
              Icons.arrow_back_ios_new_rounded,
              color: myColorScheme(context).appBarIconColor,
            ),
          ),
          actions: [
            if (getIt<AppConfig>().showWithdrawHistory)
              IconButton(
                onPressed: () => context.handleSignedInAction(
                  skipAccountCheck: true,
                  onTap: () =>
                      Navigator.pushNamed(context, routeWithdrawHistoryScreen),
                ),
                icon: SvgPicture.asset(
                  Assets.contracts1,
                  width: 14.w,
                  height: 18.h,
                  color: Theme.of(context).iconTheme.color,
                ),
              ),
          ]),
      body: BlocConsumer<WithdrawCubit, WithdrawState>(
        listenWhen: (previous, current) =>
            previous.collectionWithdrawFeeFetchStatus !=
            current.collectionWithdrawFeeFetchStatus,
        listener: (context, state) {
          if (state.collectionWithdrawFeeFetchStatus == DataStatus.success) {
            _showWithdrawBottomSheet();
          } else if (state.collectionWithdrawFeeFetchStatus ==
              DataStatus.failed) {}
        },
        builder: (context, state) {
          if (state.fundingBalanceFetchStatus == DataStatus.loading &&
              state.fundingBalanceData == null) {
            return const Center(child: CircularProgressIndicator.adaptive());
          }

          final double balance =
              double.parse(state.fundingBalanceData?.balance ?? '0');

          return RefreshIndicator(
            onRefresh: () async => _initialFunction(),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      physics: const NeverScrollableScrollPhysics(),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 15.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Center(
                              child: CommonWalletSliderItem(
                                width: 1.sw,
                                height: 121.h,
                                balance: BalanceData(
                                  cash:
                                      state.fundingBalanceData?.balance ?? '0',
                                ),
                                title: StringConstants.fundingWallet,
                              ),
                            ),
                            10.verticalSpace,
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  StringConstants.selectWithdrawalAddress.tr(),
                                  style: FontPalette.semiBold15.copyWith(
                                    color: myColorScheme(context).titleColor,
                                  ),
                                ),
                                13.verticalSpace,
                                Center(
                                  child: state.expendedAddressData?.data
                                              .isEmpty ??
                                          true
                                      ? Column(children: [
                                          if (getIt<AppConfig>().showAddWalletAddress)
                                            _buildAddAddressButton()
                                          else
                                            const CommonEmpty(
                                              topPadding: 0,
                                            )
                                        ])
                                      : NameBoxDrawer(
                                          width: double.infinity,
                                          expendAddressData:
                                              state.expendedAddressData?.data,
                                        ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 1.sw,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildAmountField(),
                        10.verticalSpace,
                        if (state.error != null) _buildErrorRow(),
                        NumPad(
                          numHeight: 60.h,
                          controller: controller,
                          onChanged: () {
                            if (parseUSDAmount(controller.text) > balance) {
                              context.read<WithdrawCubit>().setShowError(
                                    StringConstants.insufficientBalance.tr(),
                                  );
                            }
                          },
                          delete: () {
                            controller.text = controller.text.substring(
                              0,
                              controller.text.length - 1,
                            );
                          },
                          onSubmit: () {},
                          width: 1.sw,
                        ),
                        15.verticalSpace,
                        ValueListenableBuilder<bool>(
                          valueListenable: _buttonEnabledNotifier,
                          builder: (context, enable, _) {
                            return BlocConsumer<WithdrawCubit, WithdrawState>(
                              listenWhen: (previous, current) =>
                                  previous.withdrawFetchStatus !=
                                  current.withdrawFetchStatus,
                              listener: (context, state) {
                                if (state.withdrawFetchStatus ==
                                    DataStatus.failed) {
                                  NetworkHelper.handleMessage(
                                    state.error,
                                    context,
                                    type: HandleTypes.customDialog,
                                    snackBarType: SnackBarType.error,
                                  );
                                }
                                if (state.withdrawFetchStatus ==
                                    DataStatus.success) {
                                  controller.clear();
                                  context.read<WithdrawCubit>()
                                    ..fetchFundingBalance()
                                    ..clearSelectedAddress();
                                  context.read<HomeCubit>().getBalance();
                                }
                              },
                              builder: (context, state) {
                                return CustomButton(
                                  isEnabled:
                                      enable && state.selectedAddress != null,
                                  isLoading:
                                      state.collectionWithdrawFeeFetchStatus ==
                                          DataStatus.loading,
                                  width: 356.w,
                                  height: 52.h,
                                  label: StringConstants.withdraw.tr(),
                                  isOutlined: false,
                                  onPressed: () {
                                    context.read<WithdrawCubit>().setShowError(
                                          validateWithdrawAmount(
                                              controller.text, balance),
                                        );

                                    if (state.error == null) {
                                      _onSubmitWithdraw(state);
                                    }
                                  },
                                );
                              },
                            );
                          },
                        ),
                        15.verticalSpace,
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
