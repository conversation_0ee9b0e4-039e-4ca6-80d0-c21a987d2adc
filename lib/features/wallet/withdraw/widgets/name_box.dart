import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

class NameBox extends StatelessWidget {
  final void Function()? onPressed;
  final String name;
  final String type;
  final bool selected;

  const NameBox({
    super.key,
    required this.name,
    required this.type,
    this.selected = false,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8).r,
      ),
      elevation: 4,
      shadowColor: Colors.black54,
      child: Material(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8).r,
        ),
        color: selected
            ? myColorScheme(context).primaryColor
            : myColorScheme(context).cardColor3,
        child: InkWell(
          borderRadius: BorderRadius.circular(8).r,
          onTap: onPressed ?? () {},
          child: SizedBox(
            width: 98.w,
            height: 87.h,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(4.0).w,
                  child: Text(
                    name,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: FontPalette.normal13.copyWith(
                      height: 1,
                      color: selected
                          ? myColorScheme(context).white
                          : myColorScheme(context).subTitleColor,
                    ),
                  ),
                ),
                Text(
                  type,
                  textAlign: TextAlign.center,
                  style: FontPalette.semiBold13.copyWith(
                    color: selected
                        ? myColorScheme(context).white
                        : myColorScheme(context).subTitleColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
