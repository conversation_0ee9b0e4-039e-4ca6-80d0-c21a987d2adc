import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import '../../../../core/constants/enums.dart';
import '../../../../core/constants/string_constants.dart';
import '../../../../core/theme/font_pallette.dart';
import '../../../../core/utils/mixin/animation.dart';
import '../../../../core/utils/convert_helper.dart';
import '../../../../core/widgets/common_pin_field.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../home/<USER>/home/<USER>';
import '../../../transfer/widgets/bottom_sheet_header_wallet_titles.dart';
import '../logic/withdraw/withdraw_cubit.dart';

class BottomSheetWalletBody extends StatefulWidget {
  final double relativeHeight;
  final TextEditingController? textEditingController;

  const BottomSheetWalletBody({
    super.key,
    required this.relativeHeight,
    this.textEditingController,
  });

  @override
  State<BottomSheetWalletBody> createState() => _BottomSheetWalletBodyState();
}

class _BottomSheetWalletBodyState extends State<BottomSheetWalletBody> with StaggeredAnimation {
  final walletPasswordController = TextEditingController();
  final googleAuthCodeController = TextEditingController();
  final _formGlobalKey = GlobalKey<FormState>();

  final List titles = [
    StringConstants.enterThePassword.tr(),
    StringConstants.confirm.tr(),
  ];

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WithdrawCubit, WithdrawState>(
      builder: (context, state) {
        return Container(
          color: myColorScheme(context).cardColor,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                color: myColorScheme(context).cardColor,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.0.w),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      for (int i = 0; i < titles.length; i++)
                        BottomSheetWalletHeaderTitle(
                          title: titles[i],
                          active: state.currentSheetIndex == i,
                          onTap: () {
                            (i <= (state.currentSheetIndex ?? 0))
                                ? context.read<WithdrawCubit>().setCurrentSheetIndex(i)
                                : null;
                          },
                        ),
                    ],
                  ),
                ),
              ),
              if (state.currentSheetIndex == 0)
                SizedBox(
                  // height: 0.8.sh * widget.relativeHeight,
                  child: AnimationLimiter(
                    child: Form(
                      key: _formGlobalKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: staggeredAnimation(
                          children: [
                            Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                20.verticalSpace,
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      StringConstants.actualAmount.tr(),
                                      style: FontPalette.normal22,
                                    ),
                                    10.horizontalSpace,
                                    Text(
                                      ConvertHelper.formatNumberWithTwoDecimals(state.collectionWithdrawData?.amount as num).toCurrency(),
                                      style: FontPalette.bold22.copyWith(
                                        color: myColorScheme(context).primaryColor,
                                      ),
                                    ),
                                  ],
                                ),
                                11.verticalSpace,
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      StringConstants.withdrawalFee.tr(),
                                      style: FontPalette.normal14,
                                    ),
                                    10.horizontalSpace,
                                    Text(
                                      ConvertHelper.formatNumberWithTwoDecimals(state.withdrawalFee as num).toCurrency(),
                                      style: FontPalette.semiBold14.copyWith(
                                        color: myColorScheme(context).primaryColor,
                                      ),
                                    ),
                                  ],
                                ),
                                20.verticalSpace,
                                Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 33.w),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        StringConstants.enterWalletPassword.tr(),
                                        style: FontPalette.normal14.copyWith(
                                          color: myColorScheme(context).titleColor,
                                        ),
                                      ),
                                      16.verticalSpace,
                                      CommonPinFiledText(
                                        obscureText: true,
                                        controller: walletPasswordController,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            35.verticalSpace,
                            Center(
                              child: CustomButton(
                                width: 364.w,
                                height: 50.h,
                                label: StringConstants.next.tr(),
                                isOutlined: false,
                                onPressed: () {
                                  FocusScope.of(context).unfocus();
                                  if (_formGlobalKey.currentState!.validate()) {
                                    context.read<WithdrawCubit>().setCurrentSheetIndex(1);
                                  }
                                },
                              ),
                            ),
                            30.verticalSpace,
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              if (state.currentSheetIndex == 1)
                Column(
                  children: [
                    16.verticalSpace,
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 80.0.w),
                      child: Center(
                        child: Text(
                          StringConstants.summaryWithdraw.tr(),
                          textAlign: TextAlign.center,
                          style: FontPalette.semiBold20,
                        ),
                      ),
                    ),
                    43.verticalSpace,
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 37.0.w),
                      child: Container(
                        width: 369.w,
                        height: 56.h,
                        decoration: BoxDecoration(
                          color: myColorScheme(context).backgroundColor1,
                          borderRadius: BorderRadius.circular(28.r),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              '${StringConstants.totalAmount.tr()}:',
                              textAlign: TextAlign.center,
                              style: FontPalette.normal20,
                            ),
                            10.horizontalSpace,
                            Text(
                              ConvertHelper.formatNumberWithTwoDecimals(double.parse(state.amount!)).toCurrency(),
                              textAlign: TextAlign.center,
                              style: FontPalette.semiBold20.copyWith(
                                color: myColorScheme(context).primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    54.verticalSpace,
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 37.0.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          CustomButton(
                            width: 155.w,
                            height: 45.h,
                            label: StringConstants.cancel.tr(),
                            btnTextStyle: FontPalette.semiBold14.copyWith(
                              color: myColorScheme(context).primaryColor,
                            ),
                            isOutlined: true,
                            onPressed: () {
                              context.read<WithdrawCubit>().setCurrentSheetIndex(0);
                            },
                          ),
                          // 55.horizontalSpace,
                          BlocListener<WithdrawCubit, WithdrawState>(
                            listenWhen: (previous, current) =>
                                previous.withdrawFetchStatus != current.withdrawFetchStatus,
                            listener: (context, state) {
                              if (state.withdrawFetchStatus == DataStatus.success) {
                                context.read<HomeCubit>().getBalance();
                              }
                            },
                            child: BlocSelector<WithdrawCubit, WithdrawState, (DataStatus, String?)>(
                              selector: (state) => (state.withdrawFetchStatus, state.amount),
                              builder: (context, state) {
                                return CustomButton(
                                  width: 155.w,
                                  height: 45.h,
                                  label: StringConstants.confirm.tr(),
                                  isLoading: state.$1 == DataStatus.loading,
                                  isOutlined: false,
                                  onPressed: () => context.read<WithdrawCubit>().withdraw(
                                        amount: state.$2!,
                                        payPassword: walletPasswordController.text,
                                      ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
            ],
          ),
        );
      },
    );
  }
}
