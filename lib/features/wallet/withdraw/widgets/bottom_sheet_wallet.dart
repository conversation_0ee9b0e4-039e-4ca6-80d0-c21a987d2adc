import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/common_function.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/features/wallet/withdraw/widgets/bottom_sheet_body_wallet.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/constants/assets.dart';
import '../../../../core/constants/string_constants.dart';
import '../../../../core/theme/font_pallette.dart';
import '../../../../core/widgets/custom_button.dart';
import '../logic/withdraw/withdraw_cubit.dart';

class BottomSheetWallet extends StatefulWidget {
  final VoidCallback onClose;
  final TextEditingController? textEditingController;

  const BottomSheetWallet({
    super.key,
    required this.onClose,
    this.textEditingController,
  });

  @override
  State<BottomSheetWallet> createState() => _BottomSheetWalletState();
}

class _BottomSheetWalletState extends State<BottomSheetWallet> {
  @override
  void initState() {
    super.initState();
    CommonFunctions.afterInit(
      () => context
          .read<WithdrawCubit>()
          ..resetSlider()
          ..setShowError(null)
          ..resetWithdraw(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocSelector<WithdrawCubit, WithdrawState, DataStatus>(
      selector: (state) => state.withdrawFetchStatus,
      builder: (context, value) {
        return Container(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom > 0
                ? MediaQuery.of(context).viewInsets.bottom
                : 20.h,
          ),
          decoration: BoxDecoration(
            color: myColorScheme(context).cardColor,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(20.r),
            ),
          ),
          child: ListView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _BuildHeader(onClose: widget.onClose),
              value == DataStatus.success
                  ? _BuildSuccessView(
                      onClose: widget.onClose,
                      textEditingController: widget.textEditingController,
                    )
                  : _BuildMainContent(
                      textEditingController: widget.textEditingController,
                    ),
            ],
          ),
        );
      },
    );
  }
}

class _BuildHeader extends StatelessWidget {
  final VoidCallback onClose;

  const _BuildHeader({
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40.h,
      child: Stack(
        children: [
          Center(
            child: Container(
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: myColorScheme(context).borderColor,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: IconButton(
              icon: const Icon(Icons.close),
              onPressed: onClose,
            ),
          ),
        ],
      ),
    );
  }
}

class _BuildSuccessView extends StatelessWidget {
  final VoidCallback onClose;
  final TextEditingController? textEditingController;

  const _BuildSuccessView({
    required this.onClose,
    this.textEditingController,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
      ),
      child: Column(
        children: [
          87.verticalSpace,
          CircleAvatar(
            backgroundColor: Colors.transparent,
            radius: 60.r,
            child: SvgPicture.asset(Assets.alertSuccess),
          ),
          Text(
            StringConstants.withdraw.tr(),
            style: FontPalette.medium18,
          ),
          Text(
            StringConstants.successfully.tr(),
            textAlign: TextAlign.center,
            style: FontPalette.semiBold40,
          ),
          54.verticalSpace,
          Center(
            child: CustomButton(
              width: 384,
              height: 56,
              label: StringConstants.done.tr(),
              isOutlined: false,
              onPressed: () {
                onClose();
                textEditingController?.clear();
                Navigator.pop(context);
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _BuildMainContent extends StatelessWidget {
  final TextEditingController? textEditingController;

  const _BuildMainContent({
    this.textEditingController,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<WithdrawCubit, WithdrawState>(
      builder: (context, state) {
        return SingleChildScrollView(
          child: Container(
            color: myColorScheme(context).cardColor,
            child: BottomSheetWalletBody(
              relativeHeight: 1.0, // Set to full height since we're not using snapping
              textEditingController: textEditingController,
            ),
          ),
        );
      },
    );
  }
}
