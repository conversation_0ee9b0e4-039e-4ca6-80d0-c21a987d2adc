import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/features/wallet/withdraw/domain/models/expend_address/expend_address_model.dart';
import 'package:sf_app_v2/features/wallet/withdraw/logic/withdraw/withdraw_cubit.dart';
import 'package:sf_app_v2/features/wallet/withdraw/widgets/name_box.dart';

class NameBoxDrawer extends StatefulWidget {
  final double width;
  final List<ExpendAddressData>? expendAddressData;

  const NameBoxDrawer({
    super.key,
    required this.width,
    required this.expendAddressData,
  });

  @override
  State<NameBoxDrawer> createState() => _NameBoxDrawerState();
}

class _NameBoxDrawerState extends State<NameBoxDrawer> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width.w,
      height: 87.w,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.expendAddressData?.length,
        itemBuilder: (context, index) {
          if (widget.expendAddressData == null) {
            return const SizedBox.shrink();
          }
          ExpendAddressData expendAddressData =
              widget.expendAddressData![index];
          return BlocSelector<WithdrawCubit, WithdrawState, int?>(
            selector: (state) => state.selectedAddress,
            builder: (context, state) {
              return NameBox(
                name: expendAddressData.name,
                type: expendAddressData.type,
                selected: state == expendAddressData.id ? true : false,
                onPressed: () {
                  context
                      .read<WithdrawCubit>()
                      .setSelectedAddress(expendAddressData.id);
                },
              );
            },
          );
        },
      ),
    );
  }
}
