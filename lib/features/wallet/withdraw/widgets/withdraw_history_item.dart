import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/utils/convert_helper.dart';
import 'package:sf_app_v2/features/wallet/withdraw/domain/models/withdraw_history/withdraw_history.dart';

class WithdrawHistoryItemWidget extends StatelessWidget {
  final WithdrawHistoryItem item;
  final int index;

  const WithdrawHistoryItemWidget({
    super.key,
    required this.item,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    return AnimationConfiguration.staggeredList(
      position: index,
      duration: const Duration(milliseconds: 300),
      child: SlideAnimation(
        verticalOffset: 50.0,
        child: FadeInAnimation(
          child: Container(
            margin: EdgeInsets.only(bottom: 12.h),
            child: Card(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.0.r),
              ),
              elevation: 2,
              shadowColor: Colors.black12,
              color: myColorScheme(context).cardColor,
              child: Padding(
                padding: EdgeInsets.all(16.r),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Amount and Status Row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          ConvertHelper.formatPriceUsd(
                              (item.amount ?? 0).toDouble()),
                          style: FontPalette.bold24.copyWith(
                            color: myColorScheme(context).titleColor,
                          ),
                        ),
                        _buildStatusChip(context),
                      ],
                    ),
                    12.verticalSpace,
                    // Address
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: const Color(0xFFF5F5F5),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 4.w, vertical: 6.h),
                        child: Text(
                          item.address ?? '',
                          style: FontPalette.normal13.copyWith(
                            color: myColorScheme(context).titleColor,
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    10.verticalSpace,
                    // Date and Relative Time Row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (item.status == 2 || item.status == 3)
                              Text(
                                '${StringConstants.completed.tr()}:',
                                style: FontPalette.normal13.copyWith(
                                    color: myColorScheme(context).titleColor,
                                    fontWeight: FontWeight.w500),
                              ),
                            Text(
                              _getDisplayDate(),
                              style: FontPalette.normal14.copyWith(
                                color: myColorScheme(context)
                                    .titleColor
                                    ?.withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ),
                        Text(
                          _getRelativeTime(),
                          style: FontPalette.normal14.copyWith(
                            color: myColorScheme(context).titleColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    final statusText = _getStatusText();
    final statusColor = _getStatusColor();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: statusColor,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Text(
        statusText,
        style: FontPalette.medium12.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  String _getStatusText() => switch (item.status) {
        0 => StringConstants.pending.tr(),
        1 => StringConstants.processing.tr(),
        2 => StringConstants.completed.tr(),
        3 => StringConstants.rejected.tr(),
        _ => StringConstants.pending.tr(),
      };

  Color _getStatusColor() => switch (item.status) {
        0 => const Color(0xFFFAAD14), // Pending (Orange)
        1 => const Color(0xFF1677FF), // Processing (Blue)
        2 => const Color(0xFF52C41A), // Completed (Green)
        3 => const Color(0xFFFF4D4F), // Rejected (Red)
        _ => const Color(0xFF1677FF), // Default (Blue)
      };

  String _getDisplayDate() {
    final timestamp = _getRelevantTimestamp();
    if (timestamp == null) return '';

    try {
      DateTime dateTime;
      dateTime = DateTime.parse(timestamp);

      return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
    } catch (e) {
      return '';
    }
  }

  String _getRelativeTime() {
    final timestamp = _getRelevantTimestamp();
    if (timestamp == null) return '';

    try {
      DateTime dateTime;
      dateTime = DateTime.parse(timestamp);

      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inDays > 30) {
        final months = (difference.inDays / 30).floor();
        return months == 1
            ? StringConstants.aMonthAgo.tr()
            : '$months ${StringConstants.daysAgo.tr()}';
      } else if (difference.inDays > 0) {
        return difference.inDays == 1
            ? StringConstants.aDayAgo.tr()
            : '${difference.inDays} ${StringConstants.daysAgo.tr()}';
      } else if (difference.inHours > 0) {
        return difference.inHours == 1
            ? StringConstants.anHourAgo.tr()
            : '${difference.inHours} ${StringConstants.hoursAgo.tr()}';
      } else if (difference.inMinutes > 0) {
        return difference.inMinutes == 1
            ? StringConstants.aMinuteAgo.tr()
            : '${difference.inMinutes} ${StringConstants.minutesAgo.tr()}';
      } else {
        return StringConstants.justNow.tr();
      }
    } catch (e) {
      return '';
    }
  }

  dynamic _getRelevantTimestamp() {
    // For pending (0) and processing (1), show createTime
    // For success (2) and rejected (3), show completeTime if available, otherwise createTime
    if (item.status == 0 || item.status == 1) {
      return item.createTime;
    } else {
      return item.completeTime ?? item.createTime;
    }
  }
}
