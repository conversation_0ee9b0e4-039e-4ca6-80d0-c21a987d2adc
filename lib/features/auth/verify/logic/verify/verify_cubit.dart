import 'dart:convert';
import 'dart:typed_data';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/auth/sign_up/domain/models/signup_captcha_model.dart';
import 'package:sf_app_v2/features/auth/sign_up/domain/models/signup_model.dart';
import 'package:sf_app_v2/features/auth/sign_up/domain/repository/sign_up_repository.dart';

part 'verify_state.dart';

@injectable
class VerifyCubit extends Cubit<VerifyState> {
  final SignUpRepository _signUpService;

  VerifyCubit(this._signUpService) : super(const VerifyState());

  void manageTimer({
    int timer = 0,
    bool showTimer = false,
    int otpTimer = 20,
  }) =>
      emit(
        state.copyWith(
          timer: timer,
          showTimer: showTimer,
          otpTimer: otpTimer,
        ),
      );

  Future<void> getRegisterCaptcha() async {
    emit(state.copyWith(registerCaptchaFetchStatus: DataStatus.loading));
    try {
      final result = await _signUpService.registerCaptcha();
      if (result.data != null) {
        int? commaIndex = result.data?.data.captcha.indexOf(',');
        Uint8List bytes = base64Decode(
          result.data?.data.captcha.substring(commaIndex! + 1) ?? '',
        );
        emit(
          state.copyWith(
            registerCaptchaData: result.data,
            registerCaptchaImage: bytes,
            registerCaptchaFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            registerCaptchaFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          registerCaptchaFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> sendCode({
    String? account,
    String? captcha,
    String? k,
  }) async {
    emit(
      state.copyWith(
        otpTimer: 1,
        showTimer: false,
        registerNonceFetchStatus: DataStatus.loading,
      ),
    );
    try {
      final result = await _signUpService.sendCode(
        account: account ?? '',
        captcha: captcha ?? state.registerCaptchaData?.data.captcha ?? '',
        k: k ?? state.registerCaptchaData?.data.k ?? '',
      );
      if (result.data != null) {
        emit(
          state.copyWith(
            otpTimer: 20,
            showTimer: true,
            registerNonceFetchStatus: DataStatus.success,
            registerNonceData: result.data,
          ),
        );
      } else {
        emit(
          state.copyWith(
            registerNonceFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          registerNonceFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  void updateVerifyState({
    SignUpEmailCaptchaModel? registerNonceData,
  }) {
    emit(
      state.copyWith(
        registerNonceData: registerNonceData,
      ),
    );
  }

  void resetTimer() {
    emit(state.copyWith(otpTimer: 0, showTimer: false, isResend: false));
  }
}
