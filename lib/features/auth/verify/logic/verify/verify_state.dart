part of 'verify_cubit.dart';

class VerifyState extends Equatable {
  final SignUpModel? signUpData;
  final int? otpTimer;
  final bool showTimer;
  final int timer;
  final bool isResend;
  final String? error;
  final SignUpCaptchaModel? registerCaptchaData;
  final DataStatus registerCaptchaFetchStatus;
  final Uint8List? registerCaptchaImage;
  final DataStatus registerNonceFetchStatus;
  final SignUpEmailCaptchaModel? registerNonceData;
  const VerifyState({
    this.signUpData,
    this.otpTimer,
    this.showTimer = false,
    this.timer = 0,
    this.isResend = false,
    this.error,
    this.registerCaptchaData,
    this.registerCaptchaFetchStatus = DataStatus.idle,
    this.registerCaptchaImage,
    this.registerNonceFetchStatus = DataStatus.idle,
    this.registerNonceData,
  });

  @override
  List<Object?> get props => [
        signUpData,
        otpTimer,
        showTimer,
        timer,
        isResend,
        error,
        registerCaptchaData,
        registerCaptchaFetchStatus,
        registerCaptchaImage,
        registerNonceFetchStatus,
        registerNonceData,
      ];

  VerifyState copyWith({
    SignUpModel? signUpData,
    int? otpTimer,
    bool? showTimer,
    int? timer,
    bool? isResend,
    String? error,
    SignUpCaptchaModel? registerCaptchaData,
    Uint8List? registerCaptchaImage,
    DataStatus? registerCaptchaFetchStatus,
    DataStatus? registerNonceFetchStatus,
    SignUpEmailCaptchaModel? registerNonceData,
  }) =>
      VerifyState(
        signUpData: signUpData ?? this.signUpData,
        otpTimer: otpTimer ?? this.otpTimer,
        showTimer: showTimer ?? this.showTimer,
        timer: timer ?? this.timer,
        isResend: isResend ?? this.isResend,
        error: error ?? this.error,
        registerCaptchaData: registerCaptchaData ?? this.registerCaptchaData,
        registerCaptchaImage: registerCaptchaImage ?? this.registerCaptchaImage,
        registerCaptchaFetchStatus:
            registerCaptchaFetchStatus ?? this.registerCaptchaFetchStatus,
        registerNonceFetchStatus:
            registerNonceFetchStatus ?? this.registerNonceFetchStatus,
        registerNonceData: registerNonceData ?? this.registerNonceData,
      );
}
