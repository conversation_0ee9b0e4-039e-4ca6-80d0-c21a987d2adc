import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/features/auth/forgot/logic/forgot/forgot_cubit.dart';
import 'package:slide_countdown/slide_countdown.dart';

class TimerWidgetForgotPassword extends StatelessWidget {
  final int? seconds;
  final Function()? onEnd;
  final Function()? onChange;

  const TimerWidgetForgotPassword({
    super.key,
    this.seconds,
    this.onEnd,
    this.onChange,
  });

  @override
  Widget build(BuildContext context) {
    return SlideCountdown(
      shouldShowDays: (_) => false,
      shouldShowHours: (_) => false,
      shouldShowMinutes: (_) => false,
      padding: EdgeInsets.zero,
      decoration: const BoxDecoration(),
      style: FontPalette.medium14
          .copyWith(color: myColorScheme(context).primaryColor),
      separatorStyle: FontPalette.medium14
          .copyWith(color: myColorScheme(context).primaryColor),
      showZeroValue: true,
      onChanged: (v) {
        context.read<ForgotCubit>().setTimerForgotPassword(v.inSeconds);
      },
      duration: Duration(seconds: seconds ?? 0),
    );
  }
}
