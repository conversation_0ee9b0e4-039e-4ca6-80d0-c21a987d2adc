import 'package:freezed_annotation/freezed_annotation.dart';

part 'forgot_password.freezed.dart';
part 'forgot_password.g.dart';

@freezed
class ForgotPassword with _$ForgotPassword {
  const factory ForgotPassword({
    required int code,
    required ForgotPasswordData data,
    required String msg,
  }) = _ForgotPassword;

  factory ForgotPassword.fromJson(Map<String, dynamic> json) =>
      _$ForgotPasswordFromJson(json);
}

@freezed
class ForgotPasswordData with _$ForgotPasswordData {
  const factory ForgotPasswordData({
    required String nonce,
  }) = _ForgotPasswordData;

  factory ForgotPasswordData.fromJson(Map<String, dynamic> json) =>
      _$ForgotPasswordDataFromJson(json);
}
