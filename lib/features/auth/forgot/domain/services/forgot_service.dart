import 'package:dio/dio.dart';
import 'package:sf_app_v2/core/api/endpoint/api_endpoints.dart';
import 'package:sf_app_v2/core/api/network/network.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/auth/forgot/domain/models/forgot_password.dart';
import '../repository/forgot_repository.dart';

/// Service class that implements the ForgotRepository interface to handle password reset functionality
class ForgotService implements ForgotRepository {
  /// Initiates password reset by sending email
  /// [account] - User's email address
  /// Returns a [ResponseResult] containing [ForgotPassword] on success
  @override
  Future<ResponseResult<ForgotPassword>> forgotPassword({
    required String account,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.emailSendForgotPassword}?account=${Uri.encodeComponent(account)}',
        options: Options(
          headers: {'auth': false},
        ),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: ForgotPassword.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to send forgot password email');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Verifies the password reset code sent to user's email
  /// [account] - User's email address
  /// [code] - Reset code from email
  /// [nonce] - One-time use security token
  /// Returns a [ResponseResult] containing [ForgotPassword] on success
  @override
  Future<ResponseResult<ForgotPassword>> checkForgotPasswordCode({
    required String account,
    required String code,
    required String nonce,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.emailCheckForgotPassword,
        data: {"account": account, "nonce": nonce, "code": code},
        options: Options(
          headers: {'auth': false},
        ),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: ForgotPassword.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to check forgot password code');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Changes user's password after successful verification
  /// [email] - User's email address
  /// [nonce] - One-time use security token
  /// [password] - New password
  /// [confirmPassword] - Confirmation of new password
  /// Returns a [ResponseResult] containing bool on success
  @override
  Future<ResponseResult<bool>> changePassword({
    required String email,
    required String nonce,
    required String password,
    required String confirmPassword,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.changePassword,
        data: {
          "email": email,
          "nonce": nonce,
          "password": password,
          "confirmPassword": confirmPassword,
        },
        options: Options(
          headers: {'auth': false},
        ),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to change password');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
