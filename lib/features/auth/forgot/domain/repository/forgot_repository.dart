import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/auth/forgot/domain/models/forgot_password.dart';

abstract class ForgotRepository {
  const ForgotRepository();

  Future<ResponseResult<ForgotPassword>> forgotPassword({
    required String account,
  });
  Future<ResponseResult<ForgotPassword>> checkForgotPasswordCode({
    required String code,
    required String account,
    required String nonce,
  });
  Future<ResponseResult<bool>> changePassword({
    required String email,
    required String nonce,
    required String password,
    required String confirmPassword,
  });
}
