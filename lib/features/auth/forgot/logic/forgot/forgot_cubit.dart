import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/auth/forgot/domain/repository/forgot_repository.dart';
import 'package:sf_app_v2/features/auth/forgot/domain/services/forgot_service.dart';

import '../../../../../core/models/phone_country/phone_country.dart';

part 'forgot_state.dart';

class ForgotCubit extends Cubit<ForgotState> {
  ForgotCubit() : super(const ForgotState());
  final ForgotRepository _forgotService = ForgotService();

  Future<void> forgotPassword(String account) async {
    emit(state.copyWith(emailSendStatus: DataStatus.loading));
    try {
      final result = await _forgotService.forgotPassword(account: account);
      if (result.data != null) {
        emit(
          state.copyWith(
            nonceConfirmEmail: result.data?.data.nonce ?? '',
            emailSendStatus: DataStatus.success,
            timerForgotPassword: 20,
          ),
        );
      } else {
        emit(
          state.copyWith(
            emailSendStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          emailSendStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> checkForgotPasswordCode(String code, {String? account}) async {
    final currentState = state;
    final accountToUse = account ?? currentState.email;
    emit(currentState.copyWith(checkForgotPasswordStatus: DataStatus.loading));
    try {
      final result = await _forgotService.checkForgotPasswordCode(
        account: accountToUse,
        nonce: currentState.nonceConfirmEmail,
        code: code,
      );
      if (result.data != null) {
        emit(
          currentState.copyWith(
            nonceConfirmReset: result.data?.data.nonce ?? '',
            checkForgotPasswordStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          currentState.copyWith(
            checkForgotPasswordStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        currentState.copyWith(
          checkForgotPasswordStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> changePassword(
    String password,
    confirmPassword, {
    String? account,
  }) async {
    final currentState = state;
    final accountToUse = account ?? currentState.email;
    emit(currentState.copyWith(changePasswordStatus: DataStatus.loading));
    try {
      final result = await _forgotService.changePassword(
        email: accountToUse,
        nonce: currentState.nonceConfirmReset,
        password: password,
        confirmPassword: confirmPassword,
      );

      if (result.data != null) {
        emit(
          currentState.copyWith(
            changePasswordStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          currentState.copyWith(
            changePasswordStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        currentState.copyWith(
          changePasswordStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  void setAccount(String account) {
    emit(state.copyWith(email: account));
  }

  void setNonceConfirmReset(String nonce) {
    emit(state.copyWith(nonceConfirmReset: nonce));
  }

  void setTimerForgotPassword(int seconds) {
    emit(state.copyWith(timerForgotPassword: seconds));
  }
  void setSelectedCountryCode(PhoneCountry countryCode) => emit(state.copyWith(selectedCountryCode: countryCode));

}
