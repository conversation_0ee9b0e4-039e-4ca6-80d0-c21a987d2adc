part of 'forgot_cubit.dart';

class ForgotState extends Equatable {
  final String email;
  final String nonceConfirmEmail;
  final String nonceConfirmReset;
  final DataStatus emailSendStatus;
  final DataStatus checkForgotPasswordStatus;
  final DataStatus changePasswordStatus;
  final String? error;
  final int timerForgotPassword;
  final PhoneCountry? selectedCountryCode;
  const ForgotState({
    this.email = '',
    this.nonceConfirmEmail = '',
    this.nonceConfirmReset = '',
    this.emailSendStatus = DataStatus.idle,
    this.checkForgotPasswordStatus = DataStatus.idle,
    this.changePasswordStatus = DataStatus.idle,
    this.error,
    this.timerForgotPassword = 0,
    this.selectedCountryCode,
  });

  @override
  List<Object?> get props => [
        email,
        nonceConfirmEmail,
        nonceConfirmReset,
        emailSendStatus,
        checkForgotPasswordStatus,
        changePasswordStatus,
        error,
        timerForgotPassword,
        selectedCountryCode,
      ];

  ForgotState copyWith({
    String? email,
    String? nonceConfirmEmail,
    String? nonceConfirmReset,
    DataStatus? emailSendStatus,
    DataStatus? checkForgotPasswordStatus,
    DataStatus? changePasswordStatus,
    String? error,
    int? timerForgotPassword,
    PhoneCountry? selectedCountryCode,
  }) {
    return ForgotState(
      email: email ?? this.email,
      nonceConfirmEmail: nonceConfirmEmail ?? this.nonceConfirmEmail,
      nonceConfirmReset: nonceConfirmReset ?? this.nonceConfirmReset,
      emailSendStatus: emailSendStatus ?? this.emailSendStatus,
      checkForgotPasswordStatus:
          checkForgotPasswordStatus ?? this.checkForgotPasswordStatus,
      changePasswordStatus: changePasswordStatus ?? this.changePasswordStatus,
      error: error ?? this.error,
      timerForgotPassword: timerForgotPassword ?? this.timerForgotPassword,
      selectedCountryCode: selectedCountryCode ?? this.selectedCountryCode,
    );
  }
}
