import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/common_function.dart';
import '../../../../core/constants/assets.dart';
import '../../../../core/constants/enums.dart';
import '../../../../core/constants/string_constants.dart';
import '../../../../core/theme/font_pallette.dart';
import '../../../../core/theme/my_color_scheme.dart';
import '../../../../core/utils/mixin/animation.dart';
import '../../../../core/validator.dart';
import '../../../../core/widgets/common_text_field.dart';
import '../../../../core/widgets/custom_alert_dialog.dart';
import '../../../../core/widgets/custom_button.dart';
import '../logic/forgot/forgot_cubit.dart';

class ResetPasswordScreen extends StatefulWidget {
  const ResetPasswordScreen({super.key});

  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> with Validator, StaggeredAnimation {
  late FocusNode firstNameFocusNode;
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final formGlobalKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    firstNameFocusNode = FocusNode();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: AnimationLimiter(
            child: Center(
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.arrow_back_ios),
                      ),
                    ],
                  ),
                  128.verticalSpace,
                  Padding(
                    padding: const EdgeInsets.all(30).r,
                    child: Hero(
                      tag: Assets.logoSvg,
                      child: SvgPicture.asset(
                        Assets.logoSvg,
                        width: 99.w,
                        height: 121.h,
                      ),
                    ),
                  ),
                  Center(
                    child: Text(
                      StringConstants.resetPassword.tr(),
                      style: FontPalette.bold21.copyWith(color: myColorScheme(context).primaryColor),
                    ),
                  ),
                  52.verticalSpace,
                  Form(
                    key: formGlobalKey,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 37.0),
                      child: Column(
                        children: staggeredAnimation(
                          children: [
                            CommonTextField(
                              autovalidateMode: AutovalidateMode.onUserInteraction,
                              enableObscure: true,
                              textInputType: TextInputType.text,
                              textInputAction: TextInputAction.next,
                              labelText: StringConstants.password.tr(),
                              hintText: StringConstants.passwordHintText.tr(),
                              controller: passwordController,
                              validator: (_) => validatePassword(
                                passwordController.text,
                                msg: StringConstants.atLeast8character.tr(),
                              ),
                              onChanged: (_) {},
                            ),
                            35.verticalSpace,
                            CommonTextField(
                              autovalidateMode: AutovalidateMode.onUserInteraction,
                              enableObscure: true,
                              textInputType: TextInputType.text,
                              textInputAction: TextInputAction.next,
                              labelText: StringConstants.confirmHintText.tr(),
                              hintText: StringConstants.confirmHintText.tr(),
                              controller: confirmPasswordController,
                              validator: (value) => validateConfirmPassword(
                                passwordController.text,
                                confirmPasswordController.text,
                              ),
                              onChanged: (_) {},
                            ),
                            40.verticalSpace,
                            BlocConsumer<ForgotCubit, ForgotState>(
                              listenWhen: (previous, current) =>
                                  previous.changePasswordStatus != current.changePasswordStatus,
                              listener: (context, state) {
                                if (state.changePasswordStatus == DataStatus.success) {
                                  CommonFunctions.showDialogPopUp(
                                    context,
                                    CustomAlertDialog(
                                      message: StringConstants.passwordUpdatedSuccessfully.tr(),
                                      actionButtonText: StringConstants.ok.tr(),
                                      buttonBackGroundColor: myColorScheme(context).primaryColor,
                                      onActionButtonPressed: () async {
                                        Navigator.pop(context);
                                        Navigator.pop(context);
                                        Navigator.pop(context);
                                      },
                                      headerImage: Assets.alertSuccess,
                                      isLoading: false,
                                      messageTextStyle: FontPalette.semiBold19.copyWith(),
                                    ),
                                  );
                                }
                              },
                              builder: (context, state) {
                                return CustomButton(
                                  width: 299.w,
                                  height: 52.h,
                                  label: StringConstants.submit.tr(),
                                  isLoading: state.changePasswordStatus == DataStatus.loading,
                                  isOutlined: false,
                                  onPressed: () {
                                    if (formGlobalKey.currentState!.validate()) {
                                      context.read<ForgotCubit>().changePassword(
                                            passwordController.text,
                                            confirmPasswordController.text,
                                          );
                                    }
                                  },
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
