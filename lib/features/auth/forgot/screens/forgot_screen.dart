import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/constants/assets.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/validator.dart';
import 'package:sf_app_v2/core/widgets/common_text_field.dart';
import 'package:sf_app_v2/core/widgets/custom_country_picker.dart';
import 'package:sf_app_v2/core/models/phone_country/phone_country.dart';
import 'package:sf_app_v2/features/auth/forgot/logic/forgot/forgot_cubit.dart';
import 'package:sf_app_v2/features/auth/forgot/widgets/timer_forgot_password.dart';
import '../../../../core/api/network/network_helper.dart';
import '../../../../core/common_function.dart';
import '../../../../core/shared/logic/country_code/country_code_cubit.dart';
import '../../../../core/utils/mixin/animation.dart';
import '../../../../core/widgets/common_pin_field.dart';
import '../../../../core/widgets/custom_alert_dialog.dart';
import '../../../../core/widgets/custom_button.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen>
    with
        Validator,
        StaggeredAnimation,
        SingleTickerProviderStateMixin {
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final pinController = TextEditingController();
  final formGlobalKeyEmail = GlobalKey<FormState>();
  final formGlobalKeyCode = GlobalKey<FormState>();
  late TabController _tabController;
  bool _isEmailTab = true;
  bool codeSent = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_handleTabChange);
  }

  void _handleTabChange() {
    if (!_tabController.indexIsChanging) {
      setState(() {
        _isEmailTab = _tabController.index == 0;
      });
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    emailController.dispose();
    phoneController.dispose();
    pinController.dispose();
    super.dispose();
  }

  bool check() {
    if (int.tryParse(pinController.text) == null) {
      return false;
    } else {
      return true;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SafeArea(
          child: SingleChildScrollView(
            child: AnimationLimiter(
              child: Center(
                child: Column(
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.arrow_back_ios),
                        ),
                      ],
                    ),
                    80.verticalSpace,
                    Padding(
                      padding: const EdgeInsets.all(30).r,
                      child: Hero(
                        tag: Assets.logoSvg,
                        child: SvgPicture.asset(
                          Assets.logoSvg,
                          width: 99.w,
                          height: 121.h,
                        ),
                      ),
                    ),
                    Text(
                      StringConstants.authentication.tr(),
                      style: FontPalette.bold21
                          .copyWith(color: myColorScheme(context).primaryColor),
                    ),
                    52.verticalSpace,
                    Form(
                      key: formGlobalKeyEmail,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 37.0),
                        child: Column(
                          children: staggeredAnimation(
                            children: [
                              _buildTabBar(),
                              35.verticalSpace,
                              _isEmailTab
                                  ? _buildEmailField()
                                  : _buildPhoneField(),
                              16.verticalSpace,
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  BlocListener<ForgotCubit, ForgotState>(
                                    listenWhen: (previous, current) =>
                                        previous.emailSendStatus !=
                                        current.emailSendStatus,
                                    listener: (context, state) {
                                      if (state.emailSendStatus ==
                                          DataStatus.success) {
                                        CommonFunctions.showDialogPopUp(
                                          context,
                                          CustomAlertDialog(
                                            message: StringConstants
                                                .sendCodeAlert
                                                .tr(),
                                            actionButtonText: _isEmailTab
                                                ? StringConstants.checkEmail
                                                    .tr()
                                                : StringConstants.checkPhone
                                                    .tr(),
                                            buttonBackGroundColor:
                                                ColorPalette.primaryVar1,
                                            onActionButtonPressed: () =>
                                                Navigator.pop(context),
                                            headerImage: Assets.alertSuccess,
                                            isLoading: false,
                                            messageTextStyle:
                                                FontPalette.medium20,
                                          ),
                                          barrierDismissible: false,
                                        );
                                        setState(() {
                                          codeSent = true;
                                        });
                                      }
                                      if (state.emailSendStatus ==
                                          DataStatus.failed) {
                                        NetworkHelper.handleMessage(
                                          state.error,
                                          context,
                                          type: HandleTypes.customDialog,
                                          snackBarType: SnackBarType.error,
                                        );
                                      }
                                    },
                                    child: BlocSelector<ForgotCubit,
                                        ForgotState, (int?, DataStatus)>(
                                      selector: (state) => (
                                        state.timerForgotPassword,
                                        state.emailSendStatus
                                      ),
                                      builder: (context, data) {
                                        if (data.$1 == 0) {
                                          return CustomButton(
                                            width: 100.w,
                                            height: 32.h,
                                            isLoading:
                                                data.$2 == DataStatus.loading,
                                            label: codeSent
                                                ? StringConstants.resendCode
                                                    .tr()
                                                : StringConstants.sendCode.tr(),
                                            onPressed: () {
                                              if (formGlobalKeyEmail
                                                  .currentState!
                                                  .validate()) {
                                                String countryCode = context
                                                        .read<ForgotCubit>()
                                                        .state
                                                        .selectedCountryCode
                                                        ?.phoneCode ??
                                                    '+52';
                                                final String contactInfo =
                                                    _isEmailTab
                                                        ? emailController.text
                                                        : countryCode +
                                                            phoneController
                                                                .text;
                                                context
                                                    .read<ForgotCubit>()
                                                    .setAccount(
                                                      contactInfo,
                                                    );
                                                context
                                                    .read<ForgotCubit>()
                                                    .forgotPassword(
                                                      contactInfo,
                                                    );
                                              }
                                            },
                                          );
                                        } else {
                                          return CustomButton(
                                            isEnabled: false,
                                            isLoading: false,
                                            label: '',
                                            btnTextStyle:
                                                FontPalette.medium14.copyWith(
                                              color: ColorPalette.primaryVar1,
                                            ),
                                            width: 120.w,
                                            height: 32.h,
                                            borderRadiusUser: 26,
                                            isOutlined: true,
                                            child: Row(
                                              // mainAxisSize: MainAxisSize.min,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                TimerWidgetForgotPassword(
                                                  seconds: data.$1,
                                                ),
                                                4.horizontalSpace,
                                                Text(
                                                  StringConstants.secondsUpper
                                                      .tr(),
                                                  style: FontPalette.normal10
                                                      .copyWith(
                                                    color: myColorScheme(
                                                      context,
                                                    ).primaryColor,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          );
                                        }
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    50.verticalSpace,
                    Form(
                      key: formGlobalKeyCode,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 37.0),
                        child: Column(
                          children: AnimationConfiguration.toStaggeredList(
                            childAnimationBuilder: (widget) => SlideAnimation(
                              verticalOffset: 50.0,
                              child: FadeInAnimation(
                                child: widget,
                              ),
                            ),
                            children: [
                              Align(
                                alignment: Alignment.topLeft,
                                child: Text(
                                  StringConstants.code.tr(),
                                  style: FontPalette.normal14.copyWith(
                                    color: myColorScheme(context).primaryColor,
                                  ),
                                  textAlign: TextAlign.start,
                                ),
                              ),
                              8.verticalSpace,
                              CommonPinFiledText(
                                type: 'code',
                                controller: pinController,
                              ),
                              40.verticalSpace,
                              BlocListener<ForgotCubit, ForgotState>(
                                listenWhen: (previous, current) =>
                                    previous.checkForgotPasswordStatus !=
                                    current.checkForgotPasswordStatus,
                                listener: (context, state) {
                                  if (state.checkForgotPasswordStatus ==
                                      DataStatus.success) {
                                    Navigator.pushNamed(
                                      context,
                                      routeResetPassword,
                                    );
                                  }
                                  if (state.checkForgotPasswordStatus ==
                                      DataStatus.failed) {
                                    pinController.clear();
                                    NetworkHelper.handleMessage(
                                      state.error,
                                      context,
                                      type: HandleTypes.customDialog,
                                      snackBarType: SnackBarType.error,
                                    );
                                  }
                                },
                                child: BlocSelector<ForgotCubit, ForgotState,
                                    DataStatus>(
                                  selector: (state) =>
                                      state.checkForgotPasswordStatus,
                                  builder: (context, state) {
                                    return CustomButton(
                                      isEnabled: codeSent,
                                      isLoading: state == DataStatus.loading,
                                      width: 299.w,
                                      height: 52.h,
                                      label: StringConstants.submit.tr(),
                                      isOutlined: false,
                                      onPressed: () {
                                        FocusScope.of(context).unfocus();
                                        if (formGlobalKeyCode.currentState!
                                            .validate()) {
                                          String countryCode = context
                                                  .read<ForgotCubit>()
                                                  .state
                                                  .selectedCountryCode
                                                  ?.phoneCode ??
                                              '+52';
                                          final String contactInfo = _isEmailTab
                                              ? emailController.text
                                              : countryCode +
                                                  phoneController.text;
                                          context
                                              .read<ForgotCubit>()
                                              .setAccount(
                                                contactInfo,
                                              );
                                          context
                                              .read<ForgotCubit>()
                                              .checkForgotPasswordCode(
                                                pinController.text,
                                                account: contactInfo,
                                              );
                                        }
                                      },
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: myColorScheme(context).borderColor ?? Colors.grey,
            width: 1.0,
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        indicatorSize: TabBarIndicatorSize.tab,
        dividerHeight: 0,
        indicatorColor: myColorScheme(context).primaryColor,
        labelColor: myColorScheme(context).primaryColor,
        unselectedLabelColor: myColorScheme(context).titleColor,
        labelStyle: FontPalette.medium16,
        unselectedLabelStyle: FontPalette.normal16,
        tabs: [
          Tab(text: StringConstants.email.tr()),
          Tab(text: StringConstants.phoneNumber.tr()),
        ],
      ),
    );
  }

  Widget _buildEmailField() {
    return CommonTextField(
      prefixIcon: SvgPicture.asset(
        Assets.emailIcon,
        height: 11.h,
        width: 11.w,
      ),
      textInputType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      labelText: StringConstants.emailLabelText.tr(),
      hintText: StringConstants.emailHintText.tr(),
      controller: emailController,
      validator: (_) => validateEmail(emailController.text),
      onChanged: (_) {},
    );
  }

  Widget _buildPhoneField() {
    return Stack(
      children: [
        CommonTextField(
          autofillHints: const [AutofillHints.telephoneNumber],
          prefixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              BlocBuilder<CountryCodeCubit, CountryCodeState>(
                builder: (context, cState) {
                  return CustomCountryPicker(
                    onChanged: (PhoneCountry countryCode) {
                      context
                          .read<ForgotCubit>()
                          .setSelectedCountryCode(countryCode);
                    },
                    onInit: (countryCode) {
                      context
                          .read<ForgotCubit>()
                          .setSelectedCountryCode(countryCode ??
                              const PhoneCountry(
                                phoneCode: '+52',
                                name: 'Mexico',
                                flagEmoji: '🇲🇽',
                                code: 'MX',
                              ));
                    },
                    initialSelectionCode: cState.countryCode?.isoCode ?? 'MX',
                    showCountryOnly: false,
                    showOnlyCountryWhenClosed: false,
                    alignLeft: false,
                    padding: EdgeInsets.zero,
                    textStyle: FontPalette.normal14,
                    showFlag: true,
                    showDropDownButton: true,
                    builder: (PhoneCountry? countryCode) {
                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            countryCode?.flagEmoji ?? '🇲🇽',
                            style: TextStyle(fontSize: 15.sp),
                          ),
                          4.horizontalSpace,
                          Text(
                            countryCode?.phoneCode ?? '+52',
                            style: FontPalette.normal14,
                          ),
                          Icon(
                            Icons.arrow_drop_down_sharp,
                            size: 24.w,
                            color: Colors.grey.shade600,
                          ),
                          Container(
                            width: 1.0.w,
                            height: 18.h,
                            color: Colors.grey.shade300,
                            margin: EdgeInsets.only(right: 10.w, left: 4.w),
                          ),
                        ],
                      );
                    },
                  );
                },
              ),
            ],
          ),
          textInputAction: TextInputAction.next,
          labelText: StringConstants.phoneNumber.tr(),
          hintText: StringConstants.enterPhoneNumber.tr(),
          controller: phoneController,
          textInputType: TextInputType.phone,
          validator: (_) => validateMobile(phoneController.text),
          onChanged: (_) {},
        ),
      ],
    );
  }
}
