import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:sf_app_v2/core/widgets/custom_country_picker.dart';
import 'package:sf_app_v2/core/models/phone_country/phone_country.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/common_function.dart';
import 'package:sf_app_v2/core/constants/assets.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/common_text_field.dart';
import 'package:sf_app_v2/core/widgets/custom_alert_dialog.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';
import 'package:sf_app_v2/core/widgets/language_selector_widget.dart';
import 'package:sf_app_v2/features/auth/sign_up/domain/models/signup_model.dart';
import 'package:sf_app_v2/features/auth/sign_up/logic/sign_up/sign_up_cubit.dart';
import 'package:sf_app_v2/features/auth/verify/logic/verify/verify_cubit.dart';

import '../../../../core/api/network/network_helper.dart';
import '../../../../core/shared/logic/country_code/country_code_cubit.dart';
import '../../../../core/utils/global.dart' as global;
import '../../../../core/utils/mixin/animation.dart';
import '../../../../core/widgets/common_pin_field.dart';
import '../../../../core/widgets/timer_widget.dart';
import '../../../../core/validator.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key, this.isFromHome});
  final bool? isFromHome;

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen>
    with Validator, StaggeredAnimation, SingleTickerProviderStateMixin {
  late FocusNode firstNameFocusNode;
  final userNameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final confirmCodeBoxController = TextEditingController();
  final invitationCodeController = TextEditingController();
  final otpController = TextEditingController();
  bool cbAgreed = false;
  bool showError = false;
  final _formGlobalKey = GlobalKey<FormState>();
  Timer? debounceTimer;
  late TabController _tabController;
  bool _isEmailTab = true;

  @override
  void dispose() {
    debounceTimer?.cancel();
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    phoneController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    firstNameFocusNode = FocusNode();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_handleTabChange);
    CommonFunctions.afterInit(_initialFunction);
  }

  void _handleTabChange() {
    if (!_tabController.indexIsChanging) {
      setState(() {
        _isEmailTab = _tabController.index == 0;
      });
      context.read<VerifyCubit>().resetTimer();
    }
  }

  _initialFunction() {
    if (global.invitationCode != null) {
      context.read<SignUpCubit>().setInviteCode(global.invitationCode ?? '');
    }
    context.read<VerifyCubit>().resetTimer();
  }

  bool isClosingPopup = false;
  void delayedPop({Function? function}) {
    if (isClosingPopup) return;
    isClosingPopup = true;
    Navigator.pop(context);
    if (function != null) function();
    Future.delayed(const Duration(milliseconds: 200), () {
      isClosingPopup = false;
    });
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: myColorScheme(context).borderColor ?? Colors.grey,
            width: 1.0,
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        indicatorSize: TabBarIndicatorSize.tab,
        dividerHeight: 0,
        indicatorColor: myColorScheme(context).primaryColor,
        indicatorWeight: 1.0,
        labelColor: myColorScheme(context).primaryColor,
        unselectedLabelColor: myColorScheme(context).titleColor,
        labelStyle: FontPalette.medium16,
        unselectedLabelStyle: FontPalette.normal16,
        tabs: [
          Tab(text: StringConstants.email.tr()),
          Tab(text: StringConstants.phoneNumber.tr()),
        ],
      ),
    );
  }

  Widget _buildEmailField() {
    return CommonTextField(
      prefixIcon: SvgPicture.asset(
        Assets.emailIcon,
        height: 11.h,
        width: 11.w,
      ),
      textInputType: TextInputType.emailAddress,
      textInputAction: TextInputAction.next,
      labelText: StringConstants.emailLabelText.tr(),
      hintText: StringConstants.emailHintText.tr(),
      suffixIcon: _sendCodeWidget(),
      controller: emailController,
      validator: (_) => validateEmail(emailController.text),
      textInputFormatter: [
        FilteringTextInputFormatter.deny(
            RegExp(r'^\s+')), // Prevent leading spaces
      ],
      onChanged: (_) {},
    );
  }

  Widget _sendCodeWidget() {
    return BlocBuilder<VerifyCubit, VerifyState>(
      builder: (context, state) {
        if (state.showTimer) {
          return Padding(
            padding: EdgeInsets.all(5.0.w),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TimerWidget(
                  color: myColorScheme(context).subTitleColor,
                  seconds: state.otpTimer ?? 0,
                  onEnd: () {
                    context.read<VerifyCubit>().manageTimer(
                          timer: 0,
                          showTimer: false,
                        );
                  },
                ),
                Text(
                  ' (${StringConstants.seconds.tr().toLowerCase()})',
                  style: FontPalette.normal10.copyWith(
                    color: myColorScheme(context).subTitleColor,
                  ),
                ),
                10.horizontalSpace,
              ],
            ),
          );
        }
        return Padding(
          padding: EdgeInsets.all(5.0.w),
          child: OutlinedButton(
            onPressed: () {
              if (_isEmailTab && emailController.text.isEmpty) {
                // Show error if email is empty
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(StringConstants.emptyEmailMsg.tr()),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              } else if (!_isEmailTab && phoneController.text.isEmpty) {
                // Show error if phone is empty
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(StringConstants.enterPhoneNumber.tr()),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              // Get captcha and show dialog
              context.read<VerifyCubit>().getRegisterCaptcha();
              _showCaptchaDialog(context);
            },
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                  color: myColorScheme(context).borderColor ?? Colors.grey),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.0),
              ),
              padding: EdgeInsets.symmetric(horizontal: 12.w),
              minimumSize: Size(100.w, 36.h),
            ),
            child: Text(
              StringConstants.sendCode1.tr(),
              style: TextStyle(
                color: myColorScheme(context).primaryColor,
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      },
    );
  }

  void _showCaptchaDialog(BuildContext parentContext) {
    final captchaController = TextEditingController();

    CommonFunctions.showDialogPopUp(
      parentContext,
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30.r),
        ),
        insetPadding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Builder(builder: (BuildContext dialogContext) {
          return Container(
            width: 350.w,
            padding: EdgeInsets.symmetric(vertical: 30.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  StringConstants.sendCode.tr(),
                  style: FontPalette.bold20.copyWith(
                    color: myColorScheme(dialogContext).primaryColor,
                  ),
                ),
                20.verticalSpace,
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Text(
                    StringConstants.sendCodeAlert.tr(),
                    textAlign: TextAlign.center,
                    style: FontPalette.medium16.copyWith(
                      color: myColorScheme(dialogContext).titleColor,
                    ),
                  ),
                ),
                30.verticalSpace,
                Text(
                  StringConstants.captchaCode.tr(),
                  style: FontPalette.medium14.copyWith(
                    color: myColorScheme(dialogContext).primaryColor,
                  ),
                ),
                15.verticalSpace,
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Captcha input field
                    SizedBox(
                      width: 120.w,
                      child: CommonTextField(
                        fillColor: myColorScheme(dialogContext).cardColor,
                        borderColor: myColorScheme(dialogContext).cardColor,
                        textInputType: TextInputType.text,
                        textInputAction: TextInputAction.done,
                        hintText: StringConstants.enterTheCode.tr(),
                        controller: captchaController,
                        validator: (_) =>
                            validateCaptcha(captchaController.text),
                        onChanged: (_) {},
                      ),
                    ),
                    15.horizontalSpace,
                    // Captcha image
                    BlocSelector<VerifyCubit, VerifyState,
                        (DataStatus, Uint8List?)>(
                      selector: (state) => (
                        state.registerCaptchaFetchStatus,
                        state.registerCaptchaImage,
                      ),
                      builder: (context, state) {
                        switch (state.$1) {
                          case DataStatus.success:
                            return Container(
                              width: 100.w,
                              height: 45.h,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5.r),
                                color: Colors.white,
                                image: DecorationImage(
                                  image: MemoryImage(state.$2 ?? Uint8List(0)),
                                  fit: BoxFit.fill,
                                ),
                              ),
                            );
                          case DataStatus.loading:
                            return Center(
                              child: Container(
                                width: 45.h,
                                height: 45.h,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5.r),
                                  color: Colors.transparent,
                                ),
                                child:
                                    const CircularProgressIndicator.adaptive(),
                              ),
                            );
                          default:
                            return SizedBox(
                              width: 100.w,
                              height: 45.h,
                            );
                        }
                      },
                    ),
                    15.horizontalSpace,
                    // Refresh button
                    InkWell(
                      onTap: () {
                        parentContext.read<VerifyCubit>().getRegisterCaptcha();
                      },
                      child: Container(
                        width: 45.h,
                        height: 45.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5.r),
                          color: myColorScheme(dialogContext).primaryColor,
                        ),
                        child: Icon(
                          Icons.refresh,
                          color: myColorScheme(dialogContext).cardColor,
                        ),
                      ),
                    ),
                  ],
                ),
                30.verticalSpace,
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: BlocConsumer<VerifyCubit, VerifyState>(
                    listenWhen: (previous, current) =>
                        previous.registerNonceFetchStatus !=
                        current.registerNonceFetchStatus,
                    listener: (context, state) {
                      if (state.registerNonceFetchStatus ==
                          DataStatus.success) {
                        // Store the necessary data before popping the dialog
                        final registerNonceData = state.registerNonceData;

                        // Close captcha dialog first
                        Navigator.of(dialogContext).pop();

                        // Store the necessary data for later use
                        final BuildContext contextToUse = parentContext;
                        final SignUpEmailCaptchaModel? dataToUse =
                            registerNonceData;

                        // Use a small delay to ensure the first dialog is fully dismissed
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          // Now show the success dialog
                          CommonFunctions.showDialogPopUp(
                            contextToUse,
                            CustomAlertDialog(
                              message: StringConstants.sendCodeAlert.tr(),
                              actionButtonText: StringConstants.ok.tr(),
                              buttonBackGroundColor: ColorPalette.primaryVar1,
                              onActionButtonPressed: () {
                                Navigator.of(contextToUse).pop();

                                // Update the state after dialog is closed
                                if (dataToUse != null) {
                                  contextToUse
                                      .read<VerifyCubit>()
                                      .updateVerifyState(
                                        registerNonceData: dataToUse,
                                      );
                                }
                              },
                              headerImage: Assets.alertSuccess,
                              isLoading: false,
                              messageTextStyle: FontPalette.medium20,
                            ),
                            barrierDismissible: false,
                          );
                        });
                      }
                    },
                    builder: (context, state) {
                      return BlocListener<VerifyCubit, VerifyState>(
                        listenWhen: (previous, current) =>
                            previous.registerNonceFetchStatus !=
                            current.registerNonceFetchStatus,
                        listener: (context, state) {
                          if (state.registerNonceFetchStatus ==
                              DataStatus.failed) {
                            captchaController.clear();
                            parentContext
                                .read<VerifyCubit>()
                                .getRegisterCaptcha();
                            NetworkHelper.handleMessage(
                              state.error,
                              context,
                              type: HandleTypes.customDialog,
                              snackBarType: SnackBarType.error,
                              onTap: delayedPop,
                            );
                          }
                        },
                        child: CustomButton(
                          width: 250.w,
                          height: 52.h,
                          label: StringConstants.sendCode.tr(),
                          isLoading: state.registerNonceFetchStatus ==
                              DataStatus.loading,
                          isOutlined: false,
                          onPressed: () {
                            String? k = parentContext
                                    .read<VerifyCubit>()
                                    .state
                                    .registerCaptchaData
                                    ?.data
                                    .k ??
                                '';
                            if (captchaController.text.isNotEmpty) {
                              if (_isEmailTab) {
                                parentContext.read<VerifyCubit>().sendCode(
                                      account: emailController.text,
                                      captcha: captchaController.text,
                                      k: k,
                                    );
                              } else {
                                parentContext.read<VerifyCubit>().sendCode(
                                      account: (parentContext
                                                  .read<SignUpCubit>()
                                                  .state
                                                  .selectedCountryCode
                                                  ?.phoneCode ??
                                              '+52') +
                                          phoneController.text,
                                      captcha: captchaController.text,
                                      k: k,
                                    );
                              }
                            } else {
                              ScaffoldMessenger.of(dialogContext).showSnackBar(
                                SnackBar(
                                  content:
                                      Text(StringConstants.enterCaptcha.tr()),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          },
                        ),
                      );
                    },
                  ),
                ),
                20.verticalSpace,
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                  child: Text(
                    StringConstants.cancel.tr(),
                    style: FontPalette.medium16.copyWith(
                      color: myColorScheme(dialogContext).primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Widget _buildPhoneField() {
    return Stack(
      children: [
        CommonTextField(
          autofillHints: const [AutofillHints.telephoneNumber],
          prefixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              BlocBuilder<CountryCodeCubit, CountryCodeState>(
                builder: (context, cState) {
                  return CustomCountryPicker(
                    onChanged: (PhoneCountry countryCode) {
                      context
                          .read<SignUpCubit>()
                          .setSelectedCountryCode(countryCode);
                    },
                    initialSelectionCode: cState.countryCode?.isoCode ?? 'MX',
                    showCountryOnly: false,
                    showOnlyCountryWhenClosed: false,
                    onInit: (countryCode) {
                      context
                          .read<SignUpCubit>()
                          .setSelectedCountryCode(countryCode ??
                              const PhoneCountry(
                                phoneCode: '+52',
                                name: 'Mexico',
                                flagEmoji: '🇲🇽',
                                code: 'MX',
                              ));
                    },
                    alignLeft: false,
                    padding: EdgeInsets.zero,
                    textStyle: FontPalette.normal14,
                    showFlag: true,
                    showDropDownButton: true,
                    builder: (PhoneCountry? countryCode) {
                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            countryCode?.phoneCode ?? '+52',
                            style: FontPalette.normal14,
                          ),
                          Icon(
                            Icons.arrow_drop_down_sharp,
                            size: 24.w,
                            color: Colors.grey.shade600,
                          ),
                          Container(
                            width: 1.0.w,
                            height: 18.h,
                            color: Colors.grey.shade300,
                            margin: EdgeInsets.only(right: 10.w, left: 4.w),
                          ),
                        ],
                      );
                    },
                  );
                },
              ),
            ],
          ),
          textInputAction: TextInputAction.next,
          labelText: StringConstants.phoneNumber.tr(),
          hintText: StringConstants.enterPhoneNumber.tr(),
          controller: phoneController,
          textInputType: TextInputType.phone,
          textInputFormatter: [
            FilteringTextInputFormatter.deny(
                RegExp(r'^\s+')), // Prevent leading spaces
          ],
          validator: (_) => validateMobile(phoneController.text),
          suffixIcon: _sendCodeWidget(),
          onChanged: (_) {},
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
        leading: BackButton(
          color: myColorScheme(context).appBarIconColor,
          onPressed: () {
            if (widget.isFromHome == true) {
              Navigator.pop(context);
            } else {
              Navigator.pushReplacementNamed(context, routeLoginScreen);
            }
          },
        ),
        actions: [
          LanguageSelectorWidget(
            fontSize: 25.sp,
            heroTag: 'flag_${context.locale.languageCode}',
          ),
          20.horizontalSpace,
        ],
      ),
      body: SafeArea(
        child: AnimationLimiter(
          child: Center(
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Hero(
                    tag: Assets.logoSvg,
                    child: SvgPicture.asset(
                      Assets.logoSvg,
                      width: 80.w,
                      height: 79.h,
                    ),
                  ),
                  20.verticalSpace,
                  Text(
                    StringConstants.signUp.tr(),
                    style: FontPalette.bold28.copyWith(
                      fontSize: 20.sp,
                      color: myColorScheme(context).primaryColor,
                    ),
                  ),
                  56.verticalSpace,
                  Form(
                    key: _formGlobalKey,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 30.0),
                      child: Column(
                        children: staggeredAnimation(
                          children: [
                            _buildTabBar(),
                            35.verticalSpace,
                            _isEmailTab
                                ? _buildEmailField()
                                : _buildPhoneField(),
                            35.verticalSpace,
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  _isEmailTab
                                      ? StringConstants.otp.tr()
                                      : StringConstants.otpPhone.tr(),
                                  style: FontPalette.normal14.copyWith(
                                    color: myColorScheme(context).primaryColor,
                                  ),
                                ),
                                10.verticalSpace,
                                CommonPinFiledText(
                                  type: 'code',
                                  controller: otpController,
                                ),
                              ],
                            ),
                            35.verticalSpace,
                            CommonTextField(
                              enableObscure: true,
                              textInputType: TextInputType.text,
                              textInputAction: TextInputAction.next,
                              labelText: StringConstants.password.tr(),
                              hintText: StringConstants.passwordHintText.tr(),
                              controller: passwordController,
                              validator: (_) => validatePassword(
                                passwordController.text,
                                msg: StringConstants.atLeast8character.tr(),
                              ),
                              onChanged: (_) {},
                            ),
                            35.verticalSpace,
                            CommonTextField(
                              enableObscure: true,
                              textInputType: TextInputType.text,
                              textInputAction: TextInputAction.next,
                              labelText: StringConstants.confirmHintText.tr(),
                              hintText: StringConstants.confirmHintText.tr(),
                              controller: confirmPasswordController,
                              validator: (value) => validateConfirmPassword(
                                passwordController.text,
                                confirmPasswordController.text,
                              ),
                              onChanged: (_) {},
                            ),
                            35.verticalSpace,
                            BlocConsumer<SignUpCubit, SignUpState>(
                              listenWhen: (previous, current) =>
                                  previous.checkCodeFetchStatus !=
                                  current.checkCodeFetchStatus,
                              listener: (context, state) {
                                if (state.checkCodeFetchStatus ==
                                    DataStatus.success) {}
                              },
                              builder: (context, state) {
                                return CommonTextField(
                                  textInputType: TextInputType.text,
                                  textInputAction: TextInputAction.next,
                                  labelText:
                                      StringConstants.invitationCode.tr(),
                                  hintText: StringConstants.invitationCode.tr(),
                                  controller: invitationCodeController,
                                  suffixIcon: (state.checkCodeFetchStatus ==
                                          DataStatus.loading)
                                      ? Padding(
                                          padding: const EdgeInsets.only(
                                              right: 10.0),
                                          child: CupertinoActivityIndicator(
                                              color: myColorScheme(context)
                                                  .primaryColor),
                                        )
                                      : null,
                                  autovalidateMode:
                                      AutovalidateMode.onUserInteraction,
                                  validator: (value) {
                                    if (debounceTimer != null) {
                                      return null;
                                    }
                                    return validateCharacters(
                                      invitationCodeController.text,
                                      msg: (!(state.isCheckCodeSuccess ??
                                                  false) &&
                                              state.checkCodeFetchStatus ==
                                                  DataStatus.success)
                                          ? StringConstants.invitationCodeError
                                              .tr()
                                          : null,
                                    );
                                  },
                                  onChanged: (value) {
                                    if (debounceTimer != null) {
                                      debounceTimer!.cancel();
                                    }
                                    debounceTimer = Timer(
                                        const Duration(seconds: 1), () async {
                                      if (value.isNotEmpty) {
                                        context.read<SignUpCubit>().checkCode(
                                            code:
                                                invitationCodeController.text);
                                      }
                                    });
                                  },
                                );
                              },
                            ),
                            20.verticalSpace,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Checkbox(
                                  value: cbAgreed,
                                  side: BorderSide(
                                    color: Theme.of(context).brightness ==
                                            Brightness.dark
                                        ? ColorPalette.cardColorDark
                                        : Colors.black,
                                    width: 1.5,
                                  ),
                                  activeColor:
                                      myColorScheme(context).primaryColor,
                                  onChanged: (val) {
                                    if (val != null) {
                                      setState(() {
                                        cbAgreed = val;
                                        showError = val ? false : true;
                                      });
                                    }
                                  },
                                ),
                                Expanded(
                                  child: GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        cbAgreed = !cbAgreed;
                                        showError = cbAgreed ? false : true;
                                      });
                                    },
                                    child: RichText(
                                      textAlign: TextAlign.start,
                                      text: TextSpan(
                                        style: FontPalette.normal12,
                                        children: <TextSpan>[
                                          TextSpan(
                                            text: StringConstants.iAgree.tr(),
                                            style:
                                                FontPalette.normal12.copyWith(
                                              color: ColorPalette.greyColor4,
                                            ),
                                          ),
                                          TextSpan(
                                            text: StringConstants.tc.tr(),
                                            style: FontPalette.bold12.copyWith(
                                              color: myColorScheme(context)
                                                  .primaryColor,
                                            ),
                                            recognizer: TapGestureRecognizer()
                                              ..onTap = () {
                                                Navigator.pushNamed(
                                                  context,
                                                  routeTermsAndConditionScreen,
                                                );
                                              },
                                          ),
                                          TextSpan(
                                            text: StringConstants.and.tr(),
                                            style:
                                                FontPalette.normal12.copyWith(
                                              color: myColorScheme(context)
                                                  .primaryColor,
                                            ),
                                          ),
                                          TextSpan(
                                            text: StringConstants.privacyPolicy
                                                .tr(),
                                            style: FontPalette.bold12.copyWith(
                                              color: myColorScheme(context)
                                                  .primaryColor,
                                            ),
                                            recognizer: TapGestureRecognizer()
                                              ..onTap = () {
                                                Navigator.pushNamed(
                                                  context,
                                                  routePrivacyPolicyScreen,
                                                );
                                              },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            if (showError)
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  17.horizontalSpace,
                                  CircleAvatar(
                                    backgroundColor: ColorPalette.deniedColor,
                                    radius: 5.r,
                                    child: Text(
                                      "!",
                                      style: TextStyle(
                                        color: ColorPalette.white,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 7.sp,
                                      ),
                                    ),
                                  ),
                                  7.horizontalSpace,
                                  Expanded(
                                    child: Text(
                                      StringConstants.checkBoxSignupError.tr(),
                                      style: FontPalette.normal10.copyWith(
                                        color: ColorPalette.deniedColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            28.verticalSpace,
                            BlocListener<SignUpCubit, SignUpState>(
                              listenWhen: (previous, current) =>
                                  previous.registrationFetchStatus !=
                                  current.registrationFetchStatus,
                              listener: (context, state) {
                                if (state.registrationFetchStatus ==
                                    DataStatus.success) {
                                  CommonFunctions.showDialogPopUp(
                                    context,
                                    CustomAlertDialog(
                                      message:
                                          StringConstants.registerSuccess.tr(),
                                      actionButtonText: StringConstants.ok.tr(),
                                      buttonBackGroundColor:
                                          ColorPalette.primaryVar1,
                                      onActionButtonPressed: () async {
                                        Navigator.pop(context);
                                        Navigator.pushNamed(
                                          context,
                                          routeLoginScreen,
                                        );
                                      },
                                      headerImage: Assets.alertSuccess,
                                      isLoading: false,
                                      messageTextStyle:
                                          FontPalette.semiBold19.copyWith(),
                                    ),
                                  );
                                }
                                if (state.registrationFetchStatus ==
                                    DataStatus.failed) {
                                  NetworkHelper.handleMessage(
                                    state.error,
                                    context,
                                    type: HandleTypes.customDialog,
                                    snackBarType: SnackBarType.error,
                                  );
                                }
                              },
                              child: BlocBuilder<SignUpCubit, SignUpState>(
                                key: const Key('registerNonceFetchStatus'),
                                builder: (context, state) {
                                  return CustomButton(
                                    width: 299.w,
                                    height: 52.h,
                                    label: StringConstants.signUp.tr(),
                                    isLoading: state.registerNonceFetchStatus ==
                                        DataStatus.loading,
                                    isOutlined: false,
                                    onPressed: () {
                                      String nonce = context
                                              .read<VerifyCubit>()
                                              .state
                                              .registerNonceData
                                              ?.data ??
                                          '';
                                      String selectedCountryCode = context
                                              .read<SignUpCubit>()
                                              .state
                                              .selectedCountryCode
                                              ?.phoneCode ??
                                          '+52';
                                      FocusScope.of(context).unfocus();
                                      if (_formGlobalKey.currentState!
                                          .validate()) {
                                        if (cbAgreed) {
                                          setState(() {
                                            showError = false;
                                          });
                                          context.read<SignUpCubit>().register(
                                                otp: otpController.text,
                                                nonce: nonce,
                                                inviteCode:
                                                    invitationCodeController
                                                        .text,
                                                username: _isEmailTab
                                                    ? emailController.text
                                                        .trim()
                                                    : selectedCountryCode +
                                                        phoneController.text
                                                            .trim(),
                                                password:
                                                    passwordController.text,
                                              );
                                        } else {
                                          setState(() {
                                            showError = true;
                                          });
                                        }
                                      } else {
                                        if (cbAgreed) {
                                          setState(() {
                                            showError = false;
                                          });
                                        } else {
                                          setState(() {
                                            showError = true;
                                          });
                                        }
                                      }
                                    },
                                  );
                                },
                              ),
                            ),
                            12.verticalSpace,
                            Center(
                              child: RichText(
                                textAlign: TextAlign.center,
                                text: TextSpan(
                                  style: FontPalette.normal12,
                                  children: <TextSpan>[
                                    TextSpan(
                                      text: StringConstants.alreadyHaveAccount
                                          .tr(),
                                      style: FontPalette.normal12.copyWith(
                                        color: myColorScheme(context)
                                            .secondaryVar1,
                                      ),
                                    ),
                                    TextSpan(
                                      text: StringConstants.logIn.tr(),
                                      style: FontPalette.bold12.copyWith(
                                        color:
                                            myColorScheme(context).primaryColor,
                                      ),
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          Navigator.pushReplacementNamed(
                                            context,
                                            routeLoginScreen,
                                          );
                                        },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            35.verticalSpace,
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
