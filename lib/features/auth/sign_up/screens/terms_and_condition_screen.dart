import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/constants/assets.dart';
import '../../../../core/constants/enums.dart';
import '../../../../core/constants/string_constants.dart';
import '../../../../core/theme/color_pallette.dart';
import '../../../../core/theme/font_pallette.dart';
import '../../../../core/theme/my_color_scheme.dart';
import '../../../../core/widgets/common_appbar.dart';
import '../../../../core/widgets/common_empty_data.dart';
import '../../sign_in/logic/sign_in/sign_in_cubit.dart';

class TermsAndConditions extends StatefulWidget {
  const TermsAndConditions({super.key});

  @override
  State<TermsAndConditions> createState() => _TermsAndConditionsState();
}

class _TermsAndConditionsState extends State<TermsAndConditions> {
  @override
  void initState() {
    super.initState();
    context.read<SignInCubit>().getTermsAndConditions();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        buildContext: context,
        titleWidget: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                SvgPicture.asset(Assets.termsAndConditionIcon),
                6.horizontalSpace,
                Text(
                  StringConstants.termsAndConditions.tr(),
                  style: FontPalette.semiBold16.copyWith(
                    color: myColorScheme(context).titleColor ?? Colors.black,
                  ),
                ),
              ],
            ),
            Text(
              StringConstants.dateTerms.tr(),
              style: FontPalette.normal12
                  .copyWith(color: ColorPalette.secondaryVar1),
            ),
          ],
        ),
      ),
      body: SafeArea(
        child: BlocBuilder<SignInCubit, SignInState>(
          builder: (context, state) {
            if (state.termsFetchStatus == DataStatus.success) {
              return SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(20).r,
                  child: Html(data: state.termsAndConditions ?? ''),
                ),
              );
            } else if (state.termsFetchStatus == DataStatus.failed) {
              return const Center(child: CommonEmpty());
            } else {
              return Padding(
                padding: const EdgeInsets.fromLTRB(0, 40, 0, 0).r,
                child:
                    const Center(child: CircularProgressIndicator.adaptive()),
              );
            }
          },
        ),
      ),
    );
  }
}
