import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/auth/sign_up/domain/models/signup_captcha_model.dart';
import 'package:sf_app_v2/features/auth/sign_up/domain/models/signup_model.dart';

abstract class SignUpRepository {
  const SignUpRepository();

  Future<ResponseResult<SignUpEmailCaptchaModel>> sendCode({
    required String account,
    required String captcha,
    required String k,
  });

  Future<ResponseResult<SignUpCaptchaModel>> registerCaptcha();

  Future<ResponseResult<bool>> register({
    required String username,
    required String password,
    required String invitationCode,
    required String otp,
    required String nonce,
  });

  Future<ResponseResult<bool>> checkCode({
    required String code,
  });
}
