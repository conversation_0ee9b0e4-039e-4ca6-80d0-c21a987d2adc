// To parse this JSON data, do
//
//     final signUpEmailCaptchaModel = signUpEmailCaptchaModelFromJson(jsonString);

import 'dart:convert';

SignUpEmailCaptchaModel signUpEmailCaptchaModelFromJson(String str) =>
    SignUpEmailCaptchaModel.fromJson(json.decode(str));

String signUpEmailCaptchaModelToJson(SignUpEmailCaptchaModel data) =>
    json.encode(data.toJson());

class SignUpEmailCaptchaModel {
  int code;
  String data;
  String msg;

  SignUpEmailCaptchaModel({
    required this.code,
    required this.data,
    required this.msg,
  });

  factory SignUpEmailCaptchaModel.fromJson(Map<String, dynamic> json) =>
      SignUpEmailCaptchaModel(
        code: json["code"],
        data: json["data"],
        msg: json["msg"],
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "data": data,
        "msg": msg,
      };
}

class SignUpModel {
  String email;
  String userName;
  String password;
  String? invitationCode;

  SignUpModel({
    this.email = '',
    this.userName = '',
    this.password = '',
    this.invitationCode,
  });
  factory SignUpModel.fromJson(Map<String, dynamic> json) => SignUpModel(
        email: json["email"],
        userName: json["userName"],
        password: json["password"],
      );

  Map<String, dynamic> toJson() => {
        "email": email,
        "userName": userName,
        "password": password,
        "invitationCode": invitationCode,
      };
}
