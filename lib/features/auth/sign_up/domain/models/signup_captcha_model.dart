// To parse this JSON data, do
//
//     final signUpCaptchaModel = signUpCaptchaModelFromJson(jsonString);

import 'dart:convert';

SignUpCaptchaModel signUpCaptchaModelFromJson(String str) =>
    SignUpCaptchaModel.fromJson(json.decode(str));

String signUpCaptchaModelToJson(SignUpCaptchaModel data) =>
    json.encode(data.toJson());

class SignUpCaptchaModel {
  int code;
  Data data;
  String msg;

  SignUpCaptchaModel({
    required this.code,
    required this.data,
    required this.msg,
  });

  factory SignUpCaptchaModel.fromJson(Map<String, dynamic> json) =>
      SignUpCaptchaModel(
        code: json["code"],
        data: Data.fromJson(json["data"]),
        msg: json["msg"],
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "data": data.toJson(),
        "msg": msg,
      };
}

class Data {
  String captcha;
  String k;

  Data({
    required this.captcha,
    required this.k,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        captcha: json["captcha"],
        k: json["k"],
      );

  Map<String, dynamic> toJson() => {
        "captcha": captcha,
        "k": k,
      };
}
