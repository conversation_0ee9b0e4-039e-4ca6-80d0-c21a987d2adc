import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/api/endpoint/api_endpoints.dart';
import 'package:sf_app_v2/core/api/network/network.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/auth/sign_up/domain/models/signup_captcha_model.dart';
import 'package:sf_app_v2/features/auth/sign_up/domain/models/signup_model.dart';

import '../repository/sign_up_repository.dart';

/// Service class that implements the SignUpRepository interface to handle user registration functionality
@Injectable(as: SignUpRepository)
class SignUpService implements SignUpRepository {
  /// Registers a new user account
  /// [username] - User's email address
  /// [password] - User's chosen password
  /// [invitationCode] - Invitation code for registration
  /// [otp] - One-time password from email verification
  /// [nonce] - One-time use security token
  /// Returns a [ResponseResult] containing bool on success
  @override
  Future<ResponseResult<bool>> register({
    required String username,
    required String password,
    required String invitationCode,
    required String otp,
    required String nonce,
  }) async {
    try {
      final response = await NetworkProvider().post(
        ApiEndpoints.register,
        data: {
          "username": username,
          "password": password,
          "invitationCode": invitationCode,
          "otp": otp,
          "nonce": nonce,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to register');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches a new captcha for registration
  /// Returns a [ResponseResult] containing [SignUpCaptchaModel] on success
  @override
  Future<ResponseResult<SignUpCaptchaModel>> registerCaptcha() async {
    try {
      final response = await NetworkProvider().get(ApiEndpoints.captcha);
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
            data: SignUpCaptchaModel.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get register captcha');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Sends verification code to user's email
  /// [account] - User's email address
  /// [captcha] - Captcha value entered by user
  /// [k] - Captcha key
  /// Returns a [ResponseResult] containing [SignUpEmailCaptchaModel] on success
  @override
  Future<ResponseResult<SignUpEmailCaptchaModel>> sendCode({
    required String account,
    required String captcha,
    required String k,
  }) async {
    try {
      final response = await NetworkProvider().post(
        ApiEndpoints.emailCaptcha,
        data: {
          "account": account,
          "captcha": captcha,
          "k": k,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
            data: SignUpEmailCaptchaModel.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get register nonce');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Validates an invitation code
  /// [code] - Invitation code to check
  /// Returns a [ResponseResult] containing bool indicating if code is valid
  @override
  Future<ResponseResult<bool>> checkCode({
    required String code,
  }) async {
    try {
      final response = await NetworkProvider().get(
        '${ApiEndpoints.checkCode}?invitationCode=$code',
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: response.data['data']);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to check code');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
