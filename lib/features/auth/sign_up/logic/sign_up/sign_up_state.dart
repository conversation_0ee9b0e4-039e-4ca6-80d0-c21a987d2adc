part of 'sign_up_cubit.dart';

class SignUpState extends Equatable {
  final DataStatus registerNonceFetchStatus;
  final SignUpEmailCaptchaModel? registerNonceData;
  final DataStatus registrationFetchStatus;
  final DataStatus checkCodeFetchStatus;
  final SignUpModel? signUpData;
  final String? inviteCode;
  final String? error;
  final bool? isCheckCodeSuccess;
  final PhoneCountry? selectedCountryCode;
  const SignUpState({
    this.registerNonceFetchStatus = DataStatus.idle,
    this.registrationFetchStatus = DataStatus.idle,
    this.checkCodeFetchStatus = DataStatus.idle,
    this.registerNonceData,
    this.signUpData,
    this.error,
    this.inviteCode,
    this.isCheckCodeSuccess,
    this.selectedCountryCode,
  });

  @override
  List<Object?> get props => [
        registerNonceFetchStatus,
        registerNonceData,
        registrationFetchStatus,
        checkCodeFetchStatus,
        signUpData,
        error,
        inviteCode,
        isCheckCodeSuccess,
        selectedCountryCode,
      ];

  SignUpState copyWith({
    DataStatus? registerNonceFetchStatus,
    SignUpEmailCaptchaModel? registerNonceData,
    DataStatus? registrationFetchStatus,
    SignUpModel? signUpData,
    DataStatus? checkCodeFetchStatus,
    String? error,
    String? inviteCode,
    bool? isCheckCodeSuccess,
    PhoneCountry? selectedCountryCode,
  }) {
    return SignUpState(
      registerNonceFetchStatus: registerNonceFetchStatus ?? this.registerNonceFetchStatus,
      registerNonceData: registerNonceData ?? this.registerNonceData,
      registrationFetchStatus: registrationFetchStatus ?? this.registrationFetchStatus,
      signUpData: signUpData ?? this.signUpData,
      checkCodeFetchStatus: checkCodeFetchStatus ?? this.checkCodeFetchStatus,
      error: error ?? this.error,
      inviteCode: inviteCode ?? this.inviteCode,
      isCheckCodeSuccess: isCheckCodeSuccess ?? this.isCheckCodeSuccess,
      selectedCountryCode: selectedCountryCode ?? this.selectedCountryCode,
    );
  }
}
