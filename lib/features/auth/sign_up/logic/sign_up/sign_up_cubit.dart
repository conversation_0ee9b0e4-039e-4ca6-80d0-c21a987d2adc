import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/models/phone_country/phone_country.dart';
import 'package:sf_app_v2/features/auth/sign_up/domain/models/signup_model.dart';
import 'package:sf_app_v2/features/auth/sign_up/domain/repository/sign_up_repository.dart';

part 'sign_up_state.dart';

@injectable
class SignUpCubit extends Cubit<SignUpState> {
  final SignUpRepository _signUpService;

  SignUpCubit(this._signUpService) : super(const SignUpState());

  Future<void> register({
    required String otp,
    required String nonce,
    required String inviteCode,
    required String username,
    required String password,
  }) async {
    emit(state.copyWith(registrationFetchStatus: DataStatus.loading));
    try {
      final result = await _signUpService.register(
        username: username,
        password: password,
        invitationCode: inviteCode,
        otp: otp,
        nonce: nonce,
      );
      if (result.data != null) {
        emit(state.copyWith(registrationFetchStatus: DataStatus.success));
      } else {
        emit(
          state.copyWith(
            registrationFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          registrationFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  void setInviteCode(String inviteCode) {
    emit(state.copyWith(inviteCode: inviteCode));
  }

  Future<void> checkCode({
    required String code,
  }) async {
    emit(state.copyWith(checkCodeFetchStatus: DataStatus.loading));
    try {
      final result = await _signUpService.checkCode(code: code);
      if (result.data != null) {
        emit(state.copyWith(checkCodeFetchStatus: DataStatus.success, isCheckCodeSuccess: result.data));
      } else {
        emit(
          state.copyWith(
            checkCodeFetchStatus: DataStatus.failed,
            error: result.error,
            isCheckCodeSuccess: false,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          checkCodeFetchStatus: DataStatus.failed,
          error: e.toString(),
          isCheckCodeSuccess: false,
        ),
      );
    }
  }

  void setSelectedCountryCode(PhoneCountry selectedCountryCode) =>
      emit(state.copyWith(selectedCountryCode: selectedCountryCode));
}
