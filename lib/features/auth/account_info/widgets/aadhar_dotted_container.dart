import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

import '../../../../core/widgets/common_icon_button.dart';
import '../../../../core/widgets/dotted_border.dart';

class AadhaarDottedContainer extends StatelessWidget {
  final void Function()? onPressed;
  final double width;
  final double height;
  final String label;
  final bool? isLoading;
  final double? borderRadiusUser;
  final TextStyle? fontStyle;
  final ButtonStyle? buttonStyle;
  final TextStyle? textStyle;
  final TextStyle? btnTextStyle;
  final bool? isOutlined;
  final File? imageFile;
  final String? imageType;

  const AadhaarDottedContainer({
    super.key,
    this.onPressed,
    required this.width,
    required this.height,
    required this.label,
    this.isLoading,
    this.borderRadiusUser,
    this.fontStyle,
    this.buttonStyle,
    this.btnTextStyle,
    this.isOutlined,
    this.imageFile,
    this.imageType,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height.w,
      width: width.w,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          CustomDottedBorder(
            radius: 12.r,
            color: myColorScheme(context).primaryColor ??
                ColorPalette.primaryColor,
            strokeWidth: 3,
            dashPattern: const [5, 3],
            child: imageFile == null
                ? SizedBox(
                    height: (height - 20.w).w,
                    width: width.w,
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(18, 50, 18, 0).r,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: 80.w,
                            height: 60.w,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: Colors.grey.withValues(alpha: 0.2),
                            ),
                          ),
                          SizedBox(
                            width: 20.r,
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                width: 120.w,
                                height: 10.w,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(6).r,
                                  color: Colors.grey.withValues(alpha: 0.2),
                                ),
                              ),
                              SizedBox(
                                height: 6.r,
                              ),
                              Container(
                                width: 110.w,
                                height: 10.w,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(6).r,
                                  color: Colors.grey.withValues(alpha: 0.2),
                                ),
                              ),
                              SizedBox(
                                height: 6.r,
                              ),
                              Container(
                                width: 100.w,
                                height: 10.w,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(6).r,
                                  color: Colors.grey.withValues(alpha: 0.2),
                                ),
                              ),
                              SizedBox(
                                height: 6.r,
                              ),
                              Container(
                                width: 90.w,
                                height: 10.w,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(6).r,
                                  color: Colors.grey.withValues(alpha: 0.2),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  )
                : Container(
                    height: (height - 20.w).w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8).r,
                      image: DecorationImage(
                        fit: BoxFit.cover,
                        image: FileImage(
                          File(imageFile!.path.toString()),
                        ),
                      ),
                    ),
                  ),
          ),
          Positioned(
            bottom: -5,
            left: 1,
            right: 1,
            child: CustomCircularIconButton(
              width: 40,
              icon: Icons.add,
              onPressed: onPressed,
            ),
          ),
        ],
      ),
    );
  }
}


