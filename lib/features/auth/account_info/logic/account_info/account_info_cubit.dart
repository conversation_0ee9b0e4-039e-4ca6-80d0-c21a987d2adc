import 'dart:developer';
import 'dart:io';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:image_picker/image_picker.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/models/status/status_model.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/repository/account_info_repository.dart';

import '../../../../../core/constants/keys.dart';
import '../../../../../core/services/image_picker/image_picker_repository.dart';
import '../../../../../core/utils/shared_preference_helper.dart';

part 'account_info_state.dart';

@injectable
class AccountInfoCubit extends Cubit<AccountInfoState> {
  final AccountInfoRepository _accountInfoService;
  final ImagePickerRepository _imagePickerService;

  AccountInfoCubit(this._accountInfoService, this._imagePickerService)
      : super(const AccountInfoState());

  Future<bool> uploadAadhaarCard({
    required File? file,
    required CardType fileType,
  }) async {
    emit(state.copyWith(uploadStatus: DataStatus.loading));
    final response =
        await _accountInfoService.fileUpload(file: file, type: fileType.name);
    if (response.data != null) {
      try {
        final fileUploadData = response.data;
        switch (fileType) {
          case CardType.front:
            {
              emit(
                state.copyWith(
                  rePathFront:
                      fileUploadData?.data?.rePath?.replaceAll('.png', '') ??
                          '',
                ),
              );
              break;
            }
          case CardType.back:
            {
              emit(
                state.copyWith(
                  rePathBack:
                      fileUploadData?.data?.rePath?.replaceAll('.png', '') ??
                          '',
                ),
              );
              break;
            }
        }
        emit(state.copyWith(uploadStatus: DataStatus.success));
        return true;
      } on Exception catch (e) {
        log('Error parsing response: $e');
        emit(state.copyWith(uploadStatus: DataStatus.failed));
        return false;
      }
    } else {
      emit(
        state.copyWith(
          uploadStatus: DataStatus.failed,
          error: response.error,
        ),
      );
      return false;
    }
  }

  Future<void> submitAadhaarCardAuthIdentity({
    required String aadhaarCardNumber,
  }) async {
    bool frontUpload = false;
    bool backUpload = false;
    emit(state.copyWith(submitAadhaarCardStatus: DataStatus.loading));
    try {
      frontUpload = await uploadAadhaarCard(
        file: state.imageFileFront,
        fileType: CardType.front,
      );
      backUpload = await uploadAadhaarCard(
        file: state.imageFileBack,
        fileType: CardType.back,
      );
    } on Exception catch (e) {
      emit(
        state.copyWith(
          submitAadhaarCardStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
    if (!frontUpload || !backUpload) {
      return;
    }
    try {
      final result = await _accountInfoService.aadhaarCardAuthIdentity(
        front: state.rePathFront,
        back: state.rePathBack,
        aadhaarCardNumber: aadhaarCardNumber,
        idType: state.selectedIdType,
      );
      if (result.data != null) {
        emit(
          state.copyWith(
            submitAadhaarCardStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            submitAadhaarCardStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          submitAadhaarCardStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<StatusData?> getStatus() async {
    emit(state.copyWith(isFetchingStatus: DataStatus.loading));
    try {
      final result = await _accountInfoService.status();
      if (result.data != null) {
        bool isAccountVerified =
            checkAccountStatus(result.data ?? const StatusData());
        if (isAccountVerified) {
          await SharedPreferenceHelper().writeBoolData(
            SharedPreferencesKeys.isAccountStatusLogged,
            true,
          );
        }
        if (result.data?.wallet == 1) {
          await SharedPreferenceHelper().writeBoolData(
            SharedPreferencesKeys.isWalletPasswordSet,
            true,
          );
        }
        emit(
          state.copyWith(
            statusData: result.data,
            isAccountVerified: isAccountVerified,
            isFetchingStatus: DataStatus.success,
          ),
        );
        return result.data;
      } else {
        emit(
          state.copyWith(
            isFetchingStatus: DataStatus.failed,
            error: result.error,
          ),
        );
        return null;
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          isFetchingStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
      return null;
    } finally {
      setIdCardFetchingStatus(DataStatus.idle);
    }
  }

  void setImageFileFront(File? imageFileFront) {
    emit(state.copyWith(imageFileFront: imageFileFront));
  }

  void setImageFileBack(File? imageFileBack) {
    emit(state.copyWith(imageFileBack: imageFileBack));
  }

  void setShowError(bool showError) {
    emit(state.copyWith(showError: showError));
  }

  Future<void> getAadhaarImage({required CardType type}) async {
    switch (type) {
      case CardType.front:
        {
          final imageFileFront =
              await _imagePickerService.getImage(ImageSource.gallery);
          setImageFileFront(imageFileFront);
          break;
        }
      case CardType.back:
        {
          final imageFileBack =
              await _imagePickerService.getImage(ImageSource.gallery);
          setImageFileBack(imageFileBack);
          break;
        }
    }
  }

  bool checkAccountStatus(StatusData data) {
    if (data.identity == 1 && data.wallet == 1) {
      return true;
    }
    return false;
  }

  void resetFile() {
    emit(state.copyWith(resetFile: true));
  }

  void setSelectedIdType(String selectedIdType) =>
      emit(state.copyWith(selectedIdType: selectedIdType));
  void setIdCardFetchingStatus(DataStatus status) =>
      emit(state.copyWith(idCardFetchingStatus: status));
}
