part of 'account_info_cubit.dart';

class AccountInfoState extends Equatable {
  final File? imageFileFront;
  final File? imageFileBack;
  final String? rePathFront;
  final String? rePathBack;
  final StatusData? statusData;
  final bool accountStatus;
  final DataStatus isFetchingStatus;
  final DataStatus idCardFetchingStatus;
  final bool showError;
  final DataStatus uploadStatus;
  final DataStatus submitAadhaarCardStatus;
  final bool isAccountVerified;
  final String? error;
  final List<Map<String, dynamic>> idTypes;
  final String selectedIdType;

  const AccountInfoState(
      {this.imageFileFront,
      this.imageFileBack,
      this.rePathFront,
      this.rePathBack,
      this.statusData,
      this.accountStatus = false,
      this.isFetchingStatus = DataStatus.idle,
      this.idCardFetchingStatus = DataStatus.idle,
      this.showError = false,
      this.uploadStatus = DataStatus.idle,
      this.submitAadhaarCardStatus = DataStatus.idle,
      this.error,
      this.isAccountVerified = false,
      this.selectedIdType = 'idCard',
      this.idTypes = const [
        {'label': 'ID Card', 'value': 'idCard', 'regex': r'^\d{9}$'},
        {'label': 'Passport', 'value': 'passport', 'regex': r'^[A-Z]\d{8}$'},
        {
          'label': 'Driver License',
          'value': 'driverLicense',
          'regex': r'^[A-Z]\d{7}$'
        }
      ]});

  @override
  List<Object?> get props => [
        imageFileFront,
        imageFileBack,
        rePathFront,
        rePathBack,
        statusData,
        accountStatus,
        isFetchingStatus,
        idCardFetchingStatus,
        showError,
        uploadStatus,
        submitAadhaarCardStatus,
        error,
        isAccountVerified,
        idTypes,
        selectedIdType,
      ];

  AccountInfoState copyWith({
    File? imageFileFront,
    File? imageFileBack,
    String? rePathFront,
    String? rePathBack,
    StatusData? statusData,
    bool? accountStatus,
    DataStatus? isFetchingStatus,
    DataStatus? idCardFetchingStatus,
    bool? showError,
    DataStatus? uploadStatus,
    DataStatus? submitAadhaarCardStatus,
    String? error,
    bool? isAccountVerified,
    bool resetFile = false,
    List<Map<String, dynamic>>? idTypes,
    String? selectedIdType,
  }) {
    return AccountInfoState(
      imageFileFront: resetFile ? null : imageFileFront ?? this.imageFileFront,
      imageFileBack: resetFile ? null : imageFileBack ?? this.imageFileBack,
      rePathFront: resetFile ? null : rePathFront ?? this.rePathFront,
      rePathBack: resetFile ? null : rePathBack ?? this.rePathBack,
      statusData: statusData ?? this.statusData,
      accountStatus: accountStatus ?? this.accountStatus,
      isFetchingStatus: isFetchingStatus ?? this.isFetchingStatus,
      idCardFetchingStatus: idCardFetchingStatus ?? this.idCardFetchingStatus,
      showError: showError ?? this.showError,
      uploadStatus: uploadStatus ?? this.uploadStatus,
      submitAadhaarCardStatus:
          submitAadhaarCardStatus ?? this.submitAadhaarCardStatus,
      error: error ?? this.error,
      isAccountVerified: isAccountVerified ?? this.isAccountVerified,
      idTypes: idTypes ?? this.idTypes,
      selectedIdType: selectedIdType ?? this.selectedIdType,
    );
  }
}
