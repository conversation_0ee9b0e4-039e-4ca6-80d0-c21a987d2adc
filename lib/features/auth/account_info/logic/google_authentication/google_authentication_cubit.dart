import 'dart:convert';
import 'dart:typed_data';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/models/google_auth/google_auth_reset.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/models/google_token/google_token.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/models/status/status_model.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/repository/account_info_repository.dart';

part 'google_authentication_state.dart';

@injectable
class GoogleAuthenticationCubit extends Cubit<GoogleAuthenticationState> {
  GoogleAuthenticationCubit(this._googleAuthService)
      : super(const GoogleAuthenticationState());
  final AccountInfoRepository _googleAuthService;

  Future<void> googleAuth() async {
    emit(state.copyWith(bytes: null, googleAuthStatus: DataStatus.loading));
    try {
      final response = await _googleAuthService.googleAuth();
      if (response.data != null) {
        emit(
          state.copyWith(
            googleTokenData: response.data?.data,
            bytes:
                base64Decode(response.data?.data?.qrcode?.split(',')[1] ?? ''),
            googleAuthStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            googleAuthStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          googleAuthStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> googleAuthCheck(String code) async {
    final currentState = state;
    emit(state.copyWith(googleAuthCheckStatus: DataStatus.loading));

    try {
      final response = await _googleAuthService.googleAuthCheck(
        code: code,
        nonce: currentState.googleTokenData?.nonce,
      );
      if (response.data != null) {
        emit(currentState.copyWith(googleAuthCheckStatus: DataStatus.success));
      } else {
        emit(
          currentState.copyWith(
            googleAuthCheckStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        currentState.copyWith(
          googleAuthCheckStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> googleAuthReset({
    required emailCaptcha,
    required String nonce,
  }) async {
    emit(state.copyWith(googleAuthResetStatus: DataStatus.loading));
    try {
      final response = await _googleAuthService.googleAuthReset(
        emailCaptcha: emailCaptcha,
        nonce: nonce,
      );
      if (response.data != null) {
        emit(
          state.copyWith(
            googleAuthResetData: response.data?.data,
            bytesReset:
                base64Decode(response.data?.data?.qrcode?.split(',')[1] ?? ''),
            googleAuthResetStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            googleAuthResetStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          googleAuthResetStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> googleAuthResetCheck({required String code}) async {
    final currentState = state;
    emit(state.copyWith(googleAuthResetCheckStatus: DataStatus.loading));
    try {
      final response = await _googleAuthService.googleAuthResetCheck(
        code: code,
        nonce: currentState.googleTokenData?.nonce,
      );
      if (response.data != null) {
        emit(
          currentState.copyWith(
            googleAuthResetCheckStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          currentState.copyWith(
            googleAuthResetCheckStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        currentState.copyWith(
          googleAuthResetCheckStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  void setBytesReset(Uint8List? bytesReset) {
    emit(state.copyWith(bytesReset: bytesReset));
  }
}
