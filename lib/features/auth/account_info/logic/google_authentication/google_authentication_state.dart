part of 'google_authentication_cubit.dart';

class GoogleAuthenticationState extends Equatable {
  final String? rePathFront;
  final StatusData? statusData;
  final GoogleTokenData? googleTokenData;
  final GoogleAuthResetData? googleAuthResetData;
  final Uint8List? bytes;
  final Uint8List? bytesReset;
  final DataStatus googleAuthStatus;
  final DataStatus googleAuthCheckStatus;
  final DataStatus googleAuthResetStatus;
  final DataStatus googleAuthResetCheckStatus;
  final String? error;

  const GoogleAuthenticationState({
    this.rePathFront,
    this.statusData,
    this.googleTokenData,
    this.googleAuthResetData,
    this.bytes,
    this.bytesReset,
    this.googleAuthStatus = DataStatus.idle,
    this.googleAuthCheckStatus = DataStatus.idle,
    this.googleAuthResetStatus = DataStatus.idle,
    this.googleAuthResetCheckStatus = DataStatus.idle,
    this.error,
  });

  @override
  List<Object?> get props => [
        rePathFront,
        statusData,
        googleTokenData,
        googleAuthResetData,
        bytes,
        bytesReset,
        googleAuthStatus,
        googleAuthCheckStatus,
        googleAuthResetStatus,
        googleAuthResetCheckStatus,
        error,
      ];

  GoogleAuthenticationState copyWith({
    String? rePathFront,
    StatusData? statusData,
    GoogleTokenData? googleTokenData,
    GoogleAuthResetData? googleAuthResetData,
    Uint8List? bytes,
    Uint8List? bytesReset,
    DataStatus? googleAuthStatus,
    DataStatus? googleAuthCheckStatus,
    DataStatus? googleAuthResetStatus,
    DataStatus? googleAuthResetCheckStatus,
    String? error,
  }) {
    return GoogleAuthenticationState(
      rePathFront: rePathFront ?? this.rePathFront,
      statusData: statusData ?? this.statusData,
      googleTokenData: googleTokenData ?? this.googleTokenData,
      googleAuthResetData: googleAuthResetData ?? this.googleAuthResetData,
      bytes: bytes ?? this.bytes,
      bytesReset: bytesReset ?? this.bytesReset,
      googleAuthStatus: googleAuthStatus ?? this.googleAuthStatus,
      googleAuthCheckStatus:
          googleAuthCheckStatus ?? this.googleAuthCheckStatus,
      googleAuthResetStatus:
          googleAuthResetStatus ?? this.googleAuthResetStatus,
      googleAuthResetCheckStatus:
          googleAuthResetCheckStatus ?? this.googleAuthResetCheckStatus,
      error: error ?? this.error,
    );
  }
}
