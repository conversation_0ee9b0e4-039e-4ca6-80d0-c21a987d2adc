part of 'phone_cubit.dart';

class PhoneState extends Equatable {
  final String mobile;
  final DataStatus bindStatus;
  final String? error;

  const PhoneState({
    this.mobile = '',
    this.bindStatus = DataStatus.idle,
    this.error,
  });

  @override
  List<Object?> get props => [
        mobile,
        bindStatus,
        error,
      ];

  PhoneState copyWith({
    String? mobile,
    DataStatus? bindStatus,
    String? error,
  }) {
    return PhoneState(
      mobile: mobile ?? this.mobile,
      bindStatus: bindStatus ?? this.bindStatus,
      error: error ?? this.error,
    );
  }
}

final class PhoneInitial extends PhoneState {}
