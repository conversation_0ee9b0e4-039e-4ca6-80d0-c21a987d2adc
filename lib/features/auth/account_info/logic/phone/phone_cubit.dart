import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/repository/account_info_repository.dart';
part 'phone_state.dart';

@injectable
class PhoneCubit extends Cubit<PhoneState> {
  PhoneCubit(this._accountInfoService) : super(const PhoneState());
  final AccountInfoRepository _accountInfoService;

  Future<void> bindMobileNumber(String mobile) async {
    emit(state.copyWith(bindStatus: DataStatus.loading));
    try {
      final response =
          await _accountInfoService.bindMobilePhoneNumber(mobile: mobile);
      if (response.data != null) {
        emit(
          state.copyWith(
            bindStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            bindStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          bindStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  void setMobile(String mobile) {
    emit(state.copyWith(mobile: mobile));
  }
}
