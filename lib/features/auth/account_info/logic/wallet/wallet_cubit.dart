import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/repository/account_info_repository.dart';

part 'wallet_state.dart';

@injectable
class WalletCubit extends Cubit<WalletState> {
  WalletCubit(this._accountInfoService) : super(const WalletState());
  final AccountInfoRepository _accountInfoService;

  Future<void> setupPaymentPassword({
    required String password,
    required String confirmPassword,
  }) async {
    emit(state.copyWith(setupWalletStatus: DataStatus.loading));
    try {
      final response = await _accountInfoService.setupPaymentPassword(
        password: password,
        confirmPassword: confirmPassword,
      );
      if (response.data != null) {
        emit(
          state.copyWith(
            setupWalletStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            setupWalletStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          setupWalletStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  void setPassword(String password) {
    emit(state.copyWith(password: password));
  }

  void setConfirmPassword(String confirmPassword) {
    emit(state.copyWith(confirmPassword: confirmPassword));
  }

  void setRecordSliderIndex(int index) {
    emit(state.copyWith(recordSliderIndex: index));
  }
}
