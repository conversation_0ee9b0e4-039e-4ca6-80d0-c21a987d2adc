part of 'wallet_cubit.dart';

class WalletState extends Equatable {
  final String password;
  final String confirmPassword;
  final DataStatus setupWalletStatus;
  final String? error;
  final int recordSliderIndex;

  const WalletState({
    this.password = '',
    this.confirmPassword = '',
    this.setupWalletStatus = DataStatus.idle,
    this.error,
    this.recordSliderIndex = 0,
  });

  @override
  List<Object?> get props => [
        password,
        confirmPassword,
        setupWalletStatus,
        error,
        recordSliderIndex,
      ];

  WalletState copyWith({
    String? password,
    String? confirmPassword,
    DataStatus? setupWalletStatus,
    String? error,
    int? recordSliderIndex,
  }) {
    return WalletState(
      password: password ?? this.password,
      confirmPassword: confirmPassword ?? this.confirmPassword,
      setupWalletStatus: setupWalletStatus ?? this.setupWalletStatus,
      error: error ?? this.error,
      recordSliderIndex: recordSliderIndex ?? this.recordSliderIndex,
    );
  }
}
