import 'dart:io';

import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/models/file/file_upload.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/models/google_auth/google_auth_reset.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/models/google_token/google_token.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/models/status/status_model.dart';

abstract class AccountInfoRepository {
  const AccountInfoRepository();

  Future<ResponseResult<GoogleToken>> googleAuth();

  Future<ResponseResult<bool>> googleAuthCheck({
    required String? code,
    required String? nonce,
  });

  Future<ResponseResult<GoogleAuthReset>> googleAuthReset({
    required String? emailCaptcha,
    required String? nonce,
  });

  Future<ResponseResult<bool>> googleAuthResetCheck({
    required String? code,
    required String? nonce,
  });
  Future<ResponseResult<bool>> setupPaymentPassword({
    required String password,
    required String confirmPassword,
  });
  Future<ResponseResult<bool>> bindMobilePhoneNumber({
    required String mobile,
  });
  Future<ResponseResult<bool>> aadhaarCardAuthIdentity({
    required String? front,
    required String? back,
    required String? aadhaarCardNumber,
    String? idType,
  });

  Future<ResponseResult<StatusData>> status();

  Future<ResponseResult<FileUpload>> fileUpload({
    required File? file,
    required String? type,
  });
}
