import 'package:freezed_annotation/freezed_annotation.dart';

part 'payment_password.freezed.dart';
part 'payment_password.g.dart';

@freezed
class PaymentPassword with _$PaymentPassword {
  const factory PaymentPassword({
    required int code,
    required String data,
    required String msg,
  }) = _PaymentPassword;

  factory PaymentPassword.fromJson(Map<String, dynamic> json) =>
      _$PaymentPasswordFromJson(json);
}
