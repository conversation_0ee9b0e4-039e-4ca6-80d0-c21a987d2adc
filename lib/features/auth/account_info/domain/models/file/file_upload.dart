import 'package:freezed_annotation/freezed_annotation.dart';
part 'file_upload.freezed.dart';
part 'file_upload.g.dart';

@freezed
class FileUpload with _$FileUpload {
  const factory FileUpload({
    int? code,
    FileUploadData? data,
    String? msg,
  }) = _FileUpload;

  factory FileUpload.fromJson(Map<String, dynamic> json) =>
      _$FileUploadFromJson(json);
}

@freezed
class FileUploadData with _$FileUploadData {
  const factory FileUploadData({
    String? original,
    String? rePath,
    String? title,
    String? url,
  }) = _FileUploadData;

  factory FileUploadData.fromJson(Map<String, dynamic> json) =>
      _$FileUploadDataFromJson(json);
}
