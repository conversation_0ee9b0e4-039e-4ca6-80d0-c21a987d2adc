import 'package:freezed_annotation/freezed_annotation.dart';
part 'google_auth_reset.freezed.dart';
part 'google_auth_reset.g.dart';

@freezed
class GoogleAuthReset with _$GoogleAuthReset {
  const factory GoogleAuthReset({
    int? code,
    GoogleAuthResetData? data,
    String? msg,
  }) = _GoogleAuthReset;

  factory GoogleAuthReset.fromJson(Map<String, dynamic> json) =>
      _$GoogleAuthResetFromJson(json);
}

@freezed
class GoogleAuthResetData with _$GoogleAuthResetData {
  const factory GoogleAuthResetData({
    String? nonce,
    String? qrcode,
    String? token,
  }) = _GoogleAuthResetData;

  factory GoogleAuthResetData.fromJson(Map<String, dynamic> json) =>
      _$GoogleAuthResetDataFromJson(json);
}
