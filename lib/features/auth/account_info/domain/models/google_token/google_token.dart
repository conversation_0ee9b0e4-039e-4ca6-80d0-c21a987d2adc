import 'package:freezed_annotation/freezed_annotation.dart';

part 'google_token.freezed.dart';
part 'google_token.g.dart';

@freezed
class GoogleToken with _$GoogleToken {
  const factory GoogleToken({
    int? code,
    GoogleTokenData? data,
    String? msg,
  }) = _GoogleToken;

  factory GoogleToken.fromJson(Map<String, dynamic> json) =>
      _$GoogleTokenFromJson(json);
}

@freezed
class GoogleTokenData with _$GoogleTokenData {
  const factory GoogleTokenData({
    String? nonce,
    String? qrcode,
    String? token,
  }) = _GoogleTokenData;

  factory GoogleTokenData.fromJson(Map<String, dynamic> json) =>
      _$GoogleTokenDataFromJson(json);
}
