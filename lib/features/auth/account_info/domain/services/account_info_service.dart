import 'dart:io';

import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/api/endpoint/api_endpoints.dart';
import 'package:sf_app_v2/core/api/network/network.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/models/file/file_upload.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/models/google_auth/google_auth_reset.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/models/google_token/google_token.dart';
import 'package:sf_app_v2/features/auth/account_info/domain/models/status/status_model.dart';

import '../repository/account_info_repository.dart';

/// Service class that implements the AccountInfoRepository interface to handle account related API calls
@Injectable(as: AccountInfoRepository)
class AccountInfoService implements AccountInfoRepository {
  /// Initiates Google authentication flow
  /// Returns a [ResponseResult] containing [GoogleToken] on success
  @override
  Future<ResponseResult<GoogleToken>> googleAuth() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.googleAuth,
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: GoogleToken.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to authenticate with Google');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Verifies Google authentication code and nonce
  /// [code] - The authentication code from Google
  /// [nonce] - One-time use security token
  /// Returns a [ResponseResult] containing bool on success
  @override
  Future<ResponseResult<bool>> googleAuthCheck({
    required String? code,
    required String? nonce,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.googleAuthCheck,
        data: {"code": code, "nonce": nonce},
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to check Google authentication');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Resets Google authentication using email code and nonce
  /// [emailCaptcha] - Email verification code
  /// [nonce] - One-time use security token
  /// Returns a [ResponseResult] containing [GoogleAuthReset] on success
  @override
  Future<ResponseResult<GoogleAuthReset>> googleAuthReset({
    required String? emailCaptcha,
    required String? nonce,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.googleAuthReset,
        data: {"emailCaptcha": emailCaptcha, "nonce": nonce},
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: GoogleAuthReset.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to reset Google authentication');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Verifies Google authentication reset using code and nonce
  /// [code] - The authentication code from Google
  /// [nonce] - One-time use security token
  /// Returns a [ResponseResult] containing bool on success
  @override
  Future<ResponseResult<bool>> googleAuthResetCheck({
    required String? code,
    required String? nonce,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.googleAuthCheckReset,
        data: {"code": code, "nonce": nonce},
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(
          error: 'Failed to check Google authentication reset',
        );
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Sets up payment password for the user
  /// [password] - The new payment password
  /// [confirmPassword] - Confirmation of the new payment password
  /// Returns a [ResponseResult] containing bool on success
  @override
  Future<ResponseResult<bool>> setupPaymentPassword({
    required String password,
    required String confirmPassword,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.setupPaymentPassword,
        data: {'confirmPassword': confirmPassword, 'password': password},
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to setup payment password');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Binds mobile phone number to user account
  /// [mobile] - The mobile phone number to bind
  /// Returns a [ResponseResult] containing bool on success
  @override
  Future<ResponseResult<bool>> bindMobilePhoneNumber({
    required String mobile,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.mobileNo,
        data: {'mobileNo': mobile},
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to bind mobile phone number');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Submits Aadhaar card details for identity verification
  /// [front] - URL of front side image of Aadhaar card
  /// [back] - URL of back side image of Aadhaar card
  /// [aadhaarCardNumber] - Aadhaar card number
  /// [idType] - Type of ID document, defaults to 'idCard'
  /// Returns a [ResponseResult] containing bool on success
  @override
  Future<ResponseResult<bool>> aadhaarCardAuthIdentity({
    required String? front,
    required String? back,
    required String? aadhaarCardNumber,
    String? idType,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.submitAadhar,
        data: {
          'front': front,
          'back': back,
          'idCardNo': aadhaarCardNumber,
          'idType': idType ?? 'idCard',
        },
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to submit Aadhaar card identity');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches user account status
  /// Returns a [ResponseResult] containing [StatusData] on success
  @override
  Future<ResponseResult<StatusData>> status() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.status,
        isSigninRequired: true,
      );
      if (response.statusCode == 200) {
        if (response.data['code'] == 200) {
          return ResponseResult(
            data: StatusData.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch status');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Uploads a file (e.g. profile image, documents)
  /// [file] - The file to upload
  /// [type] - Type of file being uploaded
  /// Returns a [ResponseResult] containing [FileUpload] on success
  @override
  Future<ResponseResult<FileUpload>> fileUpload({
    required File? file,
    required String? type,
  }) async {
    try {
      final multipartFile = await MultipartFile.fromFile(
        file!.path,
        filename: file.path.split('/').last,
      );
      final Response response = await NetworkProvider().formData(
        ApiEndpoints.uploadAadhaar,
        isSigninRequired: true,
        options: Options(headers: {'Content-Type': 'multipart/form-data'}),
        formData: FormData.fromMap(
          {
            'file': multipartFile,
          },
        ),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: FileUpload.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to upload profile image');
      }
    } on Error catch (e) {
      return ResponseResult(error: e.toString());
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
