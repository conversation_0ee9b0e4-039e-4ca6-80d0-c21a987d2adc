import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/account_info/account_info_cubit.dart';
import '../../../../core/common_function.dart';
import '../../../../core/constants/assets.dart';
import '../../../../core/constants/string_constants.dart';
import '../../../../core/utils/mixin/animation.dart';
import '../../../../core/widgets/common_text_field.dart';
import '../../../../core/widgets/custom_alert_dialog.dart';

class AccountInfoScreen extends StatefulWidget {
  const AccountInfoScreen({super.key});

  @override
  State<AccountInfoScreen> createState() => _AccountInfoScreenState();
}

class _AccountInfoScreenState extends State<AccountInfoScreen>
    with StaggeredAnimation {
  late FocusNode firstNameFocusNode;

  final TextEditingController aadhaarCardController = TextEditingController();
  final TextEditingController walletPasswordController =
      TextEditingController();
  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController googleAuthCodeController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    CommonFunctions.afterInit(_initialFunction);
  }

  bool checkIdentityOnlyPending(int identity, int wallet, int mobileNo) {
    if (identity == 0 && wallet == 1 && mobileNo == 1) {
      return true;
    }
    return false;
  }

  bool isClosingPopup = false;
  void delayedPop({Function? function}) {
    if (isClosingPopup) return;
    isClosingPopup = true;
    Navigator.pop(context);
    if (function != null) function();
    Future.delayed(const Duration(milliseconds: 200), () {
      isClosingPopup = false;
    });
  }

  _initialFunction() async {
    firstNameFocusNode = FocusNode();
    context.read<AccountInfoCubit>().resetFile();

    await context.read<AccountInfoCubit>().getStatus();
    if (mounted) {
      _getStatusSuccess();
    }
  }

  void _getStatusSuccess() {
    if (context.read<AccountInfoCubit>().state.isAccountVerified) {
      CommonFunctions.showDialogPopUp(
        context,
        CustomAlertDialog(
          message: StringConstants.approvedSuccess.tr(),
          actionButtonText: StringConstants.ok.tr(),
          onActionButtonPressed: () =>
              delayedPop(function: () => Navigator.pop(context)),
          headerImage: Assets.alertSuccess,
          isLoading: false,
          messageTextStyle: FontPalette.normal18,
        ),
        barrierDismissible: false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AccountInfoCubit, AccountInfoState>(
      listenWhen: (previous, current) =>
          previous.idCardFetchingStatus != current.idCardFetchingStatus,
      listener: (context, state) {
        if (state.isFetchingStatus == DataStatus.success &&
            !state.isAccountVerified) {
          CommonFunctions.showDialogPopUp(
            context,
            CustomAlertDialog(
              message: StringConstants.idCardUnder.tr(),
              actionButtonText: StringConstants.ok.tr(),
              onActionButtonPressed: delayedPop,
              headerImage: Assets.alertSuccess,
              isLoading: false,
              messageTextStyle: FontPalette.normal18.copyWith(
                color: myColorScheme(context).titleColor,
              ),
            ),
            barrierDismissible: false,
          );
        }
      },
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onPanDown: (_) => FocusScope.of(context).requestFocus(FocusNode()),
        child: Scaffold(
          appBar: AppBar(
            elevation: 0,
            systemOverlayStyle: SystemUiOverlayStyle.dark,
            backgroundColor: Colors.transparent,
            surfaceTintColor: Colors.transparent,
          leading: Navigator.of(context).canPop()
              ? IconButton(
                  icon: const Icon(Icons.arrow_back_ios),
                  onPressed: () => Navigator.pop(context),
                )
              : null,
          ),
          body: AnimationLimiter(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 37.0.w),
              child: RefreshIndicator(
                onRefresh: () async {
                  _initialFunction();
                },
                child: ListView(
                  children: [
                    120.verticalSpace,
                    Center(
                      child: Hero(
                        tag: Assets.logoSvg,
                        child: SizedBox(
                          width: 99.w,
                          height: 97.h,
                          child: SvgPicture.asset(Assets.logoSvg),
                        ),
                      ),
                    ),
                    54.verticalSpace,
                    Text(
                      StringConstants.accountInformation.tr(),
                      style: FontPalette.bold20
                          .copyWith(color: myColorScheme(context).titleColor),
                    ),
                    30.verticalSpace,
                    BlocBuilder<AccountInfoCubit, AccountInfoState>(
                      builder: (context, accountInfoState) {
                        int identity =
                            accountInfoState.statusData?.identity ?? -1;
                        int wallet = accountInfoState.statusData?.wallet ?? -1;
                        // int mobileNo = accountInfoState.statusData?.mobileNo ?? -1;
                        // int googleToken = accountInfoState.statusData?.googleToken ?? -1;
                        // bool accountStatus = accountInfoState.isAccountVerified;
                        DataStatus isFetchingStatus =
                            accountInfoState.isFetchingStatus;

                        if (isFetchingStatus == DataStatus.loading) {
                          return Column(
                            children: AnimationConfiguration.toStaggeredList(
                              childAnimationBuilder: (widget) => SlideAnimation(
                                verticalOffset: 50.0,
                                child: FadeInAnimation(
                                  child: widget,
                                ),
                              ),
                              children: [
                                CommonShimmer(
                                    height: 45.h, width: 1.sw, br: 10),
                                35.verticalSpace,
                                CommonShimmer(
                                    height: 45.h, width: 1.sw, br: 10),
                                35.verticalSpace,
                                // CommonShimmer(height: 45.h, width: 1.sw, br: 10),
                                // 35.verticalSpace,
                                // CommonShimmer(height: 45.h, width: 1.sw, br: 10),
                                // 50.verticalSpace,
                                // CommonShimmer(
                                //   height: 52.h,
                                //   width: 299.w,
                                //   br: 50,
                                // ),
                                20.verticalSpace,
                              ],
                            ),
                          );
                        }

                        return Form(
                          child: Column(
                            children: staggeredAnimation(
                              children: [
                                CommonTextField(
                                  hintType: AccountInfoType.identity,
                                  status: isFetchingStatus == DataStatus.loading
                                      ? 3
                                      : identity,
                                  showCustomSuffixBox: true,
                                  onSuffixIconTap: () async {
                                    if (identity == 0) {
                                      if (mounted) {
                                        context.read<AccountInfoCubit>()
                                          ..getStatus()
                                          ..setIdCardFetchingStatus(
                                              DataStatus.loading);
                                      }
                                      _getStatusSuccess();
                                    } else {
                                      Navigator.pushNamed(
                                        context,
                                        routeUploadAadhaarScreen,
                                      );
                                    }
                                  },
                                  isEditable: false,
                                  textInputType: TextInputType.name,
                                  textInputAction: TextInputAction.next,
                                  labelText: StringConstants.bindIdCard.tr(),
                                  hintText: StringConstants.uploadIdCard.tr(),
                                  controller: aadhaarCardController,
                                  onChanged: (_) {},
                                ),
                                35.verticalSpace,
                                CommonTextField(
                                  hintType: AccountInfoType.wallet,
                                  status: wallet,
                                  showCustomSuffixBox: true,
                                  onSuffixIconTap: () {
                                    Navigator.pushNamed(
                                      context,
                                      routeOpenWalletScreen,
                                    );
                                  },
                                  isEditable: false,
                                  textInputType: TextInputType.phone,
                                  textInputAction: TextInputAction.next,
                                  labelText: StringConstants.openWallet.tr(),
                                  hintText: StringConstants.walletPassword.tr(),
                                  controller: phoneNumberController,
                                  onChanged: (_) {},
                                ),
                                // 35.verticalSpace,
                                // CommonTextField(
                                //   hintType: AccountInfoType.mobileNo,
                                //   status: mobileNo,
                                //   showCustomSuffixBox: true,
                                //   onSuffixIconTap: () {
                                //     Navigator.pushNamed(
                                //       context,
                                //       routePhoneVerificationScreen,
                                //     );
                                //   },
                                //   isEditable: false,
                                //   textInputType: TextInputType.name,
                                //   textInputAction: TextInputAction.next,
                                //   labelText: StringConstants.reservedPhone.tr(),
                                //   hintText: StringConstants.enterPhoneNumber.tr(),
                                //   controller: phoneNumberController,
                                //   onChanged: (_) {},
                                // ),
                                // 35.verticalSpace,
                                // CommonTextField(
                                //     hintType: AccountInfoType.googleToken,
                                //     status: googleToken,
                                //     showCustomSuffixBox: true,
                                //     onSuffixIconTap: () {
                                //       Navigator.pushNamed(context, routeGoogleAuthenticationScreen);
                                //     },
                                //     isEditable: false,
                                //     textInputType: TextInputType.name,
                                //     textInputAction: TextInputAction.next,
                                //     labelText: StringConstants.googleAuthentication.tr(),
                                //     hintText: StringConstants.enterGoogleCode.tr(),
                                //     controller: googleAuthCodeController,
                                //     onChanged: (_) {}),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
