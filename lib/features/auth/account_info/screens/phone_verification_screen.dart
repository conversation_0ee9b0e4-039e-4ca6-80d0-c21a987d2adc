import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/api/network/network_helper.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';
import 'package:sf_app_v2/core/widgets/number_prefix_tile.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/account_info/account_info_cubit.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/phone/phone_cubit.dart';
import '../../../../core/constants/string_constants.dart';
import '../../../../core/constants/assets.dart';
import '../../../../core/utils/mixin/animation.dart';
import '../../../../core/validator.dart';
import '../../../../core/widgets/common_text_field.dart';

class PhoneVerificationScreen extends StatefulWidget {
  const PhoneVerificationScreen({super.key});

  @override
  State<PhoneVerificationScreen> createState() => _PhoneVerificationScreenState();
}

class _PhoneVerificationScreenState extends State<PhoneVerificationScreen> with Validator, StaggeredAnimation {
  final mobileController = TextEditingController();
  final _formGlobalKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onPanDown: (_) => FocusScope.of(context).requestFocus(FocusNode()),
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
        ),
        body: SingleChildScrollView(
          child: AnimationLimiter(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 37.0.w),
              child: Form(
                key: _formGlobalKey,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: staggeredAnimation(
                    children: [
                      100.verticalSpace,
                      Center(
                        child: Hero(
                          tag: Assets.logoSvg,
                          child: SizedBox(
                            width: 99.w,
                            height: 97.h,
                            child: SvgPicture.asset(Assets.logoSvg),
                          ),
                        ),
                      ),
                      54.verticalSpace,
                      Text(
                        StringConstants.phoneVerification.tr(),
                        style: FontPalette.semiBold20.copyWith(color: myColorScheme(context).titleColor),
                      ),
                      30.verticalSpace,
                      CommonTextField(
                        height: 50.h,
                        maxLength: 10,
                        prefixIcon: WidgetExtension.crossSwitch(
                          first: const NumberPrefixTile(),
                          value: false,
                        ),
                        textInputType: TextInputType.number,
                        textInputAction: TextInputAction.next,
                        labelText: StringConstants.enterPhoneNumber.tr(),
                        hintText: StringConstants.hintPhone.tr(),
                        controller: mobileController,
                        validator: (_) => validateMobile(mobileController.text),
                        onChanged: (_) {},
                      ),
                      25.verticalSpace,
                      Center(
                        child: BlocListener<AccountInfoCubit, AccountInfoState>(
                          listenWhen: (previous, current) => previous.isFetchingStatus != current.isFetchingStatus,
                          listener: (context, state) {
                            if (state.isFetchingStatus == DataStatus.success) {
                              Navigator.pop(context);
                            }
                          },
                          child: BlocConsumer<PhoneCubit, PhoneState>(
                            listenWhen: (previous, current) => previous.bindStatus != current.bindStatus,
                            listener: (context, state) {
                              if (state.bindStatus == DataStatus.success) {
                                context.read<AccountInfoCubit>().getStatus();
                              }
                              if (state.bindStatus == DataStatus.failed) {
                                NetworkHelper.handleMessage(
                                  state.error,
                                  context,
                                  type: HandleTypes.customDialog,
                                  snackBarType: SnackBarType.error,
                                );
                              }
                            },
                            builder: (context, state) {
                              return CustomButton(
                                width: 357.w,
                                height: 52.h,
                                label: StringConstants.submit.tr(),
                                isOutlined: false,
                                isEnabled: true,
                                isLoading: state.bindStatus == DataStatus.loading,
                                onPressed: () {
                                  if (_formGlobalKey.currentState!.validate()) {
                                    context.read<PhoneCubit>().bindMobileNumber(
                                          mobileController.text,
                                        );
                                  }
                                },
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
