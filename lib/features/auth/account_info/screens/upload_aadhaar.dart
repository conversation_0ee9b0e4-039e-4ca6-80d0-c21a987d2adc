import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:sf_app_v2/core/api/network/network_helper.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/account_info/account_info_cubit.dart';
import 'package:sf_app_v2/features/auth/account_info/widgets/aadhar_dotted_container.dart';
import '../../../../core/constants/string_constants.dart';
import '../../../../core/utils/mixin/animation.dart';
import '../../../../core/validator.dart';
import '../../../../core/widgets/common_dropdown.dart';
import '../../../../core/widgets/common_text_field.dart';

class UploadAadhaarScreen extends StatelessWidget
    with Validator, StaggeredAnimation {
  final aadhaarCardController = TextEditingController();

  UploadAadhaarScreen({super.key});
  final _formGlobalKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.transparent,
      ),
      body: SingleChildScrollView(
        child: AnimationLimiter(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 37.0.w),
            child: Form(
              key: _formGlobalKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: staggeredAnimation(
                  children: [
                    Text(
                      StringConstants.idCardConfirmation.tr(),
                      style: FontPalette.bold20.copyWith(
                        color: myColorScheme(context).titleColor,
                      ),
                    ),
                    30.verticalSpace,
                    BlocBuilder<AccountInfoCubit, AccountInfoState>(
                      builder: (context, state) {
                        return CommonDropdown(
                          labelText: StringConstants.type.tr(),
                          hintText: StringConstants.idCard.tr(),
                          items: state.idTypes
                              .map((type) => DropdownItem(
                                    value: type['value']?.toString() ?? '',
                                    label: type['value']?.toString().tr() ?? '',
                                  ))
                              .toList(),
                          initialValue: state.selectedIdType.toString(),
                          onItemSelected: (item) {
                            context
                                .read<AccountInfoCubit>()
                                .setSelectedIdType(item.value);
                            aadhaarCardController.clear();
                          },
                        );
                      },
                    ),
                    30.verticalSpace,
                    CommonTextField(
                      maxLength: 12,
                      textInputType: TextInputType.text,
                      textInputAction: TextInputAction.next,
                      labelText: StringConstants.idCardNumber.tr(),
                      hintText: 'xxxxxxxxxxxx',
                      controller: aadhaarCardController,
                      validator: (_) => validateAadhaarNumber(
                        aadhaarCardController.text,
                      ),
                      onChanged: (_) {},
                    ),
                    28.verticalSpace,
                    Center(
                      child: BlocSelector<AccountInfoCubit, AccountInfoState,
                          (File?, File?)>(
                        selector: (state) =>
                            (state.imageFileFront, state.imageFileBack),
                        builder: (context, value) {
                          return Column(
                            children: [
                              Text(
                                StringConstants.idCardFront.tr(),
                                style: FontPalette.medium14.copyWith(
                                  color: myColorScheme(context).titleColor,
                                ),
                              ),
                              11.verticalSpace,
                              AadhaarDottedContainer(
                                width: 266,
                                height: 195,
                                label: StringConstants.idCardFront.tr(),
                                imageFile: value.$1,
                                onPressed: () => context
                                    .read<AccountInfoCubit>()
                                    .getAadhaarImage(type: CardType.front),
                              ),
                              20.verticalSpace,
                              Text(
                                StringConstants.idCardBack.tr(),
                                style: FontPalette.medium14.copyWith(
                                  color: myColorScheme(context).titleColor,
                                ),
                              ),
                              11.verticalSpace,
                              AadhaarDottedContainer(
                                width: 266,
                                height: 195,
                                label: StringConstants.idCardBack.tr(),
                                imageFile: value.$2,
                                onPressed: () => context
                                    .read<AccountInfoCubit>()
                                    .getAadhaarImage(type: CardType.back),
                              ),
                              45.verticalSpace,
                              MultiBlocListener(
                                listeners: [
                                  BlocListener<AccountInfoCubit,
                                      AccountInfoState>(
                                    listenWhen: (previous, current) =>
                                        previous.isFetchingStatus !=
                                        current.isFetchingStatus,
                                    listener: (context, state) {
                                      if (state.isFetchingStatus ==
                                          DataStatus.success) {
                                        Navigator.pop(context);
                                      }
                                      if (state.isFetchingStatus ==
                                          DataStatus.failed) {
                                        NetworkHelper.handleMessage(
                                          state.error,
                                          context,
                                          type: HandleTypes.customDialog,
                                          snackBarType: SnackBarType.error,
                                        );
                                      }
                                    },
                                  ),
                                  BlocListener<AccountInfoCubit,
                                      AccountInfoState>(
                                    listenWhen: (previous, current) =>
                                        previous.submitAadhaarCardStatus !=
                                        current.submitAadhaarCardStatus,
                                    listener: (context, state) {
                                      if (state.submitAadhaarCardStatus ==
                                          DataStatus.success) {
                                        context
                                            .read<AccountInfoCubit>()
                                            .getStatus();
                                      }
                                      if (state.submitAadhaarCardStatus ==
                                          DataStatus.failed) {
                                        NetworkHelper.handleMessage(
                                          state.error,
                                          context,
                                          type: HandleTypes.customDialog,
                                          snackBarType: SnackBarType.error,
                                        );
                                      }
                                    },
                                  ),
                                ],
                                child: BlocSelector<AccountInfoCubit,
                                    AccountInfoState, DataStatus>(
                                  selector: (state) =>
                                      state.submitAadhaarCardStatus,
                                  builder: (context, state) {
                                    return CustomButton(
                                      width: 266.w,
                                      height: 52.h,
                                      label: StringConstants.submit.tr(),
                                      isLoading: state == DataStatus.loading,
                                      isOutlined: false,
                                      onPressed: () {
                                        if (_formGlobalKey.currentState!
                                            .validate()) {
                                          if (value.$1 == null) {
                                            return showSnackBar(
                                              StringConstants.uploadFrontIDCard
                                                  .tr(),
                                              context,
                                            );
                                          }
                                          if (value.$2 == null) {
                                            return showSnackBar(
                                              StringConstants.uploadBackIDCard
                                                  .tr(),
                                              context,
                                            );
                                          }

                                          context
                                              .read<AccountInfoCubit>()
                                              .submitAadhaarCardAuthIdentity(
                                                aadhaarCardNumber:
                                                    aadhaarCardController.text,
                                              );
                                        }
                                      },
                                    );
                                  },
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
