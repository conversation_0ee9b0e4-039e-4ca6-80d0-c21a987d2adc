import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/common_function.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/account_info/account_info_cubit.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/google_authentication/google_authentication_cubit.dart';
import '../../../../core/constants/string_constants.dart';
import '../../../../core/constants/assets.dart';
import '../../../../core/utils/mixin/animation.dart';
import '../../../../core/widgets/common_pin_field.dart';
import '../../../../core/widgets/common_text_field.dart';

class GoogleAuthenticationScreen extends StatefulWidget {
  const GoogleAuthenticationScreen({super.key});

  @override
  State<GoogleAuthenticationScreen> createState() => _GoogleAuthenticationScreenState();
}

class _GoogleAuthenticationScreenState extends State<GoogleAuthenticationScreen> with StaggeredAnimation {
  final TextEditingController googleCodeController = TextEditingController();
  final textController = TextEditingController();
  final _formGlobalKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    CommonFunctions.afterInit(_initialFunction);
  }

  _initialFunction() {
    context.read<GoogleAuthenticationCubit>();
    context.read<GoogleAuthenticationCubit>().googleAuth();
  }

  _onSubmit() {
    context.read<GoogleAuthenticationCubit>().googleAuthCheck(textController.text);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onPanDown: (_) => FocusScope.of(context).requestFocus(FocusNode()),
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
        ),
        body: SingleChildScrollView(
          child: AnimationLimiter(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 57.0.w),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: staggeredAnimation(
                    children: [
                      Text(
                        StringConstants.googleAuthTitleText.tr(),
                        style: FontPalette.semiBold18.copyWith(
                          color: myColorScheme(context).titleColor,
                        ),
                      ),
                      35.verticalSpace,
                      Text(
                        StringConstants.googleAuthSubTitleText.tr(),
                        style: FontPalette.normal14.copyWith(
                          color: myColorScheme(context).titleColor,
                        ),
                      ),
                      15.verticalSpace,
                      Text(
                        StringConstants.googleAuthSubTitle2Text.tr(),
                        style: FontPalette.normal12.copyWith(
                          color: myColorScheme(context).titleColor,
                        ),
                      ),
                      37.verticalSpace,
                      BlocSelector<GoogleAuthenticationCubit, GoogleAuthenticationState, Uint8List?>(
                        selector: (googleAuthenticationCubit) => googleAuthenticationCubit.bytes,
                        builder: (context, value) {
                          return Container(
                            height: 157.h,
                            width: 157.w,
                            decoration: value != null
                                ? BoxDecoration(
                                    image: DecorationImage(
                                      image: MemoryImage(value),
                                    ),
                                  )
                                : null,
                            child: value != null ? null : const CircularProgressIndicator.adaptive(),
                          );
                        },
                      ),
                      61.verticalSpace,
                      Text(
                        StringConstants.qrCodeScan.tr(),
                        style: FontPalette.normal14.copyWith(
                          color: myColorScheme(context).titleColor,
                        ),
                      ),
                      13.verticalSpace,
                      Text(
                        StringConstants.or.tr(),
                        style: FontPalette.bold11.copyWith(
                          color: myColorScheme(context).titleColor,
                        ),
                      ),
                      14.verticalSpace,
                      Text(
                        StringConstants.copyCode.tr(),
                        style: FontPalette.normal14.copyWith(
                          color: myColorScheme(context).titleColor,
                        ),
                      ),
                      65.verticalSpace,
                      Form(
                        key: _formGlobalKey,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CommonTextField(
                              isEditable: false,
                              suffixIcon: InkWell(
                                onTap: () {
                                  Clipboard.setData(
                                    ClipboardData(
                                      text: googleCodeController.text,
                                    ),
                                  );
                                  CommonFunctions().showFlutterToast(
                                    StringConstants.copiedClipboard.tr(),
                                  );
                                },
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 10.0),
                                  child: SvgPicture.asset(
                                    Assets.copy,
                                    height: 20.h,
                                    width: 20.w,
                                  ),
                                ),
                              ),
                              textInputType: TextInputType.phone,
                              textInputAction: TextInputAction.next,
                              labelText: StringConstants.code.tr(),
                              hintText: StringConstants.hintWallet.tr(),
                              controller: googleCodeController,
                              validator: (_) {
                                return null;
                              },
                              onChanged: (_) {},
                            ),
                            25.verticalSpace,
                            Text(
                              StringConstants.googleAuthCode.tr(),
                              style: FontPalette.normal16.copyWith(
                                color: myColorScheme(context).titleColor,
                              ),
                            ),
                            16.verticalSpace,
                            CommonPinFiledText(
                              controller: textController,
                              type: StringConstants.typeGoogle.tr(),
                            ),
                            45.verticalSpace,
                            MultiBlocListener(
                              listeners: [
                                BlocListener<GoogleAuthenticationCubit, GoogleAuthenticationState>(
                                  listenWhen: (previous, current) =>
                                      previous.googleAuthCheckStatus != current.googleAuthCheckStatus,
                                  listener: (context, state) {
                                    if (state.googleAuthCheckStatus == DataStatus.success) {
                                      Navigator.pop(context);
                                      context.read<AccountInfoCubit>().getStatus();
                                    }
                                    if (state.googleAuthCheckStatus == DataStatus.failed) {
                                      textController.clear();
                                    }
                                  },
                                ),
                                BlocListener<GoogleAuthenticationCubit, GoogleAuthenticationState>(
                                  listenWhen: (previous, current) =>
                                      previous.googleAuthStatus != current.googleAuthStatus,
                                  listener: (context, state) {
                                    if (state.googleAuthStatus == DataStatus.success) {
                                      googleCodeController.text = state.googleTokenData?.token ?? '';
                                    }
                                  },
                                ),
                              ],
                              child: BlocSelector<GoogleAuthenticationCubit, GoogleAuthenticationState, DataStatus>(
                                selector: (state) => state.googleAuthCheckStatus,
                                builder: (context, state) {
                                  return CustomButton(
                                    width: 356.w,
                                    height: 52.h,
                                    label: StringConstants.submit.tr(),
                                    isOutlined: false,
                                    isEnabled: true,
                                    isLoading: state == DataStatus.loading,
                                    onPressed: () {
                                      if (_formGlobalKey.currentState!.validate()) {
                                        _onSubmit();
                                      }
                                    },
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
