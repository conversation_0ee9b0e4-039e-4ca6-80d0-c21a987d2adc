import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/common_function.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/account_info/account_info_cubit.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/wallet/wallet_cubit.dart';

import '../../../../core/api/network/network_helper.dart';
import '../../../../core/constants/assets.dart';
import '../../../../core/constants/string_constants.dart';
import '../../../../core/routes/routes.dart';
import '../../../../core/utils/mixin/animation.dart';
import '../../../../core/widgets/common_pin_field.dart';

class OpenWalletScreen extends StatefulWidget {
  const OpenWalletScreen({super.key});

  @override
  State<OpenWalletScreen> createState() => _OpenWalletScreenState();
}

class _OpenWalletScreenState extends State<OpenWalletScreen>
    with StaggeredAnimation {
  final walletPasswordController = TextEditingController();
  final confirmWalletPasswordController = TextEditingController();
  final _formGlobalKey = GlobalKey<FormState>();

  void _onSubmit() {
    context.read<WalletCubit>().setupPaymentPassword(
          password: walletPasswordController.text,
          confirmPassword: confirmWalletPasswordController.text,
        );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onPanDown: (_) => FocusScope.of(context).requestFocus(FocusNode()),
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () {
              if (Navigator.of(context).canPop()) {
                Navigator.pop(context);
              } else {
                CommonFunctions.logoutUser(context);
              }
            },
          ),
        ),
        body: SingleChildScrollView(
          child: AnimationLimiter(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 37.0.w),
              child: Form(
                key: _formGlobalKey,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: staggeredAnimation(
                    children: [
                      70.verticalSpace,
                      Center(
                        child: Hero(
                          tag: Assets.logoSvg,
                          child: SizedBox(
                            width: 99.w,
                            height: 97.h,
                            child: SvgPicture.asset(Assets.logoSvg),
                          ),
                        ),
                      ),
                      54.verticalSpace,
                      Text(
                        StringConstants.setWalletPassword.tr(),
                        style: FontPalette.semiBold20
                            .copyWith(color: myColorScheme(context).titleColor),
                      ),
                      30.verticalSpace,
                      Text(
                        StringConstants.enterWalletPassword.tr(),
                        style: FontPalette.normal14
                            .copyWith(color: myColorScheme(context).titleColor),
                      ),
                      15.verticalSpace,
                      CommonPinFiledText(
                        obscureText: true,
                        controller: walletPasswordController,
                      ),
                      25.verticalSpace,
                      Text(
                        StringConstants.confirmWalletPassword.tr(),
                        style: FontPalette.normal14
                            .copyWith(color: myColorScheme(context).titleColor),
                      ),
                      15.verticalSpace,
                      CommonPinFiledText(
                        obscureText: true,
                        type: StringConstants.typeConfirmWallet.tr(),
                        controller: confirmWalletPasswordController,
                        passwordController: walletPasswordController,
                      ),
                      45.verticalSpace,
                      Center(
                        child: BlocListener<AccountInfoCubit, AccountInfoState>(
                          listenWhen: (previous, current) =>
                              previous.isFetchingStatus !=
                              current.isFetchingStatus,
                          listener: (context, state) {
                            if (state.isFetchingStatus == DataStatus.success) {
                              Navigator.pushNamedAndRemoveUntil(
                                context,
                                routeMainScreen,
                                (route) => false,
                              );
                            }
                            if (state.isFetchingStatus == DataStatus.failed) {
                              NetworkHelper.handleMessage(
                                state.error,
                                context,
                                type: HandleTypes.customDialog,
                                snackBarType: SnackBarType.error,
                              );
                            }
                          },
                          child: BlocConsumer<WalletCubit, WalletState>(
                            listenWhen: (previous, current) =>
                                previous.setupWalletStatus !=
                                current.setupWalletStatus,
                            listener: (context, state) {
                              if (state.setupWalletStatus ==
                                  DataStatus.success) {
                                context.read<AccountInfoCubit>().getStatus();
                              }
                              if (state.setupWalletStatus ==
                                  DataStatus.failed) {
                                NetworkHelper.handleMessage(
                                  state.error,
                                  context,
                                  type: HandleTypes.customDialog,
                                  snackBarType: SnackBarType.error,
                                );
                              }
                            },
                            builder: (context, state) {
                              return CustomButton(
                                width: 357.w,
                                height: 52.h,
                                label: StringConstants.submit.tr(),
                                isOutlined: false,
                                isLoading: state.setupWalletStatus ==
                                    DataStatus.loading,
                                onPressed: () {
                                  if (_formGlobalKey.currentState!.validate()) {
                                    _onSubmit();
                                  }
                                },
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class AccountInfoProvider {}
