part of 'sign_in_cubit.dart';

class SignInState extends Equatable {
  final LoginDataModel? loginData;
  final LoginCaptcha? captchaData;
  final Uint8List? captchaImage;
  final DataStatus captchaFetchStatus;
  final DataStatus loginFetchStatus;
  final DataStatus termsFetchStatus;
  final DataStatus policyFetchStatus;
  final DataStatus benefitRulesFetchStatus;
  final DataStatus chatConfigFetchStatus;
  final ChatConfigModel? chatConfig;
  final String? termsAndConditions;
  final String? privacyPolicy;
  final String? benefitRules;
  final String? token;
  final String? error;
  final PhoneCountry? selectedCountryCode;
  final bool showLoading;
  const SignInState({
    this.captchaData,
    this.captchaImage,
    this.loginData,
    this.captchaFetchStatus = DataStatus.idle,
    this.loginFetchStatus = DataStatus.idle,
    this.termsFetchStatus = DataStatus.idle,
    this.policyFetchStatus = DataStatus.idle,
    this.benefitRulesFetchStatus = DataStatus.idle,
    this.chatConfigFetchStatus = DataStatus.idle,
    this.chatConfig,
    this.termsAndConditions,
    this.privacyPolicy,
    this.benefitRules,
    this.error,
    this.token,
    this.selectedCountryCode,
    this.showLoading = false,
  });

  @override
  List<Object?> get props => [
        captchaData,
        captchaImage,
        captchaFetchStatus,
        loginData,
        loginFetchStatus,
        termsFetchStatus,
        policyFetchStatus,
        benefitRulesFetchStatus,
        chatConfigFetchStatus,
        chatConfig,
        termsAndConditions,
        privacyPolicy,
        benefitRules,
        error,
        token,
        selectedCountryCode,
        showLoading,
      ];

  SignInState copyWith({
    LoginCaptcha? captchaData,
    Uint8List? captchaImage,
    DataStatus? captchaFetchStatus,
    DataStatus? loginFetchStatus,
    LoginDataModel? loginData,
    DataStatus? termsFetchStatus,
    DataStatus? policyFetchStatus,
    DataStatus? benefitRulesFetchStatus,
    DataStatus? chatConfigFetchStatus,
    ChatConfigModel? chatConfig,
    String? termsAndConditions,
    String? privacyPolicy,
    String? benefitRules,
    String? error,
    String? token,
    PhoneCountry? selectedCountryCode,
    bool? showLoading,
  }) {
    return SignInState(
      captchaData: captchaData ?? this.captchaData,
      captchaImage: captchaImage ?? this.captchaImage,
      captchaFetchStatus: captchaFetchStatus ?? this.captchaFetchStatus,
      loginFetchStatus: loginFetchStatus ?? this.loginFetchStatus,
      termsFetchStatus: termsFetchStatus ?? this.termsFetchStatus,
      policyFetchStatus: policyFetchStatus ?? this.policyFetchStatus,
      benefitRulesFetchStatus:
          benefitRulesFetchStatus ?? this.benefitRulesFetchStatus,
      chatConfigFetchStatus:
          chatConfigFetchStatus ?? this.chatConfigFetchStatus,
      chatConfig: chatConfig ?? this.chatConfig,
      termsAndConditions: termsAndConditions ?? this.termsAndConditions,
      privacyPolicy: privacyPolicy ?? this.privacyPolicy,
      benefitRules: benefitRules ?? this.benefitRules,
      error: error ?? this.error,
      loginData: loginData ?? this.loginData,
      token: token ?? this.token,
      selectedCountryCode: selectedCountryCode ?? this.selectedCountryCode,
      showLoading: showLoading ?? this.showLoading,
    );
  }
}
