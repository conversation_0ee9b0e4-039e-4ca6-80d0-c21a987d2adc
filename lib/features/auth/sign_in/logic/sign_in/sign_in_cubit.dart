import 'dart:convert';
import 'dart:typed_data';

import 'package:bloc/bloc.dart';
import 'package:sf_app_v2/core/models/phone_country/phone_country.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/auth/sign_in/domain/models/auth/login_captcha_model.dart';
import 'package:sf_app_v2/features/auth/sign_in/domain/models/auth/login_data_model.dart';
import 'package:sf_app_v2/features/auth/sign_in/domain/repository/sign_in_repository.dart';

import '../../domain/models/chat_config/chat_config_model.dart';

part 'sign_in_state.dart';

@injectable
class SignInCubit extends Cubit<SignInState> {
  final SignInRepository _signInService;

  SignInCubit(this._signInService) : super(const SignInState());

  Future<void> loginCaptcha() async {
    emit(state.copyWith(captchaFetchStatus: DataStatus.loading));
    try {
      final result = await _signInService.requestLoginCaptcha();
      if (result.data != null) {
        int? commaIndex = result.data?.data.captcha.indexOf(',');
        Uint8List bytes = base64Decode(
          result.data?.data.captcha.substring(commaIndex! + 1) ?? '',
        );
        emit(
          state.copyWith(
            captchaData: result.data,
            captchaImage: bytes,
            captchaFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            captchaFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          captchaFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> requestLogin({
    required String username,
    required String password,
    required String k,
    required String captcha,
  }) async {
    emit(
      state.copyWith(
        loginFetchStatus: DataStatus.loading,
        showLoading: true,
      ),
    );
    try {
      final result = await _signInService.requestLogin(
        username: username,
        password: password,
        k: k,
        captcha: captcha,
      );
      if (result.data != null) {
        emit(
          state.copyWith(
            loginFetchStatus: DataStatus.success,
            loginData: result.data,
            token: result.token,
          ),
        );
      } else {
        emit(
          state.copyWith(
            loginFetchStatus: DataStatus.failed,
            chatConfigFetchStatus: DataStatus.idle,
            error: result.error,
            showLoading: false,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          loginFetchStatus: DataStatus.failed,
          chatConfigFetchStatus: DataStatus.idle,
          error: e.toString(),
          showLoading: false,
        ),
      );
    }
  }

  void setShowLoading(bool showLoading) =>
      emit(state.copyWith(showLoading: showLoading));

  Future<void> getTermsAndConditions() async {
    emit(state.copyWith(termsFetchStatus: DataStatus.loading));
    try {
      final result = await _signInService.getTermsAndConditions();
      if (result.data != null) {
        emit(
          state.copyWith(
            termsFetchStatus: DataStatus.success,
            termsAndConditions: result.data,
          ),
        );
      } else {
        emit(
          state.copyWith(
            termsFetchStatus: DataStatus.failed,
            error: result.error?.trim(),
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          termsFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> getPrivacyPolicy() async {
    emit(state.copyWith(policyFetchStatus: DataStatus.loading));
    try {
      final result = await _signInService.getPrivacyPolicy();
      if (result.data != null) {
        emit(
          state.copyWith(
            policyFetchStatus: DataStatus.success,
            privacyPolicy: result.data,
          ),
        );
      } else {
        emit(
          state.copyWith(
            policyFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          policyFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> getBenefitRules() async {
    emit(state.copyWith(benefitRulesFetchStatus: DataStatus.loading));
    try {
      final result = await _signInService.getBenefitRules();
      if (result.data != null) {
        emit(
          state.copyWith(
            benefitRulesFetchStatus: DataStatus.success,
            benefitRules: result.data,
          ),
        );
      } else {
        emit(
          state.copyWith(
            benefitRulesFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          benefitRulesFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> fetchUserChatConfig() async {
    emit(state.copyWith(chatConfigFetchStatus: DataStatus.loading));
    try {
      final result = await _signInService.fetchUserChatConfig();
      if (result.data != null) {
        emit(
          state.copyWith(
            chatConfigFetchStatus: DataStatus.success,
            chatConfig: result.data,
          ),
        );
      } else {
        emit(
          state.copyWith(
            chatConfigFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          chatConfigFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  void setSelectedCountryCode(PhoneCountry countryCode) =>
      emit(state.copyWith(selectedCountryCode: countryCode));
}
