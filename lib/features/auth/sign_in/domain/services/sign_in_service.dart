import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/api/endpoint/api_endpoints.dart';
import 'package:sf_app_v2/core/api/network/network.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/auth/sign_in/domain/models/auth/login_captcha_model.dart';
import 'package:sf_app_v2/features/auth/sign_in/domain/models/auth/login_data_model.dart';

import '../models/chat_config/chat_config_model.dart';
import '../repository/sign_in_repository.dart';

/// Service class that implements the SignInRepository interface to handle authentication related API calls
@Injectable(as: SignInRepository)
class SignInService implements SignInRepository {
  /// Authenticates user with username and password
  /// [username] - User's username/email
  /// [password] - User's password
  /// [k] - Captcha key
  /// [captcha] - Captcha value entered by user
  /// Returns a [ResponseResult] containing [LoginDataModel] and auth token on success
  @override
  Future<ResponseResult<LoginDataModel>> requestLogin({
    required String username,
    required String password,
    required String k,
    required String captcha,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.login,
        data: {
          "username": username,
          "password": password,
          "captcha": captcha,
          "k": k,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] != 200) {
          return ResponseResult(error: response.data['msg']);
        } else {
          return ResponseResult(
            data: LoginDataModel.fromJson(response.data),
            token: response.headers['authorization']?.first,
          );
        }
      } else {
        return ResponseResult(error: 'Failed to login');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches a new captcha for login
  /// Returns a [ResponseResult] containing [LoginCaptcha] on success
  @override
  Future<ResponseResult<LoginCaptcha>> requestLoginCaptcha() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.loginCaptcha,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: LoginCaptcha.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get captcha');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches privacy policy content
  /// Returns a [ResponseResult] containing policy text on success
  @override
  Future<ResponseResult<String>> getPrivacyPolicy() async {
    try {
      final response = await NetworkProvider().get(ApiEndpoints.privacyPolicy);
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: response.data['data']);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get privacy policy');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches terms and conditions content
  /// Returns a [ResponseResult] containing terms text on success
  @override
  Future<ResponseResult<String>> getTermsAndConditions() async {
    try {
      final response =
          await NetworkProvider().get(ApiEndpoints.termsAndConditions);
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: response.data['data']);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get terms and conditions');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches user's chat configuration
  /// Returns a [ResponseResult] containing [ChatConfigModel] on success
  @override
  Future<ResponseResult<ChatConfigModel>> fetchUserChatConfig() async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.tcUserSig,
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: ChatConfigModel.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch user chat config');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches benefit rules content
  /// Returns a [ResponseResult] containing rules text on success
  @override
  Future<ResponseResult<String>> getBenefitRules() async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.benefitRules,
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: response.data['data']);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get benefit rules');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
