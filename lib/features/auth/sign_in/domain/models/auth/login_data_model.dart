// To parse this JSON data, do
//
//     final loginDataModel = loginDataModelFromJson(jsonString);

import 'dart:convert';

LoginDataModel loginDataModelFromJson(String str) =>
    LoginDataModel.fromJson(json.decode(str));

String loginDataModelToJson(LoginDataModel data) => json.encode(data.toJson());

class LoginDataModel {
  int code;
  LoginData data;
  String msg;

  LoginDataModel({
    required this.code,
    required this.data,
    required this.msg,
  });

  factory LoginDataModel.fromJson(Map<String, dynamic> json) => LoginDataModel(
        code: json["code"],
        data: LoginData.fromJson(json["data"]),
        msg: json["msg"],
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "data": data.toJson(),
        "msg": msg,
      };
}

class LoginData {
  bool auth;
  String email;
  int level;
  String username;

  LoginData({
    required this.auth,
    required this.email,
    required this.level,
    required this.username,
  });

  factory LoginData.fromJson(Map<String, dynamic> json) => LoginData(
        auth: json["auth"],
        email: json["email"],
        level: json["level"],
        username: json["username"],
      );

  Map<String, dynamic> toJson() => {
        "auth": auth,
        "email": email,
        "level": level,
        "username": username,
      };
}
