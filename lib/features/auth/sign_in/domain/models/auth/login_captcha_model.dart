// To parse this JSON data, do
//
//     final loginCaptcha = loginCaptchaFromJson(jsonString);

import 'dart:convert';

LoginCaptcha loginCaptchaFromJson(String str) =>
    LoginCaptcha.fromJson(json.decode(str));

String loginCaptchaToJson(LoginCaptcha data) => json.encode(data.toJson());

class LoginCaptcha {
  int code;
  LoginCaptchaData data;
  String msg;

  LoginCaptcha({
    required this.code,
    required this.data,
    required this.msg,
  });

  factory LoginCaptcha.fromJson(Map<String, dynamic> json) => LoginCaptcha(
        code: json["code"],
        data: LoginCaptchaData.fromJson(json["data"]),
        msg: json["msg"],
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "data": data.toJson(),
        "msg": msg,
      };
}

class LoginCaptchaData {
  String captcha;
  String k;

  LoginCaptchaData({
    required this.captcha,
    required this.k,
  });

  factory LoginCaptchaData.fromJson(Map<String, dynamic> json) =>
      LoginCaptchaData(
        captcha: json["captcha"],
        k: json["k"],
      );

  Map<String, dynamic> toJson() => {
        "captcha": captcha,
        "k": k,
      };
}
