import 'package:freezed_annotation/freezed_annotation.dart';

part 'chat_config_model.freezed.dart';
part 'chat_config_model.g.dart';

@freezed
class ChatConfigModel with _$ChatConfigModel {
  const factory ChatConfigModel({
    int? code,
    ChatConfigData? data,
    String? msg,
  }) = _ChatConfigModel;

  factory ChatConfigModel.fromJson(Map<String, dynamic> json) =>
      _$ChatConfigModelFromJson(json);
}

@freezed
class ChatConfigData with _$ChatConfigData {
  const factory ChatConfigData({
    String? imImage,
    String? nickName,
    String? sdkAppId,
    String? userId,
    String? userSig,
  }) = _ChatConfigData;

  factory ChatConfigData.fromJson(Map<String, dynamic> json) =>
      _$ChatConfigDataFromJson(json);
}
