import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/auth/sign_in/domain/models/auth/login_captcha_model.dart';
import 'package:sf_app_v2/features/auth/sign_in/domain/models/auth/login_data_model.dart';

import '../models/chat_config/chat_config_model.dart';

abstract class SignInRepository {
  const SignInRepository();

  Future<ResponseResult<LoginDataModel>> requestLogin({
    required String username,
    required String password,
    required String captcha,
    required String k,
  });

  Future<ResponseResult<LoginCaptcha>> requestLoginCaptcha();

  Future<ResponseResult<String>> getTermsAndConditions();

  Future<ResponseResult<String>> getPrivacyPolicy();

  Future<ResponseResult<String>> getBenefitRules();

  Future<ResponseResult<ChatConfigModel>> fetchUserChatConfig();
}
