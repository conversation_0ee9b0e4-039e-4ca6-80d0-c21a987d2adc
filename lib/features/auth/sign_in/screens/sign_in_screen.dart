import 'dart:convert';
import 'package:sf_app_v2/core/widgets/custom_country_picker.dart';
import 'package:sf_app_v2/core/models/phone_country/phone_country.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/api/network/models/user.dart';
import 'package:sf_app_v2/core/constants/assets.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/constants/keys.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/utils/log.dart';
import 'package:sf_app_v2/core/utils/secure_storage_helper.dart';
import 'package:sf_app_v2/core/utils/shared_preference_helper.dart';
import 'package:sf_app_v2/core/validator.dart';
import 'package:sf_app_v2/core/widgets/common_text_field.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';
import 'package:sf_app_v2/core/widgets/custom_square_svg_button.dart';
import 'package:sf_app_v2/core/widgets/language_selector_widget.dart';
import 'package:sf_app_v2/features/auth/sign_in/logic/sign_in/sign_in_cubit.dart';

import '../../../../core/api/network/network_helper.dart';
import '../../../../core/shared/logic/country_code/country_code_cubit.dart';
import '../../../../core/utils/mixin/animation.dart';
import '../../account_info/domain/models/status/status_model.dart';
import '../../account_info/logic/account_info/account_info_cubit.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen>
    with Validator, StaggeredAnimation, SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _userNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _captchaController = TextEditingController();
  late final FocusNode _firstNameFocusNode;
  bool _termsAccepted = false;
  late TabController _tabController;
  bool _isEmailTab = true;

  @override
  void initState() {
    super.initState();
    _firstNameFocusNode = FocusNode();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_handleTabChange);
    _initCaptcha();
  }

  void _handleTabChange() {
    if (!_tabController.indexIsChanging) {
      setState(() {
        _isEmailTab = _tabController.index == 0;
      });
    }
  }

  void _initCaptcha() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<SignInCubit>().loginCaptcha();
    });
  }

  @override
  void dispose() {
    _userNameController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _captchaController.dispose();
    _firstNameFocusNode.dispose();
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _handleLogin({required String k}) async {
    if (!_formKey.currentState!.validate()) return;
    if (!_termsAccepted) return;
    String countryCode =
        context.read<SignInCubit>().state.selectedCountryCode?.phoneCode ??
            '+52';
    final String username = _isEmailTab
        ? _userNameController.text
        : countryCode + _phoneController.text;

    context.read<SignInCubit>().requestLogin(
          username: username.trim(),
          password: _passwordController.text.trim(),
          k: k,
          captcha: _captchaController.text.trim(),
        );
  }

  Future<void> _updateSharedPreferences(User userData) async {
    await SharedPreferenceHelper().writeBoolData(
      SharedPreferencesKeys.isLoggedIn,
      true,
    );
    await SharedPreferenceHelper().writeData(
      SharedPreferencesKeys.user,
      jsonEncode(userData.toJson()),
    );
  }

  Future<void> _updateAccountStatus(
      User userData, StatusData? statusData) async {
    await SharedPreferenceHelper().writeBoolData(
      SharedPreferencesKeys.isAccountStatusLogged,
      statusData?.identity == 1,
    );
    await SharedPreferenceHelper().writeBoolData(
      SharedPreferencesKeys.isWalletPasswordSet,
      statusData?.wallet == 1,
    );
  }

  Future<void> _handleLoginSuccess(SignInState state) async {
    await SecureStorageHelper()
        .writeSecureData(LocalStorageKeys.token, state.token ?? '');
    final userData = User.fromJson(state.loginData?.data.toJson() ?? {});
    await _updateSharedPreferences(userData);
    // if (mounted) await context.read<SignInCubit>().fetchUserChatConfig();
    if (mounted) {
      final statusData = await context.read<AccountInfoCubit>().getStatus();
      await _updateAccountStatus(userData, statusData);
    }
    final isWalletPasswordSet =
        SharedPreferenceHelper().getIsWalletPasswordSet() ?? false;

    logDev("SignInScreen", alien: true, 'userData: ${userData.toJson()}');
    if (mounted) {
      context.read<SignInCubit>().setShowLoading(false);
    }
    if (isWalletPasswordSet) {
      if (mounted) {
        Navigator.pushNamedAndRemoveUntil(
          context,
          routeMainScreen,
          (route) => false,
        );
      }
    } else {
      if (mounted) {
        Navigator.pushNamedAndRemoveUntil(
          context,
          routeOpenWalletScreen,
          (route) => false,
        );
      }
    }
  }

  void _handleLoginError() {
    if (!mounted) return;

    context.read<SignInCubit>().loginCaptcha();
    _captchaController.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        child: AnimationLimiter(
          child: Center(
            child: Column(
              children: [
                _buildHeader(),
                _buildLoginForm(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleUserChatConfigSuccess(SignInState state) async {
    if (state.chatConfig != null) {
      await SharedPreferenceHelper().writeData(
        SharedPreferencesKeys.chatConfig,
        jsonEncode(state.chatConfig?.data?.toJson()),
      );
    }
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      actions: [
        LanguageSelectorWidget(
          fontSize: 25.sp,
          heroTag: 'flag_${context.locale.languageCode}',
        ),
        20.horizontalSpace,
      ],
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        _buildLogo(),
        _buildTitle(),
        20.verticalSpace,
      ],
    );
  }

  Widget _buildLogo() {
    return Padding(
      padding: EdgeInsets.all(30.r),
      child: Hero(
        tag: Assets.logoSvg,
        child: SvgPicture.asset(
          Assets.logoSvg,
          width: 99.w,
          height: 121.h,
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Column(
      children: [
        Text(
          StringConstants.login.tr(),
          style: FontPalette.bold28.copyWith(
            fontSize: 30.sp,
            color: myColorScheme(context).primaryColor,
          ),
        ),
        8.verticalSpace,
        Text(
          StringConstants.welcome.tr(),
          style: FontPalette.normal14,
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 37.w),
        child: AutofillGroup(
          child: Column(
            children: staggeredAnimation(
              children: [
                _buildTabBar(),
                35.verticalSpace,
                _isEmailTab ? _buildUsernameField() : _buildPhoneField(),
                35.verticalSpace,
                _buildPasswordField(),
                _buildForgotPassword(),
                _buildCaptchaSection(),
                33.verticalSpace,
                _buildTermsAndConditions(),
                20.verticalSpace,
                _buildLoginButton(),
                12.verticalSpace,
                _buildSignUpText(),
                33.verticalSpace,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: myColorScheme(context).borderColor ?? Colors.grey,
            width: 1.0,
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        indicatorSize: TabBarIndicatorSize.tab,
        dividerHeight: 0,
        indicatorColor: myColorScheme(context).primaryColor,
        labelColor: myColorScheme(context).primaryColor,
        unselectedLabelColor: myColorScheme(context).titleColor,
        labelStyle: FontPalette.medium16,
        unselectedLabelStyle: FontPalette.normal16,
        tabs: [
          Tab(text: StringConstants.email.tr()),
          Tab(text: StringConstants.phoneNumber.tr()),
        ],
      ),
    );
  }

  Widget _buildUsernameField() {
    return CommonTextField(
      autofillHints: const [AutofillHints.username],
      prefixIcon: SvgPicture.asset(
        Assets.userIcon,
        height: 11.h,
        width: 11.w,
      ),
      textInputAction: TextInputAction.next,
      labelText: StringConstants.email.tr(),
      hintText: StringConstants.enterEmail.tr(),
      controller: _userNameController,
      textInputType: TextInputType.name,
      textInputFormatter: [
        FilteringTextInputFormatter.deny(
            RegExp(r'^\s+')), // Prevent leading spaces
      ],
      // validator: (_) => validateEmail(_userNameController.text),
      onChanged: (_) {},
    );
  }

  Widget _buildPasswordField() {
    return CommonTextField(
      enableObscure: true,
      autofillHints: const [AutofillHints.password],
      textInputType: TextInputType.visiblePassword,
      textInputAction: TextInputAction.next,
      labelText: StringConstants.password.tr(),
      hintText: StringConstants.passwordHintText.tr(),
      controller: _passwordController,
      validator: (_) => validateLoginPassword(_passwordController.text),
      onChanged: (_) {},
      onFieldSubmitted: (_) => TextInput.finishAutofillContext(
        shouldSave: true,
      ),
    );
  }

  Widget _buildForgotPassword() {
    return Align(
      alignment: Alignment.topRight,
      child: TextButton(
        onPressed: () {
          Navigator.pushNamed(context, routeForgotPassword);
        },
        child: Text(
          StringConstants.forgotPassword.tr(),
          style: FontPalette.medium12.copyWith(
            color: myColorScheme(context).primaryColor,
          ),
        ),
      ),
    );
  }

  Widget _buildCaptchaSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          StringConstants.captchaCode.tr(),
          style: FontPalette.medium12.copyWith(
            color: myColorScheme(context).primaryColor,
          ),
        ),
        10.verticalSpace,
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCaptchaInput(),
            SizedBox(width: 0.04.sw),
            _buildCaptchaImage(),
            SizedBox(width: 0.05.sw),
            _buildRefreshButton(),
          ],
        ),
      ],
    );
  }

  Widget _buildCaptchaInput() {
    return Flexible(
      flex: 2,
      child: CommonTextField(
        fillColor: myColorScheme(context).cardColor,
        borderColor: myColorScheme(context).cardColor,
        textInputType: TextInputType.text,
        textInputAction: TextInputAction.next,
        hintText: StringConstants.enterTheCode.tr(),
        controller: _captchaController,
        validator: (_) => validateCaptcha(_captchaController.text),
        onChanged: (_) {},
      ),
    );
  }

  Widget _buildCaptchaImage() {
    return BlocSelector<SignInCubit, SignInState, (DataStatus, Uint8List?)>(
      selector: (state) => (
        state.captchaFetchStatus,
        state.captchaImage,
      ),
      builder: (context, state) {
        switch (state.$1) {
          case DataStatus.success:
            return Flexible(
              flex: 1,
              child: Container(
                height: 45.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.r),
                  color: Colors.white,
                  image: DecorationImage(
                    image: MemoryImage(state.$2 ?? Uint8List(0)),
                    fit: BoxFit.fill,
                  ),
                ),
              ),
            );
          case DataStatus.loading:
            return Center(
              child: Container(
                width: 30.h,
                height: 30.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5.r),
                  color: Colors.transparent,
                ),
                child: const CircularProgressIndicator.adaptive(),
              ),
            );
          case DataStatus.failed:
            return const Center(
              child: Text(
                'Failed to load captcha',
                style: TextStyle(color: Colors.red),
              ),
            );
          default:
            return SizedBox(
              width: 0.2.sw,
              height: 45.h,
            );
        }
      },
    );
  }

  Widget _buildRefreshButton() {
    return BlocSelector<SignInCubit, SignInState, DataStatus>(
      selector: (state) => state.captchaFetchStatus,
      builder: (context, state) {
        return CustomSquareSvgButton(
          borderRadiusUser: 8.r,
          width: 45.h,
          height: 45.h,
          svg: Assets.refresh,
          isLoading: state == DataStatus.loading || state == DataStatus.idle,
          onPressed: state == DataStatus.success || state == DataStatus.failed
              ? () {
                  _captchaController.clear();
                  context.read<SignInCubit>().loginCaptcha();
                }
              : null,
        );
      },
    );
  }

  Widget _buildTermsAndConditions() {
    return Row(
      children: [
        SizedBox(
          height: 24.h,
          width: 24.w,
          child: Checkbox(
            side: BorderSide(
              color: Theme.of(context).brightness == Brightness.dark
                  ? ColorPalette.cardColorDark
                  : Colors.black,
              width: 1.5,
            ),
            value: _termsAccepted,
            onChanged: (value) {
              setState(() {
                _termsAccepted = value ?? false;
              });
            },
            activeColor: myColorScheme(context).primaryColor,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ),
        8.horizontalSpace,
        Expanded(
          child: RichText(
            text: TextSpan(
              style: FontPalette.normal12.copyWith(
                color: myColorScheme(context).titleColor,
              ),
              children: [
                TextSpan(text: StringConstants.signinAgree.tr()),
                TextSpan(
                  text: StringConstants.termsAndConditions.tr(),
                  style: FontPalette.normal12.copyWith(
                    color: myColorScheme(context).primaryColor,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      Navigator.pushNamed(
                        context,
                        routeTermsAndConditionScreen,
                      );
                    },
                ),
                const TextSpan(text: ' & '),
                TextSpan(
                  text: StringConstants.privacyPolicy.tr(),
                  style: FontPalette.normal12.copyWith(
                    color: myColorScheme(context).primaryColor,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      Navigator.pushNamed(
                        context,
                        routePrivacyPolicyScreen,
                      );
                    },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginButton() {
    return MultiBlocListener(
      listeners: [
        BlocListener<SignInCubit, SignInState>(
          listenWhen: (previous, current) =>
              previous.loginFetchStatus != current.loginFetchStatus,
          listener: (context, state) {
            if (state.loginFetchStatus == DataStatus.success) {
              _handleLoginSuccess(state);
            } else if (state.loginFetchStatus == DataStatus.failed) {
              _handleLoginError();
              NetworkHelper.handleMessage(
                state.error,
                context,
                type: HandleTypes.customDialog,
                snackBarType: SnackBarType.error,
              );
            }
          },
        ),
        BlocListener<SignInCubit, SignInState>(
          listenWhen: (previous, current) =>
              previous.chatConfigFetchStatus != current.chatConfigFetchStatus,
          listener: (context, state) {
            if (state.chatConfigFetchStatus == DataStatus.success) {
              _handleUserChatConfigSuccess(state);
            }
          },
        ),
      ],
      child: BlocBuilder<SignInCubit, SignInState>(
        builder: (context, state) {
          return CustomButton(
            width: 299.w,
            height: 52.h,
            label: StringConstants.logIn.tr(),
            isEnabled: _termsAccepted,
            isOutlined: false,
            onPressed: _termsAccepted
                ? () => _handleLogin(k: state.captchaData?.data.k ?? '')
                : null,
            isLoading: state.loginFetchStatus == DataStatus.loading ||
                state.showLoading,
          );
        },
      ),
    );
  }

  Widget _buildSignUpText() {
    return Center(
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          style: FontPalette.normal12,
          children: [
            TextSpan(
              text: StringConstants.dontHaveAccount.tr(),
              style: FontPalette.normal12.copyWith(
                color: myColorScheme(context).secondaryVar1,
              ),
            ),
            TextSpan(
              text: StringConstants.signUp.tr(),
              style: FontPalette.bold12.copyWith(
                color: myColorScheme(context).primaryColor,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Navigator.pushReplacementNamed(
                    context,
                    routeRegistrationScreen,
                  );
                },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhoneField() {
    return Stack(
      children: [
        CommonTextField(
          autofillHints: const [AutofillHints.telephoneNumber],
          prefixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              BlocBuilder<CountryCodeCubit, CountryCodeState>(
                builder: (context, cState) {
                  return CustomCountryPicker(
                    onChanged: (PhoneCountry countryCode) => context
                        .read<SignInCubit>()
                        .setSelectedCountryCode(countryCode),
                    onInit: (countryCode) {
                      context
                          .read<SignInCubit>()
                          .setSelectedCountryCode(countryCode ??
                              const PhoneCountry(
                                phoneCode: '+52',
                                name: 'Mexico',
                                flagEmoji: '🇲🇽',
                                code: 'MX',
                              ));
                    },
                    initialSelectionCode: cState.countryCode?.isoCode ?? 'MX',
                    showCountryOnly: false,
                    showOnlyCountryWhenClosed: false,
                    alignLeft: false,
                    padding: EdgeInsets.zero,
                    textStyle: FontPalette.normal14,
                    showFlag: true,
                    showDropDownButton: true,
                    builder: (PhoneCountry? countryCode) {
                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            countryCode?.flagEmoji ?? '🇲🇽',
                            style: TextStyle(fontSize: 15.sp),
                          ),
                          4.horizontalSpace,
                          Text(
                            countryCode?.phoneCode ?? '+52',
                            style: FontPalette.normal14,
                          ),
                          Icon(
                            Icons.arrow_drop_down_sharp,
                            size: 24.w,
                            color: Colors.grey.shade600,
                          ),
                          Container(
                            width: 1.0.w,
                            height: 18.h,
                            color: Colors.grey.shade300,
                            margin: EdgeInsets.only(right: 10.w, left: 4.w),
                          ),
                        ],
                      );
                    },
                  );
                },
              ),
            ],
          ),
          textInputAction: TextInputAction.next,
          labelText: StringConstants.phoneNumber.tr(),
          hintText: StringConstants.enterPhoneNumber.tr(),
          controller: _phoneController,
          textInputType: TextInputType.phone,
          textInputFormatter: [
            FilteringTextInputFormatter.deny(
                RegExp(r'^\s+')), // Prevent leading spaces
          ],
          validator: (_) => validateMobile(_phoneController.text),
          onChanged: (_) {},
        ),
      ],
    );
  }
}
