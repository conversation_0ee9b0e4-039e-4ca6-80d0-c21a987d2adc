import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';

import '../../../core/constants/enums.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/routes/routes.dart';
import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';
import '../../../core/widgets/common_shimmer.dart';
import '../../smart_investment/domain/models/mentor/mentor_model.dart';
import '../../smart_investment/logic/smart_investment/smart_investment_cubit.dart';

class MentorSlider extends StatelessWidget {
  const MentorSlider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
      builder: (context, state) {
        if (state.mentorListFetchStatus == DataStatus.loading) {
          return _buildShimmerView(context);
        }

        if (state.mentorListData?.data?.list?.isEmpty ?? true) {
          return const SizedBox.shrink();
        }

        return Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  child: Text(
                    StringConstants.mentors.tr(),
                    style: FontPalette.bold16.copyWith(
                      color: myColorScheme(context).titleColor,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  child: TextButton(
                    onPressed: () => Navigator.pushNamed(context, routeMentorListScreen, arguments: {'showBackButton': true}),
                    child: Text(
                      StringConstants.viewAll.tr(),
                      style: FontPalette.medium10.copyWith(
                        color: myColorScheme(context).viewAllColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 190.h,
              width: 1.sh,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: (state.mentorListData?.data?.list?.length ?? 0) >= 5
                    ? 5
                    : state.mentorListData?.data?.list?.length ?? 0,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  final mentor = state.mentorListData?.data?.list?[index];
                  return Padding(
                    padding: const EdgeInsets.fromLTRB(12, 0, 0, 0).r,
                    child: _buildMentorCard(
                      mentor: mentor ?? const Mentor(),
                      onTap: () => context.handleSignedInAction(
                        skipAccountCheck: true,
                        onTap: () => Navigator.pushNamed(
                          context,
                          routeSmartInvestmentScreen,
                          arguments: {
                            'mentor': mentor ?? const Mentor(),
                          },
                        ),
                      ),
                      context: context,
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildShimmerView(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 30.w),
              child: Text(
                StringConstants.mentors.tr(),
                style: FontPalette.bold16.copyWith(
                  color: myColorScheme(context).titleColor,
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 30.w),
              child: TextButton(
                onPressed: () {},
                child: Text(
                  StringConstants.viewAll.tr(),
                  style: FontPalette.medium10.copyWith(
                    color: myColorScheme(context).viewAllColor,
                  ),
                ),
              ),
            ),
          ],
        ),
        Container(
          padding: EdgeInsets.only(left: 10.w),
          height: 190.h,
          width: 1.sh,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 3,
            shrinkWrap: true,
            itemBuilder: (context, index) => Padding(
              padding: const EdgeInsets.fromLTRB(6, 0, 6, 0).r,
              child: CommonShimmer(
                br: 20.r,
                width: 280.w,
                height: 190.h,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMentorCard({
    required Mentor mentor,
    required VoidCallback onTap,
    required BuildContext context,
  }) {
    return Container(
      width: 280.w,
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 50.w,
                height: 50.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: const Color.fromRGBO(213, 223, 253, 1),
                  image: DecorationImage(
                    image: NetworkImage(mentor.avatar ?? ''),
                    fit: BoxFit.cover,
                  ),
                ),
                child: mentor.avatar?.isNotEmpty ?? false
                    ? const SizedBox.shrink()
                    : Center(
                        child: Text(
                          mentor.name?.substring(0, 1).toUpperCase() ?? '',
                          style: FontPalette.medium18.copyWith(
                            color: myColorScheme(context).primaryColor,
                          ),
                        ),
                      ),
              ),
              12.horizontalSpace,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${mentor.name} ${mentor.nickname}',
                      style: FontPalette.medium14
                          .copyWith(color: myColorScheme(context).titleColor),
                    ),
                    4.verticalSpace,
                    Text(
                      mentor.position ?? '',
                      style: FontPalette.normal12.copyWith(
                        color: myColorScheme(context).greyColor4,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          8.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Expanded(
                child: _buildStat(
                  StringConstants.winRate.tr(),
                  '${mentor.winRate?.toStringAsFixed(1) ?? '0.0'}%',
                  context,
                ),
              ),
              Container(
                width: 1,
                height: 30.h,
                color: ColorPalette.greyColor2,
              ),
              Expanded(
                child: _buildStat(
                  StringConstants.monthly.tr(),
                  '${mentor.monthlyProfit?.toStringAsFixed(1) ?? '0.0'}%',
                  context,
                ),
              ),
              Container(
                width: 1,
                height: 30.h,
                color: ColorPalette.greyColor2,
              ),
              Expanded(
                child: _buildStat(
                  StringConstants.drawdown.tr(),
                  '${mentor.maxDrawdown?.toStringAsFixed(1) ?? '0.0'}%',
                  context,
                ),
              ),
            ],
          ),
          8.verticalSpace,
          Expanded(
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onTap,
                style: ElevatedButton.styleFrom(
                  backgroundColor: myColorScheme(context).primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                ),
                child: Text(
                  StringConstants.follow.tr(),
                  style: FontPalette.medium14.copyWith(color: Colors.white),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStat(String label, String value, BuildContext context) {
    return Column(
      children: [
        Text(
          value,
          style: FontPalette.medium14.copyWith(
            color: myColorScheme(context).primaryColor,
          ),
        ),
        4.verticalSpace,
        Text(
          label,
          style: FontPalette.normal12.copyWith(
            color: myColorScheme(context).greyColor4,
          ),
        ),
      ],
    );
  }
}
