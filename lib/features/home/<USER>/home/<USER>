import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/services/wallet_balance/wallet_balance_service.dart';
import 'package:sf_app_v2/features/home/<USER>/models/balance/balance_model.dart';
import 'package:sf_app_v2/features/home/<USER>/models/market/market_list_model.dart';
import 'package:sf_app_v2/features/home/<USER>/models/news/news_data_list.dart';
import 'package:sf_app_v2/features/home/<USER>/models/product/product_list_model.dart';
import 'package:sf_app_v2/features/home/<USER>/repository/home_repository.dart';

import '../../domain/models/carousel/carousel_list.dart';

part 'home_state.dart';

@injectable
class HomeCubit extends Cubit<HomeState> {
  final HomeRepository _homeService;

  HomeCubit(this._homeService) : super(const HomeState());

  Future<void> getBalance() async {
    emit(state.copyWith(balanceFetchStatus: DataStatus.loading));
    try {
      final result = await _homeService.balance();
      if (result.data != null) {
        WalletBalanceService().updateBalance(result.data!);
        emit(
          state.copyWith(
            balanceData: result.data,
            balanceFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            balanceFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          balanceFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> getProductList() async {
    emit(state.copyWith(productListFetchStatus: DataStatus.loading));
    try {
      final result = await _homeService.productList();
      if (result.data != null) {
        emit(
          state.copyWith(
            productListData: result.data,
            productListFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            productListFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          productListFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> getMarketList() async {
    emit(state.copyWith(marketListFetchStatus: DataStatus.loading));
    try {
      final result = await _homeService.marketList();
      if (result.data != null) {
        emit(
          state.copyWith(
            marketListData: result.data,
            marketListFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            marketListFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          marketListFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> getNewsUpdates({int page = 1, bool isLoadMore = false}) async {
    emit(state.copyWith(newsUpdatesFetchStatus: DataStatus.loading));
    if (isLoadMore) emit(state.copyWith(hasMoreData: true));

    try {
      final result = await _homeService.newsUpdates(pageNum: page);
      if (result.data != null) {
        if (isLoadMore) {
          emit(
            state.copyWith(
              newsUpdatesFetchStatus: DataStatus.success,
              newsDataList: NewsDataList(
                code: result.data?.code ?? 0,
                data: Data(
                  newsDataList: [
                    ...?state.newsDataList?.data.newsDataList,
                    ...?result.data?.data.newsDataList,
                  ],
                  pageNum: result.data?.data.pageNum ?? 0,
                  pageSize: result.data?.data.pageSize ?? 0,
                  total: result.data?.data.total ?? 0,
                ),
                msg: result.data?.msg ?? '',
              ),
              hasMoreData: false,
            ),
          );
        } else {
          emit(
            state.copyWith(
              newsUpdatesFetchStatus: DataStatus.success,
              newsDataList: NewsDataList(
                code: result.data?.code ?? 0,
                data: Data(
                  newsDataList: result.data?.data.newsDataList ?? [],
                  pageNum: result.data?.data.pageNum ?? 0,
                  pageSize: result.data?.data.pageSize ?? 0,
                  total: result.data?.data.total ?? 0,
                ),
                msg: result.data?.msg ?? '',
              ),
              hasMoreData: false,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            newsUpdatesFetchStatus: DataStatus.failed,
            error: result.error,
            hasMoreData: false,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          newsUpdatesFetchStatus: DataStatus.failed,
          error: e.toString(),
          hasMoreData: false,
        ),
      );
    }
  }

  Future<void> getOnlineService() async {
    emit(state.copyWith(onlineServiceFetchStatus: DataStatus.loading));
    try {
      final result = await _homeService.onlineService();
      if (result.data != null) {
        emit(
          state.copyWith(
            onlineService: result.data,
            onlineServiceFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            onlineServiceFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          onlineServiceFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  void clearBalanceData() {
    // Clear the balance data in the singleton BalanceManager
    WalletBalanceService().clearBalance();

    // Clear the balance data in the local state
    emit(state.copyWith(clearBalanceData: true));
  }

  Future<void> getCarouselList() async {
    emit(state.copyWith(carouselListFetchStatus: DataStatus.loading));
    try {
      final result = await _homeService.carouselList();
      if (result.data != null) {
        emit(
          state.copyWith(
            carouselListData: result.data,
            carouselListFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            carouselListFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(state.copyWith(
        carouselListFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }
}
