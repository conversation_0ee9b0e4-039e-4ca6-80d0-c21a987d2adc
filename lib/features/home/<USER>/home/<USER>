part of 'home_cubit.dart';

class HomeState extends Equatable {
  final BalanceModel? balanceData;
  final ProductListModel? productListData;
  final MarketListModel? marketListData;
  final NewsDataList? newsDataList;
  final String? onlineService;
  final List<CarouselData>? carouselListData;
  final DataStatus balanceFetchStatus;
  final DataStatus productListFetchStatus;
  final DataStatus marketListFetchStatus;
  final DataStatus newsUpdatesFetchStatus;
  final DataStatus onlineServiceFetchStatus;
  final DataStatus carouselListFetchStatus;
  final String? error;
  final bool hasMoreData;

  const HomeState({
    this.balanceData,
    this.productListData,
    this.marketListData,
    this.newsDataList,
    this.onlineService,
    this.carouselListData,
    this.balanceFetchStatus = DataStatus.idle,
    this.productListFetchStatus = DataStatus.idle,
    this.marketListFetchStatus = DataStatus.idle,
    this.newsUpdatesFetchStatus = DataStatus.idle,
    this.onlineServiceFetchStatus = DataStatus.idle,
    this.carouselListFetchStatus = DataStatus.idle,
    this.error,
    this.hasMoreData = false,
  });

  @override
  List<Object?> get props => [
        balanceData,
        productListData,
        marketListData,
        newsDataList,
        onlineService,
        carouselListData,
        balanceFetchStatus,
        productListFetchStatus,
        marketListFetchStatus,
        newsUpdatesFetchStatus,
        onlineServiceFetchStatus,
        carouselListFetchStatus,
        error,
        hasMoreData,
      ];

  HomeState copyWith({
    BalanceModel? balanceData,
    ProductListModel? productListData,
    MarketListModel? marketListData,
    NewsDataList? newsDataList,
    String? onlineService,
    List<CarouselData>? carouselListData,
    DataStatus? balanceFetchStatus,
    DataStatus? productListFetchStatus,
    DataStatus? marketListFetchStatus,
    DataStatus? newsUpdatesFetchStatus,
    DataStatus? onlineServiceFetchStatus,
    DataStatus? carouselListFetchStatus,
    String? error,
    int? pageNumber,
    bool? hasMoreData,
    bool clearBalanceData = false,
  }) {
    return HomeState(
      balanceData: clearBalanceData ? null : balanceData ?? this.balanceData,
      productListData: productListData ?? this.productListData,
      marketListData: marketListData ?? this.marketListData,
      newsDataList: newsDataList ?? this.newsDataList,
      onlineService: onlineService ?? this.onlineService,
      carouselListData: carouselListData ?? this.carouselListData,
      balanceFetchStatus: balanceFetchStatus ?? this.balanceFetchStatus,
      productListFetchStatus: productListFetchStatus ?? this.productListFetchStatus,
      marketListFetchStatus: marketListFetchStatus ?? this.marketListFetchStatus,
      newsUpdatesFetchStatus: newsUpdatesFetchStatus ?? this.newsUpdatesFetchStatus,
      onlineServiceFetchStatus: onlineServiceFetchStatus ?? this.onlineServiceFetchStatus,
      carouselListFetchStatus: carouselListFetchStatus ?? this.carouselListFetchStatus,
      error: error ?? this.error,
      hasMoreData: hasMoreData ?? this.hasMoreData,
    );
  }
}
