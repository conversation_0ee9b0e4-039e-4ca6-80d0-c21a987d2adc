import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/constants/keys.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/api/network/route_arguments/product_details_argument.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/utils/convert_helper.dart';

import '../../../core/constants/assets.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/widgets/common_icon_button.dart';

class ContractCard extends StatelessWidget {
  final bool? isLoading;
  final double width;
  final double height;
  final String contractTitle;
  final int contractId;
  final String contractType;
  final double contractPrice;
  final String contractSize;
  final double returnRate;

  const ContractCard({
    super.key,
    required this.width,
    this.isLoading,
    required this.height,
    required this.contractId,
    required this.contractTitle,
    required this.contractType,
    required this.contractPrice,
    required this.contractSize,
    required this.returnRate,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: Stack(
        children: [
          SizedBox(
            width: width,
            height: (height - 40.h),
            child: Card(
              shadowColor: ColorPalette.shadowColor.withValues(alpha: 0.5),
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(18.0).r,
              ),
              color: Colors.white,
              child: Padding(
                padding: const EdgeInsets.all(16).r,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          contractTitle,
                          style: FontPalette.bold14
                              .copyWith(color: ColorPalette.primaryVar1),
                          textAlign: TextAlign.start,
                        ),
                        10.verticalSpace,
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    SizedBox(
                                      width: 75.w,
                                      child: Text(
                                        '${StringConstants.contractType.tr()}:',
                                        style: FontPalette.normal10,
                                        textAlign: TextAlign.start,
                                      ),
                                    ),
                                    10.verticalSpace,
                                    Text(
                                      (isLoading ?? false)
                                          ? ''
                                          : contractTypes[contractType]!,
                                      style: FontPalette.medium10,
                                      textAlign: TextAlign.start,
                                    ),
                                  ],
                                ),
                                6.verticalSpace,
                                Row(
                                  children: [
                                    SizedBox(
                                      width: 75.w,
                                      child: Text(
                                        '${StringConstants.contractPrice.tr()}:',
                                        style: FontPalette.normal10,
                                        textAlign: TextAlign.start,
                                      ),
                                    ),
                                    10.verticalSpace,
                                    Text(
                                      ConvertHelper.formatPriceUsd(
                                        contractPrice,
                                      ),
                                      style: FontPalette.medium10,
                                      textAlign: TextAlign.start,
                                    ),
                                  ],
                                ),
                                6.verticalSpace,
                                Row(
                                  children: [
                                    SizedBox(
                                      width: 75.w,
                                      child: Text(
                                        '${StringConstants.contractSize.tr()}:',
                                        style: FontPalette.normal10,
                                        textAlign: TextAlign.start,
                                      ),
                                    ),
                                    10.verticalSpace,
                                    Text(
                                      contractSize,
                                      style: FontPalette.medium10,
                                      textAlign: TextAlign.start,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                        10.verticalSpace,
                        Text(
                          '$returnRate%',
                          style: FontPalette.semiBold30,
                          textAlign: TextAlign.start,
                        ),
                        Text(
                          StringConstants.returnRate.tr(),
                          style: FontPalette.normal10,
                          textAlign: TextAlign.start,
                        ),
                      ],
                    ),
                    SvgPicture.asset(Assets.chart),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 1,
            right: 1,
            child: Column(
              children: [
                CustomCircularIconButton(
                  width: 50.h / 1.w,
                  icon: Icons.add,
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      routeContractDetailScreen,
                      arguments: ProductDetailsDetailsArguments(
                        productId: contractId,
                        title: contractTitle,
                      ),
                    );
                  },
                ),
                5.verticalSpace,
                Text(
                  StringConstants.buyIt.tr(),
                  style: FontPalette.semiBold10,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ContractCard2 extends StatelessWidget {
  final bool? isLoading;
  final double width;
  final double height;
  final String contractTitle;
  final int contractId;
  final String contractType;
  final double contractPrice;
  final String contractSize;
  final double returnRate;

  const ContractCard2({
    super.key,
    required this.width,
    this.isLoading,
    required this.height,
    required this.contractId,
    required this.contractTitle,
    required this.contractType,
    required this.contractPrice,
    required this.contractSize,
    required this.returnRate,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: Stack(
        children: [
          SizedBox(
            width: width,
            height: (height - 40.h),
            child: Card(
              shadowColor: ColorPalette.shadowColor.withValues(alpha: 0.5),
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(18.0).r,
              ),
              color: Colors.white,
              child: Padding(
                padding: const EdgeInsets.all(16).r,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      contractTitle,
                      overflow: TextOverflow.fade,
                      maxLines: 1,
                      softWrap: false,
                      style: FontPalette.bold14
                          .copyWith(color: ColorPalette.primaryVar1),
                      textAlign: TextAlign.start,
                    ),
                    10.verticalSpace,
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                SizedBox(
                                  width: 75.w,
                                  child: Text(
                                    '${StringConstants.contractType.tr()}:',
                                    style: FontPalette.normal10,
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                                10.verticalSpace,
                                Text(
                                  (isLoading ?? false)
                                      ? ''
                                      : contractTypes[contractType]!,
                                  style: FontPalette.medium10,
                                  textAlign: TextAlign.start,
                                ),
                              ],
                            ),
                            6.verticalSpace,
                            Row(
                              children: [
                                SizedBox(
                                  width: 75.w,
                                  child: Text(
                                    '${StringConstants.contractPrice.tr()}:',
                                    style: FontPalette.normal10,
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                                10.verticalSpace,
                                Text(
                                  ConvertHelper.formatPriceUsd(contractPrice),
                                  style: FontPalette.medium10,
                                  textAlign: TextAlign.start,
                                ),
                              ],
                            ),
                            6.verticalSpace,
                            Row(
                              children: [
                                SizedBox(
                                  width: 75.w,
                                  child: Text(
                                    '${StringConstants.contractSize.tr()}:',
                                    style: FontPalette.normal10,
                                    textAlign: TextAlign.start,
                                  ),
                                ),
                                10.verticalSpace,
                                Text(
                                  contractSize.toCurrency(),
                                  style: FontPalette.medium10,
                                  textAlign: TextAlign.start,
                                ),
                              ],
                            ),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '$returnRate%',
                              style: FontPalette.semiBold30,
                              textAlign: TextAlign.start,
                            ),
                            Text(
                              StringConstants.returnRate.tr(),
                              style: FontPalette.normal10,
                              textAlign: TextAlign.start,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 1,
            right: 1,
            child: Column(
              children: [
                CustomCircularIconButton(
                  width: 50.h / 1.w,
                  icon: Icons.add,
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      routeContractDetailScreen,
                      arguments: ProductDetailsDetailsArguments(
                        productId: contractId,
                        title: contractTitle,
                      ),
                    );
                  },
                ),
                5.verticalSpace,
                Text(
                  StringConstants.buyIt.tr(),
                  style: FontPalette.semiBold10,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
