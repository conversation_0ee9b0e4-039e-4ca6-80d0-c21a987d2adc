import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/constants/enums.dart';

import '../../../core/constants/string_constants.dart';
import '../../../core/routes/routes.dart';
import '../../../core/theme/color_pallette.dart';
import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';
import '../../../core/widgets/common_shimmer.dart';
import '../domain/models/product/product_list_model.dart';
import '../logic/home/<USER>';
import 'contract_cards.dart';

class ContractSlider extends StatelessWidget {
  const ContractSlider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<HomeCubit, HomeState, (DataStatus, ProductListModel?)>(
      selector: (state) =>
          (state.productListFetchStatus, state.productListData),
      builder: (context, data) {
        switch (data.$1) {
          case DataStatus.success:
            return Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 30.w,
                      ),
                      child: Text(
                        StringConstants.contracts.tr(),
                        style: FontPalette.bold16
                            .copyWith(color: ColorPalette.titleColor),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 30.w,
                      ),
                      child: TextButton(
                        child: Text(
                          StringConstants.viewAll.tr(),
                          style: FontPalette.medium10.copyWith(
                            color: myColorScheme(context).viewAllColor,
                          ),
                        ),
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            routeContractListScreen,
                          );
                        },
                      ),
                    ),
                  ],
                ),
                SizedBox(
                  height: 190.h,
                  width: 1.sh,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount:
                        data.$2!.data.length >= 5 ? 5 : data.$2!.data.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      ProductListData product = data.$2!.data[index];
                      if (product.purchasable == false) {
                        return const SizedBox.shrink();
                      }
                      return Padding(
                        padding: const EdgeInsets.fromLTRB(12, 0, 0, 0).r,
                        child: ContractCard2(
                          width: 370.w,
                          height: 200.h,
                          contractId: product.id ?? 0,
                          contractTitle: product.name ?? '',
                          contractType: product.type.toString(),
                          contractPrice: product.singleAmount ?? 0.0,
                          contractSize: product.purchasableAmount.toString(),
                          returnRate: product.annualisedReturns ?? 0.0,
                        ),
                      );
                    },
                  ),
                ),
              ],
            );
          case DataStatus.loading:
            return Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 30.w,
                      ),
                      child: Text(
                        StringConstants.contracts.tr(),
                        style: FontPalette.bold16
                            .copyWith(color: ColorPalette.titleColor),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: 30.w,
                      ),
                      child: TextButton(
                        child: Text(
                          StringConstants.viewAll.tr(),
                          style: FontPalette.semiBold10.copyWith(
                            color: myColorScheme(context).viewAllColor,
                          ),
                        ),
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            routeContractListScreen,
                          );
                        },
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: EdgeInsets.only(left: 10.w),
                  height: 240.h,
                  width: 1.sh,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: 3,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Column(
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.fromLTRB(6, 0, 6, 0).r,
                                child: CommonShimmer(
                                  br: 20.r,
                                  width: 370.w,
                                  height: 220.h,
                                  color: ColorPalette.primaryColor
                                      .withValues(alpha: 0.1),
                                ),
                              ),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ],
            );
          default:
            return SizedBox(
              width: 0.2.sw,
              height: 45.h,
            );
        }
      },
    );
  }
}
