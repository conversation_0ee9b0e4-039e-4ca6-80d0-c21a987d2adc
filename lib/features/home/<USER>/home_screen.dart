import 'package:carousel_slider/carousel_slider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:sf_app_v2/core/common_function.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/shared/logic/app_data/app_data_cubit.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/utils/shared_preference_helper.dart';
import 'package:sf_app_v2/core/widgets/common_image_carousel.dart';
import 'package:sf_app_v2/core/widgets/common_user_name_header.dart';
import 'package:sf_app_v2/core/widgets/pagination_widget.dart';
import 'package:sf_app_v2/core/widgets/modern_card.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/account_info/account_info_cubit.dart';
import 'package:sf_app_v2/features/community/logic/community/community_cubit.dart';
import 'package:sf_app_v2/features/home/<USER>/models/carousel/carousel_list.dart';
import 'package:sf_app_v2/features/home/<USER>/home/<USER>';
import 'package:sf_app_v2/features/home/<USER>/news_list.dart';
import 'package:sf_app_v2/features/market_v2/logic/market/market_cubit.dart';
import 'package:sf_app_v2/features/market_v2/widgets/visual_graph_section.dart';
import 'package:sf_app_v2/features/profile/logic/profile/profile_cubit.dart';
import 'package:sf_app_v2/features/smart_investment/logic/smart_investment/smart_investment_cubit.dart';

import '../../../core/utils/mixin/animation.dart';
import '../widgets/modern_balance_card.dart';
import '../widgets/modern_quick_actions.dart';

class HomeScreen extends StatefulWidget {
  final ScrollController scrollController;
  final VoidCallback? onNavigateToMarket;

  const HomeScreen({
    super.key,
    required this.scrollController,
    this.onNavigateToMarket,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with StaggeredAnimation {
  @override
  void initState() {
    super.initState();
    CommonFunctions.afterInit(_fetchInitialData);
  }

  Future<void> _fetchInitialData() async {
    final homeCubit = context.read<HomeCubit>();
    bool isSignedIn = SharedPreferenceHelper().getIsLoggedIn() ?? false;
    // bool isAccountStatusLogged = SharedPreferenceHelper().isAccountStatusLogged() ?? false;
    if (isSignedIn) {
      context.read<AccountInfoCubit>().getStatus();
      context.read<ProfileCubit>().getProfileInfo();
      homeCubit.getBalance();
      context.read<CommunityCubit>()
        ..getCommunityList()
        ..getCarouselList()
        ..getAgentCommissionStats();
    }
    homeCubit.getNewsUpdates();
    context.read<SmartInvestmentCubit>().getMentorList();
    context.read<AppDataCubit>().getUserData();
    context.read<HomeCubit>()
      ..getNewsUpdates()
      ..getCarouselList();
    context.read<MarketCubit>().init();
  }

  Future<bool> _checkSignedIn() async {
    bool isSignedIn = SharedPreferenceHelper().getIsLoggedIn() ?? false;
    return isSignedIn;
  }

  void _navigateToSupport() {
    Navigator.pushNamed(context, routeCustomerSupportScreen);
  }

  void _navigateToNotifications() {
    Navigator.pushNamed(context, routeNotificationListScreen);
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _fetchInitialData,
      child: Scaffold(
        appBar: _ModernAppBar(),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                myColorScheme(context).backgroundColor ?? ColorPalette.backgroundColor,
                myColorScheme(context).backgroundColor1 ?? ColorPalette.backgroundColor1,
              ],
            ),
          ),
          child: SafeArea(
            child: BlocBuilder<HomeCubit, HomeState>(
              builder: (context, state) {
                return FutureBuilder(
                  future: _checkSignedIn(),
                  builder: (context, snapshot) {
                    final isSignedIn = snapshot.data ?? false;

                    return AnimationLimiter(
                      child: PaginationWidget(
                        isPaginating:
                            state.newsUpdatesFetchStatus == DataStatus.loading &&
                                state.hasMoreData,
                        next:
                            (state.newsDataList?.data.newsDataList.length ?? 0) <=
                                (state.newsDataList?.data.total ?? 0),
                        onPagination: (notification) {
                          if (state.newsDataList?.data.newsDataList.length ==
                              (state.newsDataList?.data.total ?? 0)) {
                            return false;
                          }
                          context.read<HomeCubit>().getNewsUpdates(
                                page: (state.newsDataList?.data.pageNum ?? 0) + 1,
                                isLoadMore: true,
                              );
                          return true;
                        },
                        child: ListView(
                          physics: const BouncingScrollPhysics(
                            parent: AlwaysScrollableScrollPhysics(),
                          ),
                          controller: widget.scrollController,
                          children: staggeredAnimation(
                            children: [
                              // Modern User Header
                              _ModernUserHeader(
                                onSupport: _navigateToSupport,
                                onNotifications: _navigateToNotifications,
                              ),
                              SizedBox(height: 20.h),

                              // Balance Card (only for signed in users)
                              if (isSignedIn) ...[
                                const ModernBalanceCard(),
                                SizedBox(height: 20.h),
                              ],

                              // Quick Actions (only for signed in users)
                              if (isSignedIn) ...[
                                const ModernQuickActions(),
                                SizedBox(height: 20.h),
                              ],

                              // Enhanced Carousel Section
                              const _ModernCarouselSection(),
                              SizedBox(height: 24.h),

                              // Enhanced Market Section
                              _ModernMarketSection(onNavigateToMarket: widget.onNavigateToMarket),
                              SizedBox(height: 24.h),

                              // Enhanced News Section
                              const NewsList(),

                              // Add bottom padding for floating navigation
                              SizedBox(height: 120.h),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class _ModernAppBar extends StatelessWidget implements PreferredSizeWidget {
  @override
  Size get preferredSize => const Size.fromHeight(0);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return AppBar(
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
    );
  }
}

class _ModernUserHeader extends StatelessWidget {
  final VoidCallback onSupport;
  final VoidCallback onNotifications;

  const _ModernUserHeader({
    required this.onSupport,
    required this.onNotifications,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0),
      child: ModernCard(
        style: ModernCardStyle.glass,
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
        child: BlocBuilder<AppDataCubit, AppDataState>(
          builder: (context, state) {
            String id =
                context.read<ProfileCubit>().state.infoData?.userId.toString() ??
                    '';
            return CommonUserNameHeader(
              userId: id,
              userName: (state.userData?.email?.isNotEmpty == true
                      ? state.userData?.email
                      : state.userData?.username) ??
                  StringConstants.user.tr(),
              icon1: CommonFunctions.getIconFromLevel(state.userData?.level ?? 0),
              onPressedIcon1: null,
              onSupport: onSupport,
              onNotifications: onNotifications,
            );
          },
        ),
      ),
    );
  }
}

class _ModernMarketSection extends StatelessWidget {
  final VoidCallback? onNavigateToMarket;

  const _ModernMarketSection({this.onNavigateToMarket});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: ModernCard(
        style: ModernCardStyle.neumorphism,
        padding: EdgeInsets.all(24.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      StringConstants.marketOverview.tr(),
                      style: FontPalette.titleLarge.copyWith(
                        color: myColorScheme(context).titleColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'Real-time market data',
                      style: FontPalette.labelMedium.copyWith(
                        color: myColorScheme(context).subTitleColor,
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        ColorPalette.primaryColor,
                        ColorPalette.accentColor,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: GestureDetector(
                    onTap: onNavigateToMarket,
                    child: Text(
                      StringConstants.viewAll.tr(),
                      style: FontPalette.labelMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            const VisualGraphSection(),
          ],
        ),
      ),
    );
  }
}

class _ModernCarouselSection extends StatelessWidget {
  const _ModernCarouselSection();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: ModernCard(
        style: ModernCardStyle.glass,
        padding: EdgeInsets.all(12.r),
        borderRadius: 24,
        child: BlocBuilder<HomeCubit, HomeState>(
          builder: (context, state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(8.r),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              ColorPalette.primaryColor.withValues(alpha: 0.2),
                              ColorPalette.accentColor.withValues(alpha: 0.2),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Icon(
                          Icons.campaign_outlined,
                          color: ColorPalette.primaryColor,
                          size: 20.r,
                        ),
                      ),
                      SizedBox(width: 12.w),
                      Text(
                        'Featured Announcements',
                        style: FontPalette.titleMedium.copyWith(
                          color: myColorScheme(context).titleColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 8.h),
                CommonImageCarousel<CarouselData>(
                  items: state.carouselListData,
                  loadingStatus: state.carouselListFetchStatus,
                  imageUrlExtractor: (item) => item.imgUrl,
                  onItemTap: (item) {
                    if (item.linkUrl.isNotEmpty) {
                      CommonFunctions.launchUrls(item.linkUrl);
                    }
                  },
                  itemHeight: 200.h,
                  shimmerHeight: 200.h,
                  carouselOptions: CarouselOptions(
                    viewportFraction: 1,
                    enableInfiniteScroll: true,
                    autoPlay: true,
                    autoPlayInterval: const Duration(seconds: 5),
                    autoPlayAnimationDuration: const Duration(milliseconds: 1000),
                    autoPlayCurve: Curves.easeInOutCubic,
                    enlargeCenterPage: false,
                  ),
                  activeIndicatorColor: ColorPalette.primaryColor,
                  inactiveIndicatorColor: myColorScheme(context).greyColor3?.withValues(alpha: 0.5),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
