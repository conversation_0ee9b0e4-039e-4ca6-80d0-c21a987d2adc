import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/features/home/<USER>/models/product/product_list_model.dart';
import 'package:sf_app_v2/features/home/<USER>/home/<USER>';
import 'package:easy_localization/easy_localization.dart';

import '../../../core/constants/string_constants.dart';
import '../../../core/widgets/common_appbar.dart';
import '../../../core/widgets/common_shimmer.dart';

class ContractListScreen extends StatelessWidget {
  final bool showBackButton;

  const ContractListScreen({
    super.key,
    this.showBackButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(
            parent: AlwaysScrollableScrollPhysics(),
          ),
          slivers: [
            _buildAppBar(context),
            _buildContractList(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return CommonSliverAppBar(
      buildContext: context,
      enableNavBack: showBackButton,
      titleWidget: Text(
        StringConstants.contractList.tr(),
        style: FontPalette.semiBold20.copyWith(
          color: ColorPalette.titleColor,
        ),
      ),
      centerTitleText: true,
    );
  }

  Widget _buildContractList() {
    return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: 26.w),
      sliver: SliverFillRemaining(
        child: Padding(
          padding: EdgeInsets.only(bottom: 26.h),
          child: BlocSelector<HomeCubit, HomeState,
              (DataStatus, List<ProductListData>?)>(
            selector: (state) => (
              state.productListFetchStatus,
              state.productListData?.data,
            ),
            builder: (context, data) {
              return _buildContent(context, data);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    (DataStatus, List<ProductListData>?) data,
  ) {
    switch (data.$1) {
      case DataStatus.success:
        return _buildSuccessContent(context, data.$2!);
      case DataStatus.loading:
        return _buildLoadingContent();
      default:
        return SizedBox(width: 0.2.sw, height: 45.h);
    }
  }

  Widget _buildSuccessContent(
    BuildContext context,
    List<ProductListData> products,
  ) {
    return AnimationLimiter(
      child: RefreshIndicator(
        onRefresh: () => context.read<HomeCubit>().getProductList(),
        child: ListView.builder(
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: products.length,
          shrinkWrap: true,
          itemBuilder: (context, index) =>
              _buildProductItem(products[index], index),
        ),
      ),
    );
  }

  Widget _buildProductItem(ProductListData product, int index) {
    return AnimationConfiguration.staggeredList(
      position: index,
      duration: const Duration(milliseconds: 500),
      child: SlideAnimation(
        verticalOffset: index * 50.0,
        child: FadeInAnimation(
          child: Padding(
            padding: EdgeInsets.only(bottom: 16.h),
            // child: ContractListCard(
            //   productId: product.id,
            //   title: product.name,
            //   type: types[product.type.toString()].toString(),
            //   size: '${product.purchasableAmount}'.toUSD(),
            //   price: '${product.singleAmount ?? 0.0}'.toUSD(),
            //   returnRate: '${product.annualisedReturns}%',
            //   purchasable: product.purchasable,
            //   retracementRate: product.maximumRetracement.toString(),
            // ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingContent() {
    return ListView.builder(
      scrollDirection: Axis.vertical,
      itemCount: 3,
      shrinkWrap: true,
      itemBuilder: (_, index) => Padding(
        padding: EdgeInsets.only(bottom: 26.h),
        child: CommonShimmer(
          width: 377.w,
          height: 205.h,
          br: 15.r,
        ),
      ),
    );
  }
}
