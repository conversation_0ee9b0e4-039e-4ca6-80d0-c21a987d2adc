// To parse this JSON data, do
//
//     final carouselList = carouselListFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'carousel_list.freezed.dart';
part 'carousel_list.g.dart';

CarouselList carouselListFromJson(str) => CarouselList.fromJson(str);

String carouselListToJson(CarouselList data) => json.encode(data.toJson());

@freezed
class CarouselList with _$CarouselList {
    const factory CarouselList({
        @Json<PERSON>ey(name: "code")
        required int code,
        @Json<PERSON>ey(name: "data")
        required List<CarouselData> data,
        @Json<PERSON>ey(name: "msg")
        required String msg,
    }) = _CarouselList;

    factory CarouselList.fromJson(Map<String, dynamic> json) => _$CarouselListFromJson(json);
}

@freezed
class CarouselData with _$CarouselData {
    const factory CarouselData({
        @Json<PERSON><PERSON>(name: "imgUrl")
        required String imgUrl,
        @<PERSON><PERSON><PERSON><PERSON>(name: "linkUrl")
        required String linkUrl,
        @JsonKey(name: "sort")
        required int sort,
    }) = _CarouselData;

    factory CarouselData.fromJson(Map<String, dynamic> json) => _$CarouselDataFromJson(json);
}
