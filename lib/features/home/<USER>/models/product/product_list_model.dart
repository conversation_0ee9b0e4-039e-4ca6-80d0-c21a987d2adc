import 'package:freezed_annotation/freezed_annotation.dart';

part 'product_list_model.freezed.dart';
part 'product_list_model.g.dart';

@freezed
class ProductListModel with _$ProductListModel {
  const factory ProductListModel({
    required int code,
    required List<ProductListData> data,
    required String msg,
  }) = _ProductListModel;

  factory ProductListModel.fromJson(Map<String, dynamic> json) =>
      _$ProductListModelFromJson(json);
}

@freezed
class ProductListData with _$ProductListData {
  const factory ProductListData({
    double? annualisedReturns,
    int? id,
    double? maximumRetracement,
    String? name,
    bool? purchasable,
    double? purchasableAmount,
    double? singleAmount,
    int? type,
  }) = _ProductListData;

  factory ProductListData.fromJson(Map<String, dynamic> json) =>
      _$ProductListDataFromJson(json);
}
