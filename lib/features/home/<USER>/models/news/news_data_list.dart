import 'package:freezed_annotation/freezed_annotation.dart';

part 'news_data_list.freezed.dart';
part 'news_data_list.g.dart';

@freezed
class NewsDataList with _$NewsDataList {
  const factory NewsDataList({
    required int code,
    required Data data,
    required String msg,
  }) = _NewsDataList;

  factory NewsDataList.fromJson(Map<String, dynamic> json) =>
      _$NewsDataListFromJson(json);
}

@freezed
class Data with _$Data {
  const factory Data({
    @JsonKey(name: "list") required List<NewsData> newsDataList,
    required int pageNum,
    required int pageSize,
    required int total,
  }) = _Data;

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);
}

@freezed
class NewsData with _$NewsData {
  const factory NewsData({
    String? coverUrl,
    DateTime? createTime,
    int? id,
    String? imgUrl,
    dynamic modifiedTime,
    String? remark,
    int? status,
    String? title,
    int? type,
  }) = _NewsData;

  factory NewsData.fromJson(Map<String, dynamic> json) =>
      _$<PERSON>(json);
}
