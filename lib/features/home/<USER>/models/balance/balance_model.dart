import 'package:freezed_annotation/freezed_annotation.dart';

part 'balance_model.freezed.dart';
part 'balance_model.g.dart';

@freezed
class BalanceModel with _$BalanceModel {
  const factory BalanceModel({
    required int code,
    required BalanceData data,
    required String msg,
  }) = _BalanceModel;

  factory BalanceModel.fromJson(Map<String, dynamic> json) =>
      _$BalanceModelFromJson(json);
}

@freezed
class BalanceData with _$BalanceData {
  const factory BalanceData({
    @JsonKey(name: 'cash') String? cash,
    CommunityBalance? community,
  }) = _BalanceData;

  factory BalanceData.fromJson(Map<String, dynamic> json) =>
      _$BalanceDataFromJson(json);
}

@freezed
class CommunityBalance with _$CommunityBalance {
  const factory CommunityBalance({
    @JsonKey(name: 'availableBalance') double? availableBalance,
    @JsonKey(name: 'lockedBalance') double? lockedBalance, 
    @Json<PERSON><PERSON>(name: 'totalAmount') double? totalAmount,
  }) = _CommunityBalance;

  factory CommunityBalance.fromJson(Map<String, dynamic> json) =>
      _$CommunityBalanceFromJson(json);
}
