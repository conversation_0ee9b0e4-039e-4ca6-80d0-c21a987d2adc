import 'package:freezed_annotation/freezed_annotation.dart';

part 'market_list_model.freezed.dart';
part 'market_list_model.g.dart';

@freezed
class MarketListModel with _$MarketListModel {
  const factory MarketListModel({
    required int code,
    required List<MarketListData> data,
    required String msg,
  }) = _MarketListModel;

  factory MarketListModel.fromJson(Map<String, dynamic> json) =>
      _$MarketListModelFromJson(json);
}

@freezed
class MarketListData with _$MarketListData {
  const factory MarketListData({
    required String openPrice,
    required String priceChangePercent,
    required String symbol,
    bool? direction, // Nullable field
  }) = _MarketListData;

  factory MarketListData.fromJson(Map<String, dynamic> json) =>
      _$MarketListDataFromJson(json);
}
