import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/api/endpoint/api_endpoints.dart';
import 'package:sf_app_v2/core/api/network/network.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/home/<USER>/models/balance/balance_model.dart';
import 'package:sf_app_v2/features/home/<USER>/models/market/market_list_model.dart';
import 'package:sf_app_v2/features/home/<USER>/models/news/news_data_list.dart';
import 'package:sf_app_v2/features/home/<USER>/models/product/product_list_model.dart';
import 'package:sf_app_v2/features/home/<USER>/repository/home_repository.dart';

import '../models/carousel/carousel_list.dart';

/// Service class that implements the HomeRepository interface to handle home-related API calls
@Injectable(as: HomeRepository)
class HomeService implements HomeRepository {
  /// Fetches user's balance information
  /// Returns a [ResponseResult] containing [BalanceModel] on success
  @override
  Future<ResponseResult<BalanceModel>> balance() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.balance,
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: BalanceModel.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get balance');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches list of available products
  /// Returns a [ResponseResult] containing [ProductListModel] on success
  @override
  Future<ResponseResult<ProductListModel>> productList() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.productList,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: ProductListModel.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get product list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches market data list
  /// Returns a [ResponseResult] containing [MarketListModel] on success
  @override
  Future<ResponseResult<MarketListModel>> marketList() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.marketList,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: MarketListModel.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get market list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches paginated news updates
  /// [pageNum] - Page number for pagination
  /// Returns a [ResponseResult] containing [NewsDataList] on success
  @override
  Future<ResponseResult<NewsDataList>> newsUpdates({
    required int pageNum,
  }) async {
    try {
      final Response response = await NetworkProvider().get(ApiEndpoints.newsUpdates(pageNum),);
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: NewsDataList.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get news updates');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches online service information
  /// Returns a [ResponseResult] containing String on success
  @override
  Future<ResponseResult<String>> onlineService() async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.onlineService,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: response.data['data']);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get online service');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches carousel data for home screen
  /// Returns a [ResponseResult] containing List of [CarouselData] on success
  @override
  Future<ResponseResult<List<CarouselData>>> carouselList() async {
    try {
      final Response response = await NetworkProvider().get(ApiEndpoints.carouselHomeList);
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: CarouselList.fromJson(response.data).data);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get carousel list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
