import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/home/<USER>/models/balance/balance_model.dart';
import 'package:sf_app_v2/features/home/<USER>/models/market/market_list_model.dart';
import 'package:sf_app_v2/features/home/<USER>/models/news/news_data_list.dart';
import 'package:sf_app_v2/features/home/<USER>/models/product/product_list_model.dart';

import '../models/carousel/carousel_list.dart';

abstract class HomeRepository {
  const HomeRepository();

  Future<ResponseResult<BalanceModel>> balance();

  Future<ResponseResult<ProductListModel>> productList();

  Future<ResponseResult<MarketListModel>> marketList();

  Future<ResponseResult<NewsDataList>> newsUpdates({
    required int pageNum,
  });

  Future<ResponseResult<String>> onlineService();

  Future<ResponseResult<List<CarouselData>>> carouselList();
}
