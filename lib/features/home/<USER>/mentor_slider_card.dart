import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

import '../../../core/constants/string_constants.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../core/routes/routes.dart';
import '../../../core/theme/color_pallette.dart';
import '../../../core/theme/font_pallette.dart';
import '../../smart_investment/domain/models/mentor/mentor_model.dart';

class MentorSliderCard extends StatelessWidget {
  const MentorSliderCard({
    super.key,
    required this.mentor,
  });

  final Mentor mentor;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 310.w,
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 50.w,
                height: 50.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: const Color.fromRGBO(213, 223, 253, 1),
                  image: DecorationImage(
                    image: NetworkImage(mentor.avatar ?? ''),
                    fit: BoxFit.cover,
                  ),
                ),
                child: mentor.avatar?.isNotEmpty ?? false
                    ? const SizedBox.shrink()
                    : Center(
                        child: Text(
                          (mentor.name ?? '').substring(0, 1).toUpperCase(),
                          style: FontPalette.medium18.copyWith(
                            color: ColorPalette.primaryColor,
                          ),
                        ),
                      ),
              ),
              12.horizontalSpace,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${mentor.name} ${mentor.nickname}',
                      style: FontPalette.medium14.copyWith(color: myColorScheme(context).titleColor),
                    ),
                    4.verticalSpace,
                    Text(
                      mentor.position ?? '',
                      style: FontPalette.normal12.copyWith(
                        color: ColorPalette.greyColor4,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          8.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Expanded(
                child: _buildStat(
                  StringConstants.drawdown.tr(),
                  '${(mentor.maxDrawdown ?? 0).toStringAsFixed(1)}%',
                  context,
                ),
              ),
              Container(
                width: 1,
                height: 30.h,
                color: ColorPalette.greyColor2,
              ),
              Expanded(
                child: _buildStat(
                  StringConstants.winRate.tr(),
                  '${(mentor.winRate ?? 0).toStringAsFixed(1)}%',
                  context,
                ),
              ),
              Container(
                width: 1,
                height: 30.h,
                color: ColorPalette.greyColor2,
              ),
              Expanded(
                child: _buildStat(
                  StringConstants.monthly.tr(),
                  '${(mentor.monthlyProfit ?? 0).toStringAsFixed(1)}%',
                  context,
                ),
              ),
              Container(
                width: 1,
                height: 30.h,
                color: ColorPalette.greyColor2,
              ),
              Expanded(
                child: _buildStat(
                  StringConstants.portfolio.tr(),
                  '${(mentor.portfolio ?? 0)}',
                  context,
                ),
              ),
            ],
          ),
          8.verticalSpace,
          Expanded(
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  context.handleSignedInAction(
                    skipAccountCheck: true,
                    onTap: () => Navigator.pushNamed(
                      context,
                      routeSmartInvestmentScreen,
                      arguments: {'mentor': mentor},
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: myColorScheme(context).primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                ),
                child: Text(
                  StringConstants.follow.tr(),
                  style: FontPalette.medium14.copyWith(color: Colors.white),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStat(String label, String value, BuildContext context) {
    return Column(
      children: [
        Text(
          value,
          style: FontPalette.medium14.copyWith(
            color: myColorScheme(context).primaryColor,
          ),
        ),
        4.verticalSpace,
        Text(
          label,
          style: FontPalette.normal12.copyWith(
            color: myColorScheme(context).greyColor4,
          ),
        ),
      ],
    );
  }
}
