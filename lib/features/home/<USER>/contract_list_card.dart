// import 'package:easy_localization/easy_localization.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:sf_app_v2/core/constants/string_constants.dart';
// import 'package:sf_app_v2/core/routes/routes.dart';
// import 'package:super_tooltip/super_tooltip.dart';
// import 'package:sf_app_v2/core/theme/color_pallette.dart';
// import 'package:sf_app_v2/core/theme/font_pallette.dart';
// import 'package:sf_app_v2/core/widgets/custom_button.dart';
// import 'package:sf_app_v2/core/api/network/route_arguments/product_details_argument.dart';

// class ContractListCard extends StatelessWidget {
//   final void Function()? onBackPressed;
//   final int productId;
//   final String title;
//   final String type;
//   final String size;
//   final String price;
//   final bool purchasable;
//   final String returnRate;
//   final String retracementRate;

//   const ContractListCard({
//     super.key,
//     required this.productId,
//     required this.title,
//     required this.type,
//     required this.size,
//     required this.price,
//     required this.returnRate,
//     required this.purchasable,
//     this.onBackPressed,
//     required this.retracementRate,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadius.all(Radius.circular(20.r)),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.grey.withValues(alpha: 0.1),
//             spreadRadius: 1.r,
//             blurRadius: 1.r,
//             offset: const Offset(0, 1), // changes position of shadow
//           ),
//         ],
//       ),
//       margin: EdgeInsets.zero,
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           SizedBox(
//             height: 54.h,
//             child: Padding(
//               padding: EdgeInsets.only(left: 19.w),
//               child: Align(
//                 alignment: Alignment.centerLeft,
//                 child: Text(
//                   title,
//                   maxLines: 2,
//                   overflow: TextOverflow.ellipsis,
//                   style: FontPalette.semiBold14,
//                   textAlign: TextAlign.start,
//                 ),
//               ),
//             ),
//           ),
//           Divider(height: 1.h, thickness: 1.w),
//           Container(
//             height: 176.h,
//             width: double.infinity,
//             decoration: BoxDecoration(
//               gradient: LinearGradient(
//                 begin: Alignment.centerLeft,
//                 end: Alignment.centerRight,
//                 colors: [ColorPalette.greyColor1, Colors.white],
//               ),
//             ),
//             child: Row(
//               children: [
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Padding(
//                       padding: const EdgeInsets.fromLTRB(20, 10, 20, 0).r,
//                       child: Row(
//                         crossAxisAlignment: CrossAxisAlignment.center,
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [
//                           Text(
//                             '${StringConstants.type.tr()}: ',
//                             style: FontPalette.normal12,
//                           ),
//                           Text(
//                             type,
//                             style: FontPalette.normal12,
//                           ),
//                         ],
//                       ),
//                     ),
//                     Padding(
//                       padding: const EdgeInsets.fromLTRB(20, 5, 20, 0).r,
//                       child: Row(
//                         crossAxisAlignment: CrossAxisAlignment.center,
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [
//                           Text(
//                             '${StringConstants.size.tr()}: ',
//                             style: FontPalette.normal12,
//                           ),
//                           Text(
//                             size,
//                             style: FontPalette.normal12,
//                           ),
//                         ],
//                       ),
//                     ),
//                     Padding(
//                       padding: const EdgeInsets.fromLTRB(20, 5, 20, 0).r,
//                       child: Text(
//                         price,
//                         style: FontPalette.semiBold25,
//                       ),
//                     ),
//                     Padding(
//                       padding: const EdgeInsets.fromLTRB(20, 5, 20, 0).r,
//                       child: Row(
//                         crossAxisAlignment: CrossAxisAlignment.center,
//                         children: [
//                           Icon(
//                             Icons.keyboard_arrow_up_rounded,
//                             color: ColorPalette.greenColor,
//                           ),
//                           Text(
//                             returnRate,
//                             style: FontPalette.semiBold12
//                                 .copyWith(color: ColorPalette.greenColor),
//                           ),
//                           SizedBox(
//                             width: 4.r,
//                           ),
//                           Text(
//                             StringConstants.returnRate.tr(),
//                             style: FontPalette.normal12,
//                           ),
//                         ],
//                       ),
//                     ),
//                     Padding(
//                       padding: const EdgeInsets.fromLTRB(20, 5, 20, 0).r,
//                       child: Row(
//                         crossAxisAlignment: CrossAxisAlignment.center,
//                         children: [
//                           Text(
//                             StringConstants.retracementRate.tr(),
//                             style: FontPalette.normal12,
//                           ),
//                           6.horizontalSpace,
//                           SuperTooltip(
//                             popupDirection: TooltipDirection.right,
//                             content: SizedBox(
//                               height: 100.h,
//                               width: 200.w,
//                               child: Row(
//                                 children: [
//                                   Text(
//                                     '$retracementRate%',
//                                     style: TextStyle(
//                                       fontSize: 14.sp,
//                                       fontWeight: FontWeight.bold,
//                                       color: ColorPalette.primaryVar1,
//                                     ),
//                                   ),
//                                   10.horizontalSpace,
//                                   Text(
//                                     StringConstants.tootTipWarning.tr(),
//                                     softWrap: true,
//                                     style: TextStyle(fontSize: 10.sp),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                             child: Container(
//                               color: Colors.transparent,
//                               child: CircleAvatar(
//                                 backgroundColor: ColorPalette.primaryVar1,
//                                 radius: 11.r,
//                                 child: Center(
//                                   child: Text(
//                                     "!",
//                                     style: TextStyle(
//                                       color: ColorPalette.white,
//                                       fontSize: 11.sp,
//                                       fontWeight: FontWeight.w800,
//                                     ),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ],
//                 ),
//                 // SvgPicture.asset(Assets.chart)
//               ],
//             ),
//           ),
//           Container(
//             height: 48.h,
//             decoration: BoxDecoration(
//               color: ColorPalette.white,
//               border:
//                   Border.all(color: ColorPalette.borderColor2, width: 0.1.w),
//               borderRadius: BorderRadius.only(
//                 bottomLeft: Radius.circular(20.r),
//                 bottomRight: Radius.circular(20.r),
//               ),
//             ),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.end,
//               children: [
//                 if (!purchasable)
//                   Expanded(
//                     child: Row(
//                       children: [
//                         18.horizontalSpace,
//                         CircleAvatar(
//                           backgroundColor: ColorPalette.deniedColor,
//                           radius: 4.5,
//                           child: Text(
//                             "!",
//                             style: TextStyle(
//                               color: ColorPalette.white,
//                               fontSize: 7.sp,
//                             ),
//                           ),
//                         ),
//                         7.horizontalSpace,
//                         Expanded(
//                           child: Text(
//                             StringConstants.toolTipWarning2.tr(),
//                             style: FontPalette.normal9
//                                 .copyWith(color: ColorPalette.primaryVar1),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ClipRRect(
//                   borderRadius: const BorderRadius.only(
//                     bottomRight: Radius.circular(20),
//                   ).r,
//                   child: CustomButton(
//                     onPressed: () {
//                       Navigator.pushNamed(
//                         context,
//                         routeContractDetailScreen,
//                         arguments: ProductDetailsDetailsArguments(
//                           productId: productId,
//                           title: title,
//                         ),
//                       );
//                     },
//                     isEnabled: purchasable,
//                     width: 100.w,
//                     height: 50.h,
//                     label: StringConstants.buy.tr(),
//                     borderRadiusUser: 0,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
