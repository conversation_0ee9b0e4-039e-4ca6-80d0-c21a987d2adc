import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';

class MarketUpdateListHeader extends StatelessWidget {
  final String userName;
  final String? icon;
  final void Function()? onPressedIcon;

  const MarketUpdateListHeader({
    super.key,
    required this.userName,
    this.icon,
    this.onPressedIcon,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        icon != null
            ? IconButton(
                onPressed: onPressedIcon,
                icon: Image.asset(
                  icon!,
                  width: 24.w,
                  height: 34.h,
                ),
              )
            : const SizedBox.shrink(),
        Text(
          userName,
          style:
              FontPalette.semiBold14.copyWith(color: ColorPalette.primaryBlack),
        ),
      ],
    );
  }
}
