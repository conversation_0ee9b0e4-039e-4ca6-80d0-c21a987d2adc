import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/widgets/modern_card.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';

class ModernSkeletonLoader extends StatefulWidget {
  const ModernSkeletonLoader({super.key});

  @override
  State<ModernSkeletonLoader> createState() => _ModernSkeletonLoaderState();
}

class _ModernSkeletonLoaderState extends State<ModernSkeletonLoader>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children: [
          // Header skeleton
          _buildHeaderSkeleton(),
          SizedBox(height: 24.h),
          
          // Balance card skeleton
          _buildBalanceCardSkeleton(),
          SizedBox(height: 24.h),
          
          // Quick actions skeleton
          _buildQuickActionsSkeleton(),
          SizedBox(height: 28.h),
          
          // Carousel skeleton
          _buildCarouselSkeleton(),
          SizedBox(height: 28.h),
          
          // Market section skeleton
          _buildMarketSkeleton(),
          SizedBox(height: 28.h),
          
          // News skeleton
          _buildNewsSkeleton(),
        ],
      ),
    );
  }

  Widget _buildHeaderSkeleton() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return ModernCard(
          style: ModernCardStyle.glass,
          padding: EdgeInsets.all(20.r),
          child: Row(
            children: [
              Container(
                width: 40.w,
                height: 40.h,
                decoration: BoxDecoration(
                  color: ColorPalette.primaryColor.withValues(alpha: _pulseAnimation.value * 0.3),
                  borderRadius: BorderRadius.circular(20.r),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 120.w,
                      height: 16.h,
                      decoration: BoxDecoration(
                        color: ColorPalette.primaryColor.withValues(alpha: _pulseAnimation.value * 0.3),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Container(
                      width: 80.w,
                      height: 12.h,
                      decoration: BoxDecoration(
                        color: ColorPalette.primaryColor.withValues(alpha: _pulseAnimation.value * 0.2),
                        borderRadius: BorderRadius.circular(6.r),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 32.w,
                height: 32.h,
                decoration: BoxDecoration(
                  color: ColorPalette.primaryColor.withValues(alpha: _pulseAnimation.value * 0.3),
                  borderRadius: BorderRadius.circular(16.r),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildBalanceCardSkeleton() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return ModernCard(
          style: ModernCardStyle.gradient,
          gradientColors: [
            ColorPalette.primaryColor.withValues(alpha: _pulseAnimation.value * 0.5),
            ColorPalette.accentColor.withValues(alpha: _pulseAnimation.value * 0.3),
          ],
          padding: EdgeInsets.all(28.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 100.w,
                        height: 14.h,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: _pulseAnimation.value * 0.5),
                          borderRadius: BorderRadius.circular(7.r),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Container(
                        width: 150.w,
                        height: 32.h,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: _pulseAnimation.value * 0.7),
                          borderRadius: BorderRadius.circular(16.r),
                        ),
                      ),
                    ],
                  ),
                  Container(
                    width: 48.w,
                    height: 48.h,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: _pulseAnimation.value * 0.3),
                      borderRadius: BorderRadius.circular(24.r),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 24.h),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      height: 80.h,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: _pulseAnimation.value * 0.2),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Container(
                      height: 80.h,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: _pulseAnimation.value * 0.2),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickActionsSkeleton() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return ModernCard(
          style: ModernCardStyle.neumorphism,
          padding: EdgeInsets.all(24.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 120.w,
                height: 20.h,
                decoration: BoxDecoration(
                  color: ColorPalette.primaryColor.withValues(alpha: _pulseAnimation.value * 0.3),
                  borderRadius: BorderRadius.circular(10.r),
                ),
              ),
              SizedBox(height: 16.h),
              Row(
                children: List.generate(4, (index) {
                  return Expanded(
                    child: Container(
                      margin: EdgeInsets.only(right: index < 3 ? 12.w : 0),
                      height: 100.h,
                      decoration: BoxDecoration(
                        color: ColorPalette.primaryColor.withValues(alpha: _pulseAnimation.value * 0.2),
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                    ),
                  );
                }),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCarouselSkeleton() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return ModernCard(
          style: ModernCardStyle.glass,
          padding: EdgeInsets.all(16.r),
          child: Container(
            height: 200.h,
            decoration: BoxDecoration(
              color: ColorPalette.primaryColor.withValues(alpha: _pulseAnimation.value * 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMarketSkeleton() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return ModernCard(
          style: ModernCardStyle.neumorphism,
          padding: EdgeInsets.all(24.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 150.w,
                height: 24.h,
                decoration: BoxDecoration(
                  color: ColorPalette.primaryColor.withValues(alpha: _pulseAnimation.value * 0.3),
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              SizedBox(height: 20.h),
              Container(
                height: 120.h,
                decoration: BoxDecoration(
                  color: ColorPalette.primaryColor.withValues(alpha: _pulseAnimation.value * 0.2),
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNewsSkeleton() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return ModernCard(
          style: ModernCardStyle.neumorphism,
          padding: EdgeInsets.all(24.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 120.w,
                height: 20.h,
                decoration: BoxDecoration(
                  color: ColorPalette.primaryColor.withValues(alpha: _pulseAnimation.value * 0.3),
                  borderRadius: BorderRadius.circular(10.r),
                ),
              ),
              SizedBox(height: 24.h),
              ...List.generate(3, (index) {
                return Container(
                  margin: EdgeInsets.only(bottom: 16.h),
                  child: Row(
                    children: [
                      Container(
                        width: 100.w,
                        height: 80.h,
                        decoration: BoxDecoration(
                          color: ColorPalette.primaryColor.withValues(alpha: _pulseAnimation.value * 0.2),
                          borderRadius: BorderRadius.circular(16.r),
                        ),
                      ),
                      SizedBox(width: 16.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: double.infinity,
                              height: 16.h,
                              decoration: BoxDecoration(
                                color: ColorPalette.primaryColor.withValues(alpha: _pulseAnimation.value * 0.3),
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                            ),
                            SizedBox(height: 8.h),
                            Container(
                              width: 120.w,
                              height: 12.h,
                              decoration: BoxDecoration(
                                color: ColorPalette.primaryColor.withValues(alpha: _pulseAnimation.value * 0.2),
                                borderRadius: BorderRadius.circular(6.r),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          ),
        );
      },
    );
  }
}
