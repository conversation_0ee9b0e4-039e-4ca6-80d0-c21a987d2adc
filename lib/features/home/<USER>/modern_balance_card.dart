import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/widgets/modern_card.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';
import 'package:sf_app_v2/features/home/<USER>/home/<USER>';
import 'package:sf_app_v2/core/constants/enums.dart';

class ModernBalanceCard extends StatelessWidget {
  const ModernBalanceCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: ModernCard(
        style: ModernCardStyle.gradient,
        gradientColors: [
          ColorPalette.primaryColor,
          ColorPalette.primaryColor.withValues(alpha: 0.8),
          ColorPalette.accentColor.withValues(alpha: 0.6),
        ],
        padding: EdgeInsets.all(24.r),
        child: BlocBuilder<HomeCubit, HomeState>(
          builder: (context, state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          StringConstants.totalBalance.tr(),
                          style: FontPalette.labelMedium.copyWith(
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                        ),
                        SizedBox(height: 4.h),
                        if (state.balanceFetchStatus == DataStatus.loading)
                          CommonShimmer(
                            width: 120.w,
                            height: 32.h,
                            br: 8.r,
                          )
                        else
                          Text(
                            '\$${(state.balanceData?.data.community?.totalAmount ?? 0.0).toStringAsFixed(2)}',
                            style: FontPalette.headlineLarge.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                      ],
                    ),
                    Container(
                      padding: EdgeInsets.all(12.r),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                      child: Icon(
                        Icons.account_balance_wallet_outlined,
                        color: Colors.white,
                        size: 24.r,
                      ),
                    ),
                  ],
                ),
                
                SizedBox(height: 24.h),
                
                // Balance breakdown
                if (state.balanceFetchStatus != DataStatus.loading) ...[
                  Row(
                    children: [
                      Expanded(
                        child: _buildBalanceItem(
                          StringConstants.availableFunds.tr(),
                          '\$${(state.balanceData?.data.community?.availableBalance ?? 0.0).toStringAsFixed(2)}',
                          Icons.trending_up,
                          ColorPalette.successColor,
                        ),
                      ),
                      SizedBox(width: 16.w),
                      Expanded(
                        child: _buildBalanceItem(
                          StringConstants.unavailableFunds.tr(),
                          '\$${(state.balanceData?.data.community?.lockedBalance ?? 0.0).toStringAsFixed(2)}',
                          Icons.lock_outline,
                          ColorPalette.warningColor,
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  Row(
                    children: [
                      Expanded(
                        child: CommonShimmer(
                          width: double.infinity,
                          height: 60.h,
                          br: 12.r,
                        ),
                      ),
                      SizedBox(width: 16.w),
                      Expanded(
                        child: CommonShimmer(
                          width: double.infinity,
                          height: 60.h,
                          br: 12.r,
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildBalanceItem(String label, String amount, IconData icon, Color iconColor) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(6.r),
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: 16.r,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  label,
                  style: FontPalette.labelSmall.copyWith(
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            amount,
            style: FontPalette.titleMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
