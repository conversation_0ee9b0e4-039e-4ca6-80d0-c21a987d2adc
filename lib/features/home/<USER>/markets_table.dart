import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/constants/keys.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/utils/convert_helper.dart';
import 'package:sf_app_v2/features/home/<USER>/models/market/market_list_model.dart';

import '../../../core/common_function.dart';

class CustomMarketsTable extends StatelessWidget {
  final List<MarketListData>? marketListData;

  const CustomMarketsTable({
    super.key,
    required this.marketListData,
  });

  @override
  Widget build(BuildContext context) {
    getRow(MarketListData item) {
      return DataRow(
        cells: [
          DataCell(
            Column(
              children: [
                10.verticalSpace,
                Row(
                  children: [
                    SizedBox(
                      width: 28.r,
                      height: 28.r,
                      child: SvgPicture.asset(coins[item.symbol].toString()),
                    ),
                    10.horizontalSpace,
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          CommonFunctions()
                              .formatMarketSymbol(item.symbol.toString()),
                          overflow: TextOverflow.fade,
                          maxLines: 1,
                          softWrap: false,
                          style: FontPalette.medium12,
                        ),
                        Text(
                          coinsText[item.symbol].toString(),
                          style: FontPalette.normal9,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          DataCell(
            Row(
              children: [
                double.parse(item.priceChangePercent) > 0
                    ? Icon(
                        Icons.arrow_drop_up_rounded,
                        color: ColorPalette.successColor,
                      )
                    : Icon(
                        Icons.arrow_drop_down_rounded,
                        color: ColorPalette.deniedColor,
                      ),
                Text(
                  ConvertHelper.formatPriceUsd(double.parse(item.openPrice)),
                  overflow: TextOverflow.fade,
                  maxLines: 1,
                  softWrap: false,
                  style: FontPalette.normal12,
                ),
              ],
            ),
          ),
          DataCell(
            Text(
              double.parse(item.priceChangePercent) > 0
                  ? '+${double.parse(item.priceChangePercent)}%'
                  : '${double.parse(item.priceChangePercent)}%',
              style: FontPalette.semiBold12.copyWith(
                color: double.parse(item.priceChangePercent) > 0
                    ? ColorPalette.successColor
                    : ColorPalette.deniedColor,
              ),
            ),
          ),
        ],
      );
    }

    return SizedBox(
      width: 1.sw,
      child: DataTable(
        dataRowHeight: 64.h,
        headingRowHeight: 64.h,
        columns: [
          DataColumn(
            label: Text(
              StringConstants.mainstreamCurrency.tr(),
              style: FontPalette.medium14,
            ),
          ),
          DataColumn(
            label: Text(
              StringConstants.latestTransaction.tr(),
              style: FontPalette.medium14,
            ),
          ),
          DataColumn(
            label: Text(
              StringConstants.todaysChange.tr(),
              style: FontPalette.medium14,
            ),
          ),
        ],
        rows: [
          ...?marketListData?.map((item) => getRow(item)),
        ],
      ),
    );
  }
}
