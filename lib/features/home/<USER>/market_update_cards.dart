import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/constants/keys.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/utils/convert_helper.dart';
import 'package:sf_app_v2/core/widgets/common_icon_button.dart';

class MarketUpdateCard extends StatelessWidget {
  final double width;
  final double height;
  final String titleText;
  final String priceChangePercent;
  final double openPrice;
  final String symbol;
  final bool direction;

  const MarketUpdateCard({
    super.key,
    required this.width,
    required this.height,
    required this.titleText,
    required this.priceChangePercent,
    required this.symbol,
    required this.openPrice,
    required this.direction,
  });

  @override
  Widget build(BuildContext context) {
    double price = double.parse(priceChangePercent);

    return SizedBox(
      width: width,
      height: height,
      child: Stack(
        children: [
          Container(
            margin: const EdgeInsets.all(0),
            width: width,
            child: Container(
              margin: const EdgeInsets.fromLTRB(10, 30, 10, 0).r,
              child: Card(
                elevation: 8,
                shadowColor: ColorPalette.shadowColor.withValues(alpha: 0.7),
                margin: const EdgeInsets.all(0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(18.0).r,
                ),
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    20.verticalSpace,
                    Text(
                      titleText,
                      style: FontPalette.bold10,
                      textAlign: TextAlign.center,
                    ),
                    Text(
                      coinsText[symbol] ?? '',
                      style:
                          FontPalette.normal9.copyWith(color: Colors.black54),
                      textAlign: TextAlign.center,
                    ),
                    4.verticalSpace,
                    Text(
                      '${price < 0 ? priceChangePercent : '+$priceChangePercent'}%',
                      style: FontPalette.semiBold10.copyWith(
                        color: double.parse(priceChangePercent) > 0
                            ? ColorPalette.greenColor
                            : ColorPalette.deniedColor,
                      ),
                      textAlign: TextAlign.start,
                    ),
                    6.verticalSpace,
                    SizedBox(
                      width: width,
                      child: Card(
                        margin: const EdgeInsets.all(0),
                        elevation: 0,
                        color: ColorPalette.primaryColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.zero,
                            topRight: Radius.zero,
                            bottomLeft: const Radius.circular(18.0).r,
                            bottomRight: const Radius.circular(18.0).r,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            children: [
                              direction
                                  ? Icon(
                                      Icons.arrow_drop_up_rounded,
                                      color: ColorPalette.successColor,
                                    )
                                  : Icon(
                                      Icons.arrow_drop_down_rounded,
                                      color: ColorPalette.deniedColor,
                                    ),
                              Text(
                                ConvertHelper.formatPriceUsd(openPrice),
                                style: FontPalette.medium13
                                    .copyWith(color: Colors.white),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            top: 12.h,
            left: 1.w,
            right: 1.w,
            child: CustomIconSvgButton(
              // backgroundColor: Colors.amber,
              width: 35.r,
              icon: coins[symbol].toString(),
              onPressed: () {},
            ),
          ),
        ],
      ),
    );
  }
}
