import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/features/home/<USER>/mentor_slider_card.dart';

import '../../../core/constants/enums.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/routes/routes.dart';
import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';
import '../../../core/widgets/common_shimmer.dart';
import '../../smart_investment/domain/models/mentor/mentor_model.dart';
import '../../smart_investment/logic/smart_investment/smart_investment_cubit.dart';

class MentorSlider extends StatelessWidget {
  const MentorSlider({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
      builder: (context, state) {
        if (state.mentorListFetchStatus == DataStatus.loading) {
          return _buildShimmerView(context);
        }

        if (state.mentorListData?.data?.list?.isEmpty ?? true) {
          return const SizedBox.shrink();
        }

        return Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  child: Text(
                    StringConstants.mentors.tr(),
                    style: FontPalette.bold16.copyWith(
                      color: myColorScheme(context).titleColor,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  child: TextButton(
                    onPressed: () => Navigator.pushNamed(context, routeMentorListScreen, arguments: {'showBackButton': true}),
                    child: Text(
                      StringConstants.viewAll.tr(),
                      style: FontPalette.medium10.copyWith(
                        color: myColorScheme(context).viewAllColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 190.h,
              width: 1.sh,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: (state.mentorListData?.data?.list?.length ?? 0) >= 5
                    ? 5
                    : state.mentorListData?.data?.list?.length ?? 0,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  final mentor = state.mentorListData?.data?.list?[index];
                  return Padding(
                    padding: const EdgeInsets.fromLTRB(12, 0, 0, 0).r,
                    child: MentorSliderCard(
                      mentor: mentor ?? const Mentor(),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildShimmerView(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 30.w),
              child: Text(
                StringConstants.mentors.tr(),
                style: FontPalette.bold16.copyWith(
                  color: myColorScheme(context).titleColor,
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 30.w),
              child: TextButton(
                onPressed: () {},
                child: Text(
                  StringConstants.viewAll.tr(),
                  style: FontPalette.medium10.copyWith(
                    color: myColorScheme(context).viewAllColor,
                  ),
                ),
              ),
            ),
          ],
        ),
        Container(
          padding: EdgeInsets.only(left: 10.w),
          height: 190.h,
          width: 1.sh,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: 3,
            shrinkWrap: true,
            itemBuilder: (context, index) => Padding(
              padding: const EdgeInsets.fromLTRB(6, 0, 6, 0).r,
              child: CommonShimmer(
                br: 20.r,
                width: 280.w,
                height: 190.h,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
