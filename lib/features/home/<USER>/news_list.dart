import 'package:flutter/material.dart';
import 'package:flutter_avif/flutter_avif.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/utils/convert_helper.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';
import 'package:sf_app_v2/core/widgets/modern_card.dart';

import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/features/home/<USER>/home/<USER>';
import 'package:shimmer_animation/shimmer_animation.dart';

import '../../../core/constants/enums.dart';
import 'package:easy_localization/easy_localization.dart';

class NewsList extends StatelessWidget {
  const NewsList({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: ModernCard(
        style: ModernCardStyle.neumorphism,
        padding: EdgeInsets.all(24.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(8.r),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            ColorPalette.primaryColor.withValues(alpha: 0.2),
                            ColorPalette.accentColor.withValues(alpha: 0.2),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Icon(
                        Icons.article_outlined,
                        color: ColorPalette.primaryColor,
                        size: 20.r,
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          StringConstants.newsUpdates.tr(),
                          style: FontPalette.titleLarge.copyWith(
                            color: myColorScheme(context).titleColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 2.h),
                        Text(
                          'Latest market insights',
                          style: FontPalette.labelMedium.copyWith(
                            color: myColorScheme(context).subTitleColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        ColorPalette.primaryColor.withValues(alpha: 0.1),
                        ColorPalette.accentColor.withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20.r),
                    border: Border.all(
                      color: ColorPalette.primaryColor.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    StringConstants.seeAll.tr(),
                    style: FontPalette.labelSmall.copyWith(
                      color: ColorPalette.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 24.h),
            BlocBuilder<HomeCubit, HomeState>(
              builder: (context, state) {
                return ListView.separated(
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: state.newsDataList?.data.newsDataList.length ?? 0,
                  shrinkWrap: true,
                  separatorBuilder: (_, __) => Container(
                    height: 1,
                    margin: EdgeInsets.symmetric(vertical: 16.h),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.transparent,
                          (myColorScheme(context).borderColor ?? ColorPalette.borderColor).withValues(alpha: 0.3),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                  itemBuilder: (context, index) {
                    final news = state.newsDataList?.data.newsDataList[index];
                    final isAvif = news?.coverUrl?.contains(".avif") ?? false;
                    if (state.newsUpdatesFetchStatus == DataStatus.loading &&
                        state.newsDataList?.data.newsDataList.isEmpty == true) {
                      return const NewsListItemShimmer();
                    }
                    return ModernCard(
                      style: ModernCardStyle.glass,
                      padding: EdgeInsets.all(16.r),
                      onTap: () => Navigator.pushNamed(
                        context,
                        routeNewsDetailScreen,
                        arguments: {"id": news?.id ?? 0},
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Hero(
                            tag: news?.id ?? 0,
                            child: _ModernNewsImage(
                              imageUrl: news?.coverUrl ?? '',
                              isAvif: isAvif,
                            ),
                          ),
                          SizedBox(width: 16.w),
                          Expanded(
                            child: _ModernNewsContent(
                              title: news?.title ?? '',
                              datetime: ConvertHelper.formatDateMonthDay(
                                news?.createTime.toString() ?? '',
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _NewsImageShimmer extends StatelessWidget {
  const _NewsImageShimmer();

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12.r),
      child: CommonShimmer(
        width: 100.w,
        height: 70.h,
      ),
    );
  }
}

class _ErrorImage extends StatelessWidget {
  const _ErrorImage();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ColorPalette.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image_not_supported_rounded,
            color: ColorPalette.primaryColor.withValues(alpha: 0.3),
            size: 24.w,
          ),
          SizedBox(height: 4.h),
          Text(
            'Image not available',
            style: FontPalette.semiBold9.copyWith(
              color: ColorPalette.primaryColor.withValues(alpha: 0.3),
            ),
          ),
        ],
      ),
    );
  }
}

class _ModernNewsImage extends StatelessWidget {
  final String imageUrl;
  final bool isAvif;

  const _ModernNewsImage({
    required this.imageUrl,
    required this.isAvif,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: ColorPalette.primaryColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.r),
        child: SizedBox(
          width: 100.w,
          height: 80.h,
          child: isAvif
              ? AvifImage.network(
                  imageUrl,
                  fit: BoxFit.cover,
                  loadingBuilder: (_, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return const _NewsImageShimmer();
                  },
                  errorBuilder: (_, __, ___) => const _ErrorImage(),
                )
              : Image.network(
                  imageUrl,
                  fit: BoxFit.cover,
                  loadingBuilder: (_, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return const _NewsImageShimmer();
                  },
                  errorBuilder: (_, __, ___) => const _ErrorImage(),
                ),
        ),
      ),
    );
  }
}

class _ModernNewsContent extends StatelessWidget {
  final String title;
  final String datetime;

  const _ModernNewsContent({
    required this.title,
    required this.datetime,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: FontPalette.titleMedium.copyWith(
            color: myColorScheme(context).titleColor,
            height: 1.4,
            fontWeight: FontWeight.w600,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(height: 12.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
          decoration: BoxDecoration(
            color: ColorPalette.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20.r),
            border: Border.all(
              color: ColorPalette.primaryColor.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.schedule_outlined,
                size: 14.sp,
                color: ColorPalette.primaryColor,
              ),
              SizedBox(width: 6.w),
              Text(
                datetime,
                style: FontPalette.labelSmall.copyWith(
                  color: ColorPalette.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class NewsListItemShimmer extends StatelessWidget {
  const NewsListItemShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ModernCard(
      style: ModernCardStyle.glass,
      padding: EdgeInsets.all(12.r),
      child: Shimmer(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Shimmer for Image
            Container(
              width: 100.w,
              height: 70.h,
              decoration: BoxDecoration(
                color: myColorScheme(context).cardColor,
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Shimmer for Title
                  Container(
                    width: double.infinity,
                    height: 16.h,
                    decoration: BoxDecoration(
                      color: myColorScheme(context).cardColor,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    margin: EdgeInsets.only(bottom: 8.h),
                  ),
                  // Shimmer for Second Title Line
                  Container(
                    width: 0.7.sw,
                    height: 16.h,
                    decoration: BoxDecoration(
                      color: myColorScheme(context).cardColor,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    margin: EdgeInsets.only(bottom: 12.h),
                  ),
                  // Shimmer for Date
                  Container(
                    width: 100.w,
                    height: 12.h,
                    decoration: BoxDecoration(
                      color: myColorScheme(context).cardColor,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class NewsListShimmer extends StatelessWidget {
  final int itemCount;

  const NewsListShimmer({
    super.key,
    this.itemCount = 5,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: itemCount,
      itemBuilder: (context, index) => const NewsListItemShimmer(),
    );
  }
}
