import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/widgets/modern_card.dart';
import 'package:sf_app_v2/core/routes/routes.dart';

class ModernQuickActions extends StatelessWidget {
  const ModernQuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: ModernCard(
        style: ModernCardStyle.glass,
        padding: EdgeInsets.all(20.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              StringConstants.quickActions.tr(),
              style: FontPalette.titleMedium.copyWith(
                color: myColorScheme(context).titleColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    context,
                    icon: Icons.add_circle_outline,
                    label: StringConstants.deposit.tr(),
                    color: ColorPalette.successColor,
                    onTap: () => _navigateToDeposit(context),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildActionButton(
                    context,
                    icon: Icons.remove_circle_outline,
                    label: StringConstants.withdraw.tr(),
                    color: ColorPalette.warningColor,
                    onTap: () => _navigateToWithdraw(context),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildActionButton(
                    context,
                    icon: Icons.swap_horiz,
                    label: StringConstants.transfer.tr(),
                    color: ColorPalette.primaryColor,
                    onTap: () => _navigateToTransfer(context),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildActionButton(
                    context,
                    icon: Icons.history,
                    label: StringConstants.history.tr(),
                    color: ColorPalette.accentColor,
                    onTap: () => _navigateToHistory(context),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 8.w),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24.r,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              label,
              style: FontPalette.labelSmall.copyWith(
                color: myColorScheme(context).titleColor,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToDeposit(BuildContext context) {
    // Navigate to deposit screen
    Navigator.pushNamed(context, routeDepositScreen);
  }

  void _navigateToWithdraw(BuildContext context) {
    // Navigate to withdraw screen
    Navigator.pushNamed(context, routeWithdrawScreen);
  }

  void _navigateToTransfer(BuildContext context) {
    // Navigate to transfer screen
    Navigator.pushNamed(context, routeTransferScreen);
  }

  void _navigateToHistory(BuildContext context) {
    // Navigate to transaction history screen
    Navigator.pushNamed(context, routeRecordsScreen);
  }
}
