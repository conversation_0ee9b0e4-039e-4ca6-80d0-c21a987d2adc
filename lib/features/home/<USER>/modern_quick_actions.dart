import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/widgets/modern_card.dart';
import 'package:sf_app_v2/core/routes/routes.dart';

class ModernQuickActions extends StatefulWidget {
  const ModernQuickActions({super.key});

  @override
  State<ModernQuickActions> createState() => _ModernQuickActionsState();
}

class _ModernQuickActionsState extends State<ModernQuickActions>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<double>> _buttonAnimations;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _buttonAnimations = List.generate(4, (index) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          0.1 + (index * 0.1),
          0.6 + (index * 0.1),
          curve: Curves.elasticOut,
        ),
      ));
    });

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: ModernCard(
        style: ModernCardStyle.neumorphism,
        padding: EdgeInsets.all(24.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              StringConstants.quickActions.tr(),
              style: FontPalette.titleMedium.copyWith(
                color: myColorScheme(context).titleColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16.h),
            AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Row(
                  children: [
                    Expanded(
                      child: ScaleTransition(
                        scale: _buttonAnimations[0],
                        child: _buildActionButton(
                          context,
                          icon: Icons.add_circle_outline,
                          label: StringConstants.deposit.tr(),
                          color: ColorPalette.successColor,
                          onTap: () => _navigateToDeposit(context),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: ScaleTransition(
                        scale: _buttonAnimations[1],
                        child: _buildActionButton(
                          context,
                          icon: Icons.remove_circle_outline,
                          label: StringConstants.withdraw.tr(),
                          color: ColorPalette.warningColor,
                          onTap: () => _navigateToWithdraw(context),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: ScaleTransition(
                        scale: _buttonAnimations[2],
                        child: _buildActionButton(
                          context,
                          icon: Icons.swap_horiz,
                          label: StringConstants.transfer.tr(),
                          color: ColorPalette.primaryColor,
                          onTap: () => _navigateToTransfer(context),
                        ),
                      ),
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: ScaleTransition(
                        scale: _buttonAnimations[3],
                        child: _buildActionButton(
                          context,
                          icon: Icons.history,
                          label: StringConstants.history.tr(),
                          color: ColorPalette.accentColor,
                          onTap: () => _navigateToHistory(context),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 12.w),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withValues(alpha: 0.15),
              color.withValues(alpha: 0.08),
            ],
          ),
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(14.r),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    color.withValues(alpha: 0.2),
                    color.withValues(alpha: 0.3),
                  ],
                ),
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: color,
                size: 26.r,
              ),
            ),
            SizedBox(height: 12.h),
            Text(
              label,
              style: FontPalette.labelMedium.copyWith(
                color: myColorScheme(context).titleColor,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToDeposit(BuildContext context) {
    // Navigate to deposit screen
    Navigator.pushNamed(context, routeDepositScreen);
  }

  void _navigateToWithdraw(BuildContext context) {
    // Navigate to withdraw screen
    Navigator.pushNamed(context, routeWithdrawScreen);
  }

  void _navigateToTransfer(BuildContext context) {
    // Navigate to transfer screen
    Navigator.pushNamed(context, routeTransferScreen);
  }

  void _navigateToHistory(BuildContext context) {
    // Navigate to transaction history screen
    Navigator.pushNamed(context, routeRecordsScreen);
  }
}
