import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/config/app_config.dart';
import 'package:sf_app_v2/core/dependency_injection/injectable.dart';
import 'package:sf_app_v2/core/constants/assets.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/common_icon_button.dart';

import '../../../core/widgets/common_shimmer.dart';

class CustomCollectionButtonDrawer extends StatelessWidget {
  final bool? isLoading;

  const CustomCollectionButtonDrawer({
    super.key,
    this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return (isLoading ?? false) ? _buildLoadingState() : _buildContent(context);
  }

  Widget _buildLoadingState() {
    return SizedBox(
      width: 20.sw,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildShimmerItem(),
          _buildShimmerItem(),
          if (getIt<AppConfig>().showTransfer) _buildShimmerItem(),
          _buildShimmerItem(),
          _buildShimmerItem(),
        ],
      ),
    );
  }

  Widget _buildShimmerItem() {
    return Column(
      children: [
        CommonShimmer(
          br: 100.r,
          width: 50.w,
          height: 50.h,
          color: ColorPalette.shimmerColor,
        ),
        5.verticalSpace,
        CommonShimmer(
          br: 4.r,
          width: 30.w,
          height: 6.h,
          color: ColorPalette.shimmerColor,
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton(
          context: context,
          icon: Assets.deposit,
          label: StringConstants.deposit.tr(),
          onTap: () => Navigator.pushNamed(context, routeDepositScreen),
          skipAccountCheck: true,
        ),
        _buildActionButton(
          context: context,
          icon: Assets.withdraw,
          label: StringConstants.withdraw.tr(),
          onTap: () => Navigator.pushNamed(context, routeWithdrawScreen),
        ),
        if (getIt<AppConfig>().showTransfer)
          _buildActionButton(
            context: context,
            icon: Assets.transfer,
            label: StringConstants.transfer.tr(),
            onTap: () => Navigator.pushNamed(context, routeTransferScreen),
            iconColor: Colors.white,
          ),
        _buildActionButton(
          context: context,
          icon: Assets.tradingIcon,
          label: StringConstants.finance.tr(),
          onTap: () => Navigator.pushNamed(context, routeFinanceScreen),
          iconColor: Colors.white,
        ),
        _buildActionButton(
          context: context,
          icon: Assets.records,
          label: StringConstants.records.tr(),
          onTap: () => Navigator.pushNamed(context, routeRecordsScreen),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required String icon,
    required String label,
    required VoidCallback onTap,
    Color? iconColor,
    bool skipAccountCheck = false,
  }) {
    return Column(
      children: [
        CustomIconSvgButton(
          onPressed: () => context.handleSignedInAction(
            skipAccountCheck: skipAccountCheck,
            onTap: onTap,
          ),
          width: 50.w / 1.h,
          backgroundColor: myColorScheme(context).primaryColor,
          icon: icon,
          color: iconColor,
          borderRadiusUser: 50,
          elevation: 5,
        ),
        SizedBox(height: 5.h),
        Text(label),
      ],
    );
  }
}
