// To parse this JSON data, do
//
//     final previewModel = previewModelFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'preview_model.freezed.dart';
part 'preview_model.g.dart';

PreviewModel previewModelFromJson( str) => PreviewModel.fromJson((str));

String previewModelToJson(PreviewModel data) => json.encode(data.toJson());

@freezed
class PreviewModel with _$PreviewModel {
    const factory PreviewModel({
        @Json<PERSON>ey(name: "code")
        required int code,
        @<PERSON><PERSON><PERSON><PERSON>(name: "data")
        required PreviewData data,
        @Json<PERSON><PERSON>(name: "msg")
        required String msg,
    }) = _PreviewModel;

    factory PreviewModel.fromJson(Map<String, dynamic> json) => _$PreviewModelFromJson(json);
}

@freezed
class PreviewData with _$PreviewData {
    const factory PreviewData({
        @Json<PERSON><PERSON>(name: "chargedAmount")
         double? chargedAmount,
        @Json<PERSON>ey(name: "expectedReceive")
         double? expectedReceive,
        @Json<PERSON>ey(name: "fee")
         double? fee,
        @JsonKey(name: "freeAmount")
         double? freeAmount,
    }) = _PreviewData;

    factory PreviewData.fromJson(Map<String, dynamic> json) => _$PreviewDataFromJson(json);
}
