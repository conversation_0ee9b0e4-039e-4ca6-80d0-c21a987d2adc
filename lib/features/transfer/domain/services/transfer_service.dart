import 'package:injectable/injectable.dart';
import '../../../wallet/withdraw/domain/models/collection_supervisor/collection_supervisor_model.dart';
import '../models/preview/preview_model.dart';
import '../repository/transfer_repository.dart';
import 'package:sf_app_v2/core/api/network/network.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/core/api/endpoint/api_endpoints.dart';

/// Service class that implements the TransferRepository interface to handle transfer-related API calls
@Injectable(as: TransferRepository)
class TransferService implements TransferRepository {
  /// Fetches collection supervisor information
  /// Returns a [ResponseResult] containing [CollectionSupervisorModel] on success
  @override
  Future<ResponseResult<CollectionSupervisorModel>>
      collectionSupervisor() async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.collectionSupervisor,
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
              data: CollectionSupervisorModel.fromJson(response.data['data']));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get collection supervisor');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  /// Validates and fetches username for a given email address
  /// [userEmail] - Email address to validate
  /// Returns a [ResponseResult] containing username string on success
  @override
  Future<ResponseResult<String>> collectionUsername({
    required String userEmail,
  }) async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.collectionTransferUserEmail(userEmail),
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] != 500) {
          return ResponseResult(data: response.data);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: response.statusMessage);
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  /// Transfers funds to trading account
  /// [amount] - Amount to transfer
  /// [from] - Source account
  /// [to] - Destination account
  /// Returns a [ResponseResult] containing boolean success status
  @override
  Future<ResponseResult<bool>> toTrading({
    required String amount,
    required String from,
    required String to,
  }) async {
    try {
      final response = await NetworkProvider().post(
        ApiEndpoints.toTrading,
        data: {'amount': amount, 'from': from, 'to': to},
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to transfer to collection');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  /// Transfers funds to another user
  /// [amount] - Amount to transfer
  /// [email] - Recipient's email
  /// [googleCode] - Google authenticator code
  /// [payPassword] - Payment password
  /// [username] - Recipient's username
  /// Returns a [ResponseResult] containing boolean success status
  @override
  Future<ResponseResult<bool>> transfer({
    required String amount,
    required String email,
    required String googleCode,
    required String payPassword,
    required String username,
  }) async {
    try {
      final response = await NetworkProvider().post(
        ApiEndpoints.collectionTransfer,
        data: {
          'email': email,
          'amount': amount,
          'googleCode': googleCode,
          'payPassword': payPassword,
          'username': username,
        },
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to transfer');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  /// Withdraws funds to external wallet
  /// [amount] - Amount to withdraw
  /// [addressId] - ID of withdrawal wallet address
  /// [googleCode] - Google authenticator code
  /// [payPassword] - Payment password
  /// Returns a [ResponseResult] containing boolean success status
  @override
  Future<ResponseResult<bool>> withdraw({
    required String amount,
    required int addressId,
    required String googleCode,
    required String payPassword,
  }) async {
    try {
      final response = await NetworkProvider().post(
        ApiEndpoints.withdraw,
        data: {
          'addressId': addressId,
          'amount': amount,
          'googleCode': googleCode,
          'payPassword': payPassword,
        },
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to withdraw');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  /// Previews transaction details for a given amount
  /// [amount] - Amount to preview
  /// Returns a [ResponseResult] containing [PreviewData] on success
  @override
  Future<ResponseResult<PreviewData>> preview({
    required String amount,
  }) async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.preview(amount),
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
              data: PreviewData.fromJson(response.data['data']));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to preview');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }
}
