import '../../../../core/models/result.dart';
import '../../../wallet/withdraw/domain/models/collection_supervisor/collection_supervisor_model.dart';
import '../models/preview/preview_model.dart';

abstract class TransferRepository {
  const TransferRepository();

  Future<ResponseResult<CollectionSupervisorModel>> collectionSupervisor();

  Future<ResponseResult<String>> collectionUsername({
    required String userEmail,
  });

  Future<ResponseResult<bool>> toTrading({
    required String amount,
    required String from,
    required String to,
  });

  Future<ResponseResult<bool>> transfer({
    required String amount,
    required String email,
    required String googleCode,
    required String payPassword,
    required String username,
  });

  Future<ResponseResult<bool>> withdraw({
    required String amount,
    required int addressId,
    required String googleCode,
    required String payPassword,
  });

  Future<ResponseResult<PreviewData>> preview({
    required String amount,
  });
}
