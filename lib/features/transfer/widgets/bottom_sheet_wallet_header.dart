import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/constants/string_constants.dart';
import '../../../core/theme/color_pallette.dart';
import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';

class BottomSheetWalletHeader extends StatelessWidget {
  final void Function()? onClose;

  const BottomSheetWalletHeader({super.key, this.onClose});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(blurRadius: 25, color: Colors.black.withValues(alpha: 0.05)),
        ],
      ),
      child: <PERSON><PERSON>(
        children: [
          Align(
            alignment: Alignment.center,
            child: Container(
              width: 30.w,
              height: 5.w,
              decoration: BoxDecoration(
                color: Colors.grey,
                borderRadius: BorderRadius.circular(5),
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(0, 0, 16, 0).r,
              child: GestureDetector(
                onTap: onClose ?? () {},
                child: Text(
                  StringConstants.close.tr(),
                  style: FontPalette.normal12.copyWith(
                    color: ColorPalette.greyColor3,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
