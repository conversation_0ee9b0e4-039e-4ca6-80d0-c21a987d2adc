import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/features/transfer/logic/transfer/transfer_cubit.dart';

import '../../../core/constants/assets.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/theme/color_pallette.dart';
import '../../../core/widgets/common_wallet_slider_item.dart';
import '../../home/<USER>/models/balance/balance_model.dart';


class WalletSliderTransfer extends StatefulWidget {
  final bool? isLoading;
  final double width;
  final double fullWidth;
  final double height;
  final BalanceData balanceData;
  final TextEditingController textEditingController;
  final TextEditingController emailTextEditingController;

  const WalletSliderTransfer({
    super.key,
    required this.width,
    this.isLoading,
    required this.fullWidth,
    required this.height,
    required this.balanceData,
    required this.textEditingController,
    required this.emailTextEditingController,
  });

  @override
  State<WalletSliderTransfer> createState() => _WalletSliderTransferState();
}

class _WalletSliderTransferState extends State<WalletSliderTransfer>
    with SingleTickerProviderStateMixin {
  late final AnimationController _animationController = AnimationController(
    duration: const Duration(milliseconds: 300),
    vsync: this,
  );
  late final Animation<double> _rotationAnimation = Tween<double>(
    begin: 0,
    end: pi,
  ).animate(
    CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ),
  );

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTransferTap() {
    final transferCubit = context.read<TransferCubit>();
    final isSwapped = transferCubit.state.transferSliderIndex == 1;
    isSwapped ? _animationController.reverse() : _animationController.forward();
    transferCubit.setTransferSliderIndex(isSwapped ? 0 : 1);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TransferCubit, TransferState>(
      builder: (context, state) {
        final isSwapped = state.transferSliderIndex == 1;
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _WalletCard(
                key: ValueKey(isSwapped ? 'trading' : 'funding'),
                isCommunity: isSwapped,
                title: isSwapped
                    ? StringConstants.tradingWallet
                    : StringConstants.fundingWallet,
                height: widget.height,
                width: widget.width,
                fullWidth: widget.fullWidth,
                isLoading: widget.isLoading,
                balanceData: widget.balanceData,
              ),
              _TransferButton(
                animation: _rotationAnimation,
                onTap: _handleTransferTap,
              ),
              _WalletCard(
                key: ValueKey(isSwapped ? 'funding' : 'trading'),
                isCommunity: !isSwapped,
                title: isSwapped
                    ? StringConstants.fundingWallet
                    : StringConstants.tradingWallet,
                height: widget.height,
                width: widget.width,
                fullWidth: widget.fullWidth,
                isLoading: widget.isLoading,
                balanceData: widget.balanceData,
              ),
            ],
          ),
        );
      },
    );
  }
}

class _WalletCard extends StatelessWidget {
  final bool isCommunity;
  final String title;
  final double height;
  final double width;
  final double fullWidth;
  final bool? isLoading;
  final BalanceData balanceData;

  const _WalletCard({
    super.key,
    required this.isCommunity,
    required this.title,
    required this.height,
    required this.width,
    required this.fullWidth,
    required this.isLoading,
    required this.balanceData,
  });

  @override
  Widget build(BuildContext context) => AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (child, animation) => FadeTransition(
          opacity: animation,
          child: child,
        ),
        child: Container(
          height: height,
          width: fullWidth,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: CommonWalletSliderItem(
            isLoading: isLoading,
            width: width,
            balance: balanceData,
            isCommunity: isCommunity,
            title: title.tr(),
          ),
        ),
      );
}

class _TransferButton extends StatelessWidget {
  final Animation<double> animation;
  final VoidCallback onTap;

  const _TransferButton({
    required this.animation,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) => Container(
        margin: EdgeInsets.symmetric(vertical: 16.h),
        decoration: BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Bounceable(
          scaleFactor: 0.8,
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.all(8.r),
            child: AnimatedBuilder(
              animation: animation,
              builder: (context, child) => Transform.rotate(
                angle: animation.value,
                child: Transform.rotate(
                  angle: pi / 2,
                  child: SvgPicture.asset(
                    Assets.transfer,
                    width: 24.w,
                    height: 24.h,
                    colorFilter: ColorFilter.mode(
                      myColorScheme(context).primaryColor ??
                          ColorPalette.primaryColor,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      );
}
