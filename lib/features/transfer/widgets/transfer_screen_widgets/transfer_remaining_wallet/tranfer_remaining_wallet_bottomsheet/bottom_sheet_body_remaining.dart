import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/utils/functions.dart';

import '../../../../../../core/api/network/network_helper.dart';
import '../../../../../../core/constants/enums.dart';
import '../../../../../../core/constants/string_constants.dart';
import '../../../../../../core/theme/font_pallette.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../home/<USER>/home/<USER>';
import '../../../../logic/transfer/transfer_cubit.dart';
import '../../../../domain/models/preview/preview_model.dart';

class BottomSheetRemainingBody extends StatelessWidget {
  final TextEditingController controller;
  final int walletIndex;

  const BottomSheetRemainingBody({
    super.key,
    required this.controller,
    required this.walletIndex,
  });

  bool get disablePreview => true; // disbale transfer preview
  List<String> getSteps() => (walletIndex == 1 && !disablePreview)
      ? [StringConstants.confirm.tr()]
      : [];

  @override
  Widget build(BuildContext context) => Container(
        color: myColorScheme(context).cardColor,
        child: BlocBuilder<TransferCubit, TransferState>(
          builder: (context, state) => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                children: [
                  if (walletIndex == 1 && !disablePreview)
                    Container(
                      color: myColorScheme(context).cardColor,
                      padding: EdgeInsets.only(
                        top: 8.h,
                        left: 12.w,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          for (int i = 0; i < getSteps().length; i++)
                            _StepTile(
                              title: getSteps()[i],
                              isActive: state.currentStep >= i,
                              onTap: () {
                                if (i <= state.currentStep) {
                                  context
                                      .read<TransferCubit>()
                                      .setCurrentStep(i);
                                }
                              },
                            ),
                        ],
                      ),
                    )
                  else
                    50.verticalSpace,
                  if ((walletIndex == 1 && !disablePreview) &&
                      state.currentStep == 0)
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.w),
                      child: Center(
                        child: Column(
                          children: [
                            20.verticalSpace,
                            Text(
                              StringConstants.summaryTransfer.tr(),
                              textAlign: TextAlign.center,
                              style: FontPalette.bold20,
                            ),
                            20.verticalSpace,
                            const _SummaryInfoBox(),
                          ],
                        ),
                      ),
                    ),
                  if (walletIndex == 0 ||
                      ((walletIndex == 1 && state.currentStep == 1) ||
                          (walletIndex == 1 && disablePreview))) ...[
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                      child: Center(
                        child: Text(
                          StringConstants.summaryTransfer.tr(),
                          textAlign: TextAlign.center,
                          style: FontPalette.bold20,
                        ),
                      ),
                    ),
                    43.verticalSpace,
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 37.w),
                      child: _TotalAmountRow(controller: controller),
                    ),
                  ],
                  54.verticalSpace,
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 37.w),
                    child: _ConfirmButton(
                      controller: controller,
                      walletIndex: walletIndex,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
}

class _StepTile extends StatelessWidget {
  final String title;
  final bool isActive;
  final VoidCallback onTap;
  const _StepTile({
    required this.title,
    required this.isActive,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: onTap,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              title,
              style: FontPalette.normal14.copyWith(
                color: isActive
                    ? myColorScheme(context).primaryColor
                    : myColorScheme(context).greyColor4,
              ),
            ),
            Icon(
              Icons.navigate_next,
              color: isActive
                  ? myColorScheme(context).primaryColor
                  : myColorScheme(context).greyColor3,
            ),
          ],
        ),
      );
}

class _SummaryInfoBox extends StatelessWidget {
  const _SummaryInfoBox();

  @override
  Widget build(BuildContext context) => Container(
        width: (369).w,
        height: (150).h,
        decoration: BoxDecoration(
          color: myColorScheme(context).backgroundColor,
          borderRadius: BorderRadius.circular(28.r),
          boxShadow: [
            BoxShadow(
              color:
                  myColorScheme(context).greyColor3 ?? ColorPalette.greyColor3,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Center(
          child: BlocSelector<TransferCubit, TransferState,
              (PreviewData?, DataStatus)?>(
            selector: (state) => (state.previewData, state.previewFetchStatus),
            builder: (context, value) {
              if (value == null) {
                return const SizedBox.shrink();
              }

              final (previewData, status) = value;

              if (status == DataStatus.loading) {
                return const CircularProgressIndicator();
              }

              final freeAmount = '${previewData?.freeAmount}'.toCurrency();
              final chargedAmount = '${previewData?.chargedAmount}'.toCurrency();
              final fee = '${previewData?.fee}'.toCurrency();
              final expectedReceive = '${previewData?.expectedReceive}'.toCurrency();

              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'transferFreeAmount'.tr(args: [freeAmount]),
                    textAlign: TextAlign.center,
                    style: FontPalette.normal20,
                  ),
                  Text(
                    'transferChargedAmount'.tr(args: [chargedAmount, fee]),
                    textAlign: TextAlign.center, 
                    style: FontPalette.normal20,
                  ),
                  Text(
                    'transferExpectedReceive'.tr(args: [expectedReceive]),
                    textAlign: TextAlign.center,
                    style: FontPalette.normal20,
                  ),
                ],
              );
            },
          ),
        ),
      );
}

class _TotalAmountRow extends StatelessWidget {
  final TextEditingController controller;
  const _TotalAmountRow({required this.controller});

  @override
  Widget build(BuildContext context) => Container(
        width: 369.w,
        height: 56.h,
        decoration: BoxDecoration(
          color: myColorScheme(context).backgroundColor,
          borderRadius: BorderRadius.circular(28.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              '${StringConstants.totalAmount.tr()}:',
              textAlign: TextAlign.center,
              style: FontPalette.normal20,
            ),
            10.horizontalSpace,
            Text(
              parseUSDAmount(controller.text).toStringAsFixed(2),
              textAlign: TextAlign.center,
              style: FontPalette.semiBold20.copyWith(
                color: myColorScheme(context).primaryColor,
              ),
            ),
          ],
        ),
      );
}

class _ConfirmButton extends StatelessWidget {
  final TextEditingController controller;
  final int walletIndex;
  const _ConfirmButton({
    required this.controller,
    required this.walletIndex,
  });

  @override
  Widget build(BuildContext context) =>
      BlocListener<TransferCubit, TransferState>(
        listenWhen: (previous, current) =>
            previous.toTradingFetchStatus != current.toTradingFetchStatus,
        listener: (context, state) {
          if (state.toTradingFetchStatus == DataStatus.failed) {
            NetworkHelper.handleMessage(
              state.error,
              context,
              type: HandleTypes.customDialog,
              snackBarType: SnackBarType.error,
            );
          }
          if (state.toTradingFetchStatus == DataStatus.success) {
            context.read<HomeCubit>().getBalance();
            controller.clear();
          }
        },
        child: BlocSelector<TransferCubit, TransferState, DataStatus>(
          selector: (state) => state.toTradingFetchStatus,
          builder: (context, status) => CustomButton(
            width: (350).w,
            height: (56).h,
            label: StringConstants.confirm.tr(),
            isOutlined: false,
            isLoading: status == DataStatus.loading,
            onPressed: () => context.read<TransferCubit>().toTrading(
                  parseUSDAmount(controller.text).toString(),
                  walletIndex,
                ),
          ),
        ),
      );
}
