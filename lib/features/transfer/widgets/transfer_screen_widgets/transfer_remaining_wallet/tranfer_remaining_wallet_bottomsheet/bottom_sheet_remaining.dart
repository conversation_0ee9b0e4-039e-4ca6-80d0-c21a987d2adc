import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/constants/enums.dart';

import '../../../../../../core/constants/assets.dart';
import '../../../../../../core/constants/string_constants.dart';
import '../../../../../../core/theme/font_pallette.dart';
import '../../../../../../core/theme/my_color_scheme.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../logic/transfer/transfer_cubit.dart';
import 'bottom_sheet_body_remaining.dart';

class BottomSheetRemaining extends StatefulWidget {
  final VoidCallback onClose;
  final TextEditingController controller;
  final int walletIndex;

  const BottomSheetRemaining({
    super.key,
    required this.onClose,
    required this.controller,
    required this.walletIndex,
  });

  @override
  State<BottomSheetRemaining> createState() => BottomSheetRemainingState();
}

class BottomSheetRemainingState extends State<BottomSheetRemaining> {
  @override
  void initState() {
    super.initState();
    context.read<TransferCubit>().resetTransfer();
  }

  @override
  Widget build(BuildContext context) {
    return BlocSelector<TransferCubit, TransferState, DataStatus>(
      selector: (transferState) => transferState.toTradingFetchStatus,
      builder: (context, status) {
        return Container(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom > 0
                ? MediaQuery.of(context).viewInsets.bottom
                : 20.h,
          ),
          decoration: BoxDecoration(
            color: myColorScheme(context).cardColor,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(20.r),
            ),
          ),
          child: ListView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _BuildHeader(onClose: widget.onClose),
              status == DataStatus.success
                  ? _BuildSuccessView(
                      onClose: widget.onClose,
                      textEditingController: widget.controller,
                    )
                  : _BuildMainContent(
                      controller: widget.controller,
                      walletIndex: widget.walletIndex,
                    ),
            ],
          ),
        );
      },
    );
  }
}

class _BuildHeader extends StatelessWidget {
  final VoidCallback onClose;

  const _BuildHeader({
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40.h,
      child: Stack(
        children: [
          Center(
            child: Container(
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: myColorScheme(context).borderColor,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: IconButton(
              icon: const Icon(Icons.close),
              onPressed: onClose,
            ),
          ),
        ],
      ),
    );
  }
}

class _BuildSuccessView extends StatelessWidget {
  final VoidCallback onClose;
  final TextEditingController? textEditingController;

  const _BuildSuccessView({
    required this.onClose,
    required this.textEditingController,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
      ),
      child: Column(
        children: [
          87.verticalSpace,
          CircleAvatar(
            backgroundColor: Colors.transparent,
            radius: 60.r,
            child: SvgPicture.asset(Assets.alertSuccess),
          ),
          Text(
            StringConstants.transferred.tr(),
            style: FontPalette.medium18,
          ),
          Text(
            StringConstants.successfully.tr(),
            textAlign: TextAlign.center,
            style: FontPalette.semiBold40,
          ),
          54.verticalSpace,
          Center(
            child: CustomButton(
              width: 384,
              height: 56,
              label: StringConstants.done.tr(),
              isOutlined: false,
              onPressed: () {
                onClose();
                Navigator.pop(context);
                textEditingController?.clear();
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _BuildMainContent extends StatelessWidget {
  final TextEditingController controller;
  final int walletIndex;

  const _BuildMainContent({
    required this.controller,
    required this.walletIndex,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TransferCubit, TransferState>(
      builder: (context, state) {
        return SingleChildScrollView(
          child: Container(
            color: myColorScheme(context).cardColor,
            child: BottomSheetRemainingBody(
              controller: controller,
              walletIndex: walletIndex,
            ),
          ),
        );
      },
    );
  }
}
