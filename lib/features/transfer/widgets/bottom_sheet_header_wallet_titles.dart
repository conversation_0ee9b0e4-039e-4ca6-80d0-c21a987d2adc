import 'package:flutter/material.dart';

import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';

class BottomSheetWalletHeaderTitle extends StatelessWidget {
  final bool? active;
  final String title;
  final void Function()? onTap;

  const BottomSheetWalletHeaderTitle({
    super.key,
    this.active,
    this.onTap,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () {},
      child: Row(
        children: [
          Text(
            title,
            style: (active ?? false)
                ? FontPalette.semiBold12
                    .copyWith(color: myColorScheme(context).primaryColor)
                : FontPalette.semiBold12
                    .copyWith(color: myColorScheme(context).greyColor3),
          ),
          Icon(
            Icons.navigate_next,
            color: (active ?? false)
                ? myColorScheme(context).primaryColor
                : myColorScheme(context).greyColor3,
          ),
        ],
      ),
    );
  }
}
