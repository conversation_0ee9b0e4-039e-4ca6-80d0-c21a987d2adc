import 'package:flutter/material.dart';

class BackdropBlurAnimation extends StatefulWidget {
  final Widget child;

  const BackdropBlurAnimation({
    super.key,
    required this.child,
  });

  @override
  State<BackdropBlurAnimation> createState() => BackdropBlurAnimationState();
}

class BackdropBlurAnimationState extends State<BackdropBlurAnimation>
    with TickerProviderStateMixin {
  late final AnimationController _controller = AnimationController(
    duration: const Duration(milliseconds: 500),
    vsync: this,
  );

  late Animation<double> fadeAnimation;
  late Animation<double> slideAnimation;

  @override
  void initState() {
    super.initState();
    fadeAnimation = Tween(begin: 0.0, end: 1.0)
        .animate(CurvedAnimation(parent: _controller, curve: Curves.linear));
    slideAnimation = Tween(begin: -25.0, end: 0.0)
        .animate(CurvedAnimation(parent: _controller, curve: Curves.linear));
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: slideAnimation,
      child: widget.child,
      builder: (BuildContext context, Widget? child) {
        return Transform.translate(
          offset: Offset(0.0, slideAnimation.value),
          child: FadeTransition(
            opacity: fadeAnimation,
            child: child,
          ),
        );
      },
    );
  }
}
