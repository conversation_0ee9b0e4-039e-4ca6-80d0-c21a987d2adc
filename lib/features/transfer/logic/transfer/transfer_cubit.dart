import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';

import '../../../wallet/withdraw/domain/models/collection_withdraw/collection_withdraw_model.dart';
import '../../domain/models/preview/preview_model.dart';
import '../../domain/repository/transfer_repository.dart';

part 'transfer_state.dart';

@injectable
class TransferCubit extends Cubit<TransferState> {
  TransferCubit(this._transferService) : super(const TransferState());
  final TransferRepository _transferService;

  void setTransferSliderIndex(int index) {
    emit(state.copyWith(transferSliderIndex: index));
  }


  void resetTransfer() {
    emit(
      state.copyWith(
        showWithdrawSuccess: false,
        toTradingFetchStatus: DataStatus.idle,
      ),
    );
  }

  void resetErrorMsg() {
    emit(
      state.copyWith(
        showTransferError: false,
        resetErrorMsg: true,
        resetUserName: true,
      ),
    );
  }

  void setCurrentSheetIndex(int index) {
    emit(state.copyWith(currentSheetIndex: index));
  }

  void setTransferShowError(bool value) {
    emit(state.copyWith(showTransferError: value));
  }

  void setCollectionWithdrawData(CollectionWithdrawData data) {
    emit(state.copyWith(collectionWithdrawData: data));
  }

  void setWithdrawFetchStatus(DataStatus status) {
    emit(state.copyWith(withdrawFetchStatus: status));
  }

  void setShowWithdrawSuccess(bool value) {
    emit(state.copyWith(showWithdrawSuccess: value));
  }

  Future<void> withdraw({
    required String googleCode,
    required String payPassword,
  }) async {
    emit(state.copyWith(withdrawFetchStatus: DataStatus.loading));
    try {
      var response = await _transferService.withdraw(
        amount: state.amount!,
        addressId: state.selectedAddress ?? 0,
        googleCode: googleCode,
        payPassword: payPassword,
      );
      if (response.data != null) {
        emit(
          state.copyWith(
            showWithdrawSuccess: true,
            withdrawFetchStatus: DataStatus.success,
          ),
        );
        // getCollectionBalance(context);
      } else {
        emit(
          state.copyWith(
            withdrawFetchStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          withdrawFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> getCollectionSupervisor() async {
    emit(state.copyWith(collectionSupervisorFetchStatus: DataStatus.loading));
    try {
      var response = await _transferService.collectionSupervisor();
      if (response.data != null) {
        if (response.data?.code == 500) {
          emit(
            state.copyWith(
              userName: null,
              errorMsg: response.error,
              collectionSupervisorFetchStatus: DataStatus.failed,
            ),
          );
        } else {
          emit(
            state.copyWith(
              userName: response.data?.data.username,
              collectionSupervisorFetchStatus: DataStatus.success,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            collectionSupervisorFetchStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          collectionSupervisorFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> getCollectionUsername(String email) async {
    emit(state.copyWith(collectionUserNameFetchStatus: DataStatus.loading));
    try {
      var response = await _transferService.collectionUsername(
        userEmail: Uri.encodeQueryComponent(email),
      );
      if (response.data != null) {
        emit(
          state.copyWith(
            userName: response.data,
            collectionUserNameFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            collectionUserNameFetchStatus: DataStatus.failed,
            error: response.error,
            showTransferError: true,
            errorMsg: response.error,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          collectionUserNameFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> toTrading(String amount, int walletIndex) async {
    emit(state.copyWith(toTradingFetchStatus: DataStatus.loading));
    try {
      // Get the source wallet type
      final String fromWalletType = manageWalletType(walletIndex) ?? '';

      // Get the destination wallet type (opposite of source)
      final String toWalletType = manageWalletType(walletIndex == 0 ? 1 : 0) ?? '';

      var response = await _transferService.toTrading(
        amount: amount,
        from: fromWalletType,
        to: toWalletType,
      );
      if (response.data ?? false) {
        emit(
          state.copyWith(
            showWithdrawSuccess: true,
            toTradingFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            toTradingFetchStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          toTradingFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  String? manageWalletType(int walletIndex) {
    switch (walletIndex) {
      case 0:
        return 'CASH';
      case 1:
        return 'COMMUNITY';
      default:
        return 'CASH';
    }
  }

  Future<void> transfer({
    required String amount,
    required String email,
    required String googleCode,
    required String payPassword,
    required String username,
  }) async {
    emit(state.copyWith(transferFetchStatus: DataStatus.loading));
    try {
      var response = await _transferService.transfer(
        amount: amount,
        email: email,
        googleCode: googleCode,
        payPassword: payPassword,
        username: username,
      );
      if (response.data ?? false) {
        emit(
          state.copyWith(
            transferFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            transferFetchStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          transferFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> preview({required String amount}) async {
    emit(state.copyWith(previewFetchStatus: DataStatus.loading));
    try {
      var response = await _transferService.preview(amount: amount);
      if (response.data != null) {
        emit(
          state.copyWith(
            previewData: response.data,
            previewFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            previewFetchStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          previewFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  void setCurrentStep(int step) => emit(state.copyWith(currentStep: step));
}
