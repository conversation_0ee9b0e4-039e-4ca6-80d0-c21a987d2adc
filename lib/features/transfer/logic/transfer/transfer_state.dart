part of 'transfer_cubit.dart';

class TransferState extends Equatable {
  final int transferSliderIndex;
  final bool showTransferError;
  final String? errorMsg;
  final String? userName;
  final bool showWithdrawSuccess;
  final int currentSheetIndex;
  final double withdrawalFee;
  final CollectionWithdrawData? collectionWithdrawData;
  final String? amount;
  final DataStatus withdrawFetchStatus;
  final DataStatus collectionSupervisorFetchStatus;
  final DataStatus collectionUserNameFetchStatus;
  final DataStatus toTradingFetchStatus;
  final DataStatus transferFetchStatus;
  final int? selectedAddress;
  final String? error;
  final PreviewData? previewData;
  final DataStatus previewFetchStatus;
  final int currentStep;
  const TransferState({
    this.transferSliderIndex = 0,
    this.showTransferError = false,
    this.showWithdrawSuccess = false,
    this.errorMsg,
    this.userName,
    this.currentSheetIndex = 0,
    this.withdrawalFee = 0,
    this.collectionWithdrawData,
    this.amount,
    this.withdrawFetchStatus = DataStatus.idle,
    this.collectionSupervisorFetchStatus = DataStatus.idle,
    this.collectionUserNameFetchStatus = DataStatus.idle,
    this.toTradingFetchStatus = DataStatus.idle,
    this.transferFetchStatus = DataStatus.idle,
    this.selectedAddress,
    this.error,
    this.previewData,
    this.previewFetchStatus = DataStatus.idle,
    this.currentStep = 0,
  });

  @override
  List<Object?> get props => [
        transferSliderIndex,
        showTransferError,
        errorMsg,
        userName,
        showWithdrawSuccess,
        currentSheetIndex,
        withdrawalFee,
        collectionWithdrawData,
        withdrawFetchStatus,
        amount,
        collectionSupervisorFetchStatus,
        collectionUserNameFetchStatus,
        toTradingFetchStatus,
        transferFetchStatus,
        selectedAddress,
        error,
        previewData,
        previewFetchStatus,
        currentStep,
      ];

  TransferState copyWith({
    int? transferSliderIndex,
    bool? showTransferError,
    bool resetErrorMsg = false,
    bool resetUserName = false,
    String? errorMsg,
    String? userName,
    bool? showWithdrawSuccess,
    int? currentSheetIndex,
    double? withdrawalFee,
    CollectionWithdrawData? collectionWithdrawData,
    DataStatus? withdrawFetchStatus,
    String? amount,
    DataStatus? collectionSupervisorFetchStatus,
    DataStatus? collectionUserNameFetchStatus,
    DataStatus? toTradingFetchStatus,
    DataStatus? transferFetchStatus,
    int? selectedAddress,
    String? error,
    PreviewData? previewData,
    DataStatus? previewFetchStatus,
    int? currentStep,
  }) {
    return TransferState(
      transferSliderIndex: transferSliderIndex ?? this.transferSliderIndex,
      showTransferError: showTransferError ?? this.showTransferError,
      errorMsg: resetErrorMsg ? null : errorMsg ?? this.errorMsg,
      userName: resetUserName ? null : userName ?? this.userName,
      showWithdrawSuccess: showWithdrawSuccess ?? this.showWithdrawSuccess,
      currentSheetIndex: currentSheetIndex ?? this.currentSheetIndex,
      withdrawalFee: withdrawalFee ?? this.withdrawalFee,
      collectionWithdrawData:
          collectionWithdrawData ?? this.collectionWithdrawData,
      withdrawFetchStatus: withdrawFetchStatus ?? this.withdrawFetchStatus,
      amount: amount ?? this.amount,
      collectionSupervisorFetchStatus: collectionSupervisorFetchStatus ??
          this.collectionSupervisorFetchStatus,
      collectionUserNameFetchStatus:
          collectionUserNameFetchStatus ?? this.collectionUserNameFetchStatus,
      toTradingFetchStatus: toTradingFetchStatus ?? this.toTradingFetchStatus,
      transferFetchStatus: transferFetchStatus ?? this.transferFetchStatus,
      selectedAddress: selectedAddress ?? this.selectedAddress,
      error: error ?? this.error,
      previewData: previewData ?? this.previewData,
      previewFetchStatus: previewFetchStatus ?? this.previewFetchStatus,
      currentStep: currentStep ?? this.currentStep,
    );
  }
}
