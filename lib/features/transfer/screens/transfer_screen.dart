import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../core/common_function.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/shared/logic/chat_button/chat_button_cubit.dart';
import '../../home/<USER>/models/balance/balance_model.dart';
import '../../home/<USER>/home/<USER>';
import '../logic/transfer/transfer_cubit.dart';
import '../widgets/transfer_screen_widgets/transfer_remaining_wallet/tranfer_remaining_wallet_bottomsheet/bottom_sheet_remaining.dart';
import '../widgets/transfer_screen_widgets/transfer_remaining_wallet/transfer_remaining_body.dart';
import '../widgets/wallet_slider_transfer.dart';

class TransferScreen extends StatefulWidget {
  const TransferScreen({super.key});

  @override
  State<TransferScreen> createState() => _TransferScreenState();
}

class _TransferScreenState extends State<TransferScreen> {
  final _amountController = TextEditingController();
  final _emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeState();
  }

  void _initializeState() {
    CommonFunctions.afterInit(() {
      context.read<TransferCubit>()
        ..setTransferSliderIndex(0)
        ..resetErrorMsg();
    });
  }

  void _showTransferBottomSheet(int walletIndex) {
    try {
      showModalBottomSheet<void>(
        context: context,
        isScrollControlled: true,
        enableDrag: true,
        backgroundColor: Colors.transparent,
        builder: (context) => BottomSheetRemaining(
          onClose: () {
            Navigator.pop(context);
            context
                .read<ChatButtonCubit>()
                .toggleChatButton(showChatButton: true);
          },
          controller: _amountController,
          walletIndex: walletIndex,
        ),
      );
    } catch (e) {
      debugPrint('Error showing BottomSheet: $e');
    }
  }

  @override
  void dispose() {
    _amountController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: RefreshIndicator(
        onRefresh: () => context.read<HomeCubit>().getBalance(),
        child: _buildBody(),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: Text(StringConstants.transfer.tr()),
      centerTitle: true,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(Icons.arrow_back_ios_new_rounded),
      ),
      backgroundColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
    );
  }

  Widget _buildBody() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: constraints.maxHeight,
            ),
            child: BlocSelector<HomeCubit, HomeState, BalanceModel?>(
              selector: (state) => state.balanceData,
              builder: (context, balanceData) {
                final transferState = context.watch<TransferCubit>().state;
                final walletIndex = transferState.transferSliderIndex;
                final showError = transferState.showTransferError;

                return SafeArea(
                  child: AnimationLimiter(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildWalletSlider(balanceData),
                        if (walletIndex != 3)
                          _buildTransferRemainingBody(
                            balanceData: balanceData,
                            walletIndex: walletIndex,
                            showError: showError,
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildWalletSlider(BalanceModel? balanceData) {
    return WalletSliderTransfer(
      balanceData: balanceData?.data ?? const BalanceData(),
      textEditingController: _amountController,
      emailTextEditingController: _emailController,
      fullWidth: 1.sw,
      width: 377.w,
      height: 121.h,
    );
  }

  Widget _buildTransferRemainingBody({
    required BalanceModel? balanceData,
    required int walletIndex,
    required bool showError,
  }) {
    final walletPrice = walletIndex == 0
        ? double.tryParse(balanceData?.data.cash ?? '0') ?? 0
        : balanceData?.data.community?.availableBalance ?? 0;

    return TransferRemainingBody(
      amountController: _amountController,
      walletIndex: walletIndex,
      walletBalance: walletPrice,
      showError: showError,
      onTransferPressed: () => _showTransferBottomSheet(walletIndex),
    );
  }
}
