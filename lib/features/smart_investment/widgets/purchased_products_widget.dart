import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/common_empty_data.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';
import 'package:sf_app_v2/features/smart_investment/domain/models/purchased_list/purchased_list.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/utils/utils.dart';

class PurchasedProductsWidget extends StatelessWidget {
  final List<PurchasedListData>? purchasedList;
  final DataStatus status;
  final VoidCallback? onRefresh;

  const PurchasedProductsWidget({
    super.key,
    this.purchasedList,
    required this.status,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    if (status == DataStatus.loading) {
      return _buildShimmerList();
    }

    if (purchasedList == null || purchasedList!.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: purchasedList?.length ?? 0,
      itemBuilder: (context, index) {
        final product = purchasedList![index];
        return PurchasedProductCard(product: product);
      },
    );
  }

  Widget _buildShimmerList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.all(16.r),
      itemCount: 3,
      itemBuilder: (_, __) => _buildShimmerCard(),
    );
  }

  Widget _buildShimmerCard() {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          // Header with order number and status
          Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonShimmer(
                  width: 200.w,
                  height: 16.h,
                  br: 4.r,
                ),
                CommonShimmer(
                  width: 80.w,
                  height: 24.h,
                  br: 12.r,
                ),
              ],
            ),
          ),
          // Details rows
          Container(
            padding: EdgeInsets.all(16.r),
            child: Column(
              children: List.generate(
                3,
                (index) => Padding(
                  padding: EdgeInsets.only(bottom: 12.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CommonShimmer(
                        width: 120.w,
                        height: 14.h,
                        br: 4.r,
                      ),
                      CommonShimmer(
                        width: 100.w,
                        height: 14.h,
                        br: 4.r,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: CommonEmpty(),
    );
  }
}

class PurchasedProductCard extends StatelessWidget {
  final PurchasedListData product;

  const PurchasedProductCard({
    super.key,
    required this.product,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with order number and status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    '${StringConstants.orderNo.tr()}: ${product.orderNo}',
                    style: FontPalette.normal12.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                _buildStatusBadge(context),
              ],
            ),
            // Stock name
            Row(
              children: [
                Text(
                  '${StringConstants.stockName.tr()}:',
                  style: FontPalette.bold14,
                ),
                Text(
                  product.stockName.isNotEmpty ? product.stockName : ' Pending',
                  style: FontPalette.semiBold14,
                ),
              ],
            ),
            // Details rows
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                _buildInfoRow(
                  context,
                  '${StringConstants.orderDate.tr()}: ${_formatDate(product.createTime)}',
                  '${StringConstants.investmentAmount.tr()}: ${product.orderAmount.toStringAsFixed(2).toCurrency()}',
                ),
                5.verticalSpace,
                _buildInfoRow(
                  context,
                  '${StringConstants.buyPrice.tr()}: ${_formatPendingValue(product.buyPrice)}',
                  '${StringConstants.sellPrice.tr()}: ${_formatPendingValue(product.sellPrice)}',
                ),
                5.verticalSpace,
                Row(
                  children: [
                    Text(
                      '${StringConstants.profit.tr()}: ',
                      style: FontPalette.bold14.copyWith(
                        color: _getProfitColor(product.totalProfit),
                      ),
                    ),
                    Builder(
                      builder: (context) {
                        final totalProfit = product.totalProfit as double?;
                        final profitText = totalProfit == null
                            ? ''
                            : totalProfit > 0
                                ? '+ '
                                : '- ';
                        final profitValue = totalProfit == null
                            ? '+${AppConstants.currencySymbol} 0.00'
                            : '${AppConstants.currencySymbol} ${totalProfit.abs().toStringAsFixed(2)}';

                        return Text(
                          profitText + profitValue,
                          style: FontPalette.semiBold16.copyWith(
                            color: _getProfitColor(totalProfit),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.r, vertical: 4.r),
      decoration: BoxDecoration(
        color: getStatusColor(product.status),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Text(
        getStatusText(product.status).tr(),
        style: FontPalette.normal12.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildInfoRow(
      BuildContext context, String leftText, String rightText) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            leftText,
            style: FontPalette.normal12,
          ),
        ),
        Text(
          rightText,
          style: FontPalette.normal12,
          textAlign: TextAlign.right,
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatPendingValue(dynamic value) {
    if (value == null) {
      return StringConstants.pending2.tr();
    }
    if (value is num) {
      return value.toStringAsFixed(2).toCurrency();
    }
    return StringConstants.pending2;
  }

  Color _getProfitColor(dynamic profit) {
    if (profit == null) {
      return ColorPalette.pendingColor;
    }
    if (profit is num) {
      return profit > 0
          ? const Color.fromARGB(255, 0, 128, 0)
          : ColorPalette.deniedColor;
    }
    return ColorPalette.pendingColor;
  }
}
