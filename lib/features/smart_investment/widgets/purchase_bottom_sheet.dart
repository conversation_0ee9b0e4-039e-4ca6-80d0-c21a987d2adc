import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/extention.dart';
// import 'package:sf_app_v2/features/chat_v2/utils/theme.dart';
import 'package:sf_app_v2/features/smart_investment/widgets/wallet_slider.dart';
// import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

import '../../../core/common_function.dart';
import '../../../core/config/app_config.dart';
import '../../../core/dependency_injection/injectable.dart';
import '../../../core/constants/assets.dart';
import '../../../core/constants/enums.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';
// import '../../../core/utils/log.dart';
import '../../../core/widgets/common_pin_field.dart';
import '../../../core/widgets/common_wallet_slider_item.dart';
import '../../../core/widgets/custom_button.dart';
import '../../home/<USER>/models/balance/balance_model.dart';
import '../../home/<USER>/home/<USER>';
import '../domain/models/Investment_response/investment_respone.dart';
import '../logic/smart_investment/smart_investment_cubit.dart';

class PurchaseBottomSheet extends StatefulWidget {
  final VoidCallback onClose;
  final double amount;
  final String productId;
  final String? orderNo;
  final InvestmentType type;
  final int? mentorId;

  const PurchaseBottomSheet({
    super.key,
    required this.onClose,
    required this.amount,
    required this.productId,
    this.orderNo,
    this.type = InvestmentType.newInvestment,
    this.mentorId,
  });

  @override
  State<PurchaseBottomSheet> createState() => _PurchaseBottomSheetState();
}

class _PurchaseBottomSheetState extends State<PurchaseBottomSheet> {
  final TextEditingController walletPasswordController =
      TextEditingController();
  final _formKey = GlobalKey<FormState>();

  void getBalance() => context.read<HomeCubit>().getBalance();
  void getPurchasedList() =>
      context.read<SmartInvestmentCubit>().getPurchasedList(
            mentorId: widget.mentorId,
          );

  Future<void> handleInvestmentAction() async {
    if (!_formKey.currentState!.validate() || !context.mounted) return;

    Future<bool> Function() action = switch (widget.type) {
      InvestmentType.newInvestment => () =>
          context.read<SmartInvestmentCubit>().submitInvestment(
                password: walletPasswordController.text,
              ),
      InvestmentType.append => () =>
          context.read<SmartInvestmentCubit>().appendInvestment(
                password: walletPasswordController.text,
                orderId: int.parse(widget.productId),
              ),
      InvestmentType.withdraw => () =>
          context.read<SmartInvestmentCubit>().withdrawInvestment(
                password: walletPasswordController.text,
                orderId: widget.orderNo ?? widget.productId,
              ),
      InvestmentType.community => () =>
          context.read<SmartInvestmentCubit>().submitCommunityInvestment(
                password: walletPasswordController.text,
              ),
    };

    if (await action()) {
      getBalance();
      if (widget.mentorId != null) {
        getPurchasedList();
      }
    }
  }

  @override
  void initState() {
    super.initState();
    context.read<SmartInvestmentCubit>().resetBottomSheet();
  }

  @override
  void dispose() {
    walletPasswordController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<SmartInvestmentCubit, SmartInvestmentState>(
      listener: (context, state) {
        if (state.submitInvestmentStatus == DataStatus.success) {
          Future.delayed(const Duration(seconds: 3), () {
            if (mounted) {
              widget.onClose();
            }
          });
        }
      },
      child: BlocSelector<SmartInvestmentCubit, SmartInvestmentState,
          (DataStatus, DataStatus, DataStatus)>(
        selector: (state) => (
          state.submitInvestmentStatus,
          state.appendInvestmentStatus,
          state.withdrawInvestmentStatus
        ),
        builder: (context, state) {
          return Container(
             padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom > 0 
                  ? MediaQuery.of(context).viewInsets.bottom 
                  : 20.h,
            ),
            decoration: BoxDecoration(
              color: myColorScheme(context).cardColor,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(20.r),
              ),
            ),
            child: ListView(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _BuildHeader(widget: widget),
                (state.$1 == DataStatus.success ||
                        state.$2 == DataStatus.success ||
                        state.$3 == DataStatus.success)
                    ? _BuildSuccessView(
                        type: widget.type, onClose: widget.onClose)
                    : _BuildMainContent(
                        type: widget.type,
                        walletPasswordController: walletPasswordController,
                        formKey: _formKey,
                        onPressed: () => handleInvestmentAction(),
                      ),
              ],
            ),
          );
        },
      ),
    );
  }
}

class _BuildHeader extends StatelessWidget {
  const _BuildHeader({
    required this.widget,
  });

  final PurchaseBottomSheet widget;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40.h,
      child: Stack(
        children: [
          Center(
            child: Container(
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: myColorScheme(context).borderColor,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: IconButton(
              icon: const Icon(Icons.close),
              onPressed: () {
                Navigator.pop(context);
                widget.onClose();
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _BuildSuccessView extends StatelessWidget {
  const _BuildSuccessView({
    required this.type,
    required this.onClose,
  });
  final InvestmentType? type;
  final VoidCallback onClose;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
      ),
      child: Column(
        children: [
          87.verticalSpace,
          CircleAvatar(
            backgroundColor: Colors.transparent,
            radius: 60.r,
            child: SvgPicture.asset(Assets.alertSuccess),
          ),
          Text(
            type == InvestmentType.withdraw
                ? StringConstants.withdrawal.tr()
                : StringConstants.purchase.tr(),
            style: FontPalette.medium18,
          ),
          Text(
            StringConstants.successful.tr(),
            style: FontPalette.semiBold40,
          ),
          54.verticalSpace,
          BlocSelector<SmartInvestmentCubit, SmartInvestmentState,
              (DataStatus, InvestmentResponse?)>(
            selector: (state) =>
                (state.submitInvestmentStatus, state.purchaseProduct),
            builder: (context, state) {
              return CustomButton(
                width: 384.w,
                height: 56.h,
                label: StringConstants.done.tr(),
                onPressed: () {
                  onClose();
                  Navigator.pop(context);
                },
                child: type == InvestmentType.newInvestment
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Icons.share,
                            color: Colors.white,
                            size: 20,
                          ),
                          10.horizontalSpace,
                          Text(StringConstants.share.tr(),
                              style: FontPalette.semiBold18
                                  .copyWith(color: Colors.white)),
                        ],
                      )
                    : Text(StringConstants.done.tr()),
              );
            },
          ),
        ],
      ),
    );
  }
}

class _BuildMainContent extends StatelessWidget {
  final InvestmentType type;
  final TextEditingController walletPasswordController;
  final GlobalKey<FormState> formKey;
  final VoidCallback onPressed;
  const _BuildMainContent({
    required this.type,
    required this.walletPasswordController,
    required this.formKey,
    required this.onPressed,
  });
  List<String> getSteps() {
    return switch (type) {
      InvestmentType.newInvestment => [
          StringConstants.paymentWallet.tr(),
          StringConstants.confirm.tr(),
        ],
      InvestmentType.append => [
          StringConstants.paymentWallet.tr(),
          StringConstants.confirm.tr(),
        ],
      InvestmentType.withdraw => [StringConstants.confirm.tr()],
      InvestmentType.community => [
          StringConstants.paymentWallet.tr(),
          StringConstants.walletPassword.tr()
        ],
    };
  }

  String getActionTitle() {
    return switch (type) {
      InvestmentType.newInvestment => StringConstants.confirmInvestment.tr(),
      InvestmentType.append => StringConstants.additionalInvestment.tr(),
      InvestmentType.withdraw => StringConstants.withdrawInvestment.tr(),
      InvestmentType.community => StringConstants.confirmInvestment.tr(),
    };
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
      builder: (context, state) {
        return SingleChildScrollView(
          child: Container(
            color: myColorScheme(context).cardColor,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (type != InvestmentType.withdraw)
                  Container(
                    color: myColorScheme(context).cardColor,
                    padding: EdgeInsets.only(
                      top: 8.h,
                      left: 12.w,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        for (int i = 0; i < getSteps().length; i++)
                          _BuildStepTile(
                            title: getSteps()[i],
                            isActive: state.currentStep >= i,
                            onTap: () {
                              if (i <= state.currentStep) {
                                walletPasswordController.clear();
                                context.read<SmartInvestmentCubit>().setStep(i);
                              }
                            },
                          ),
                      ],
                    ),
                  ),
                if (state.currentStep == 0)
                  _BuildWalletDisplay(type: type)
                else
                  Column(
                    children: [
                      16.verticalSpace,
                      Padding(
                        padding: EdgeInsets.fromLTRB(20.w, 10.h, 20.w, 0),
                        child: Text(
                          getActionTitle(),
                          style: FontPalette.semiBold18,
                        ),
                      ),
                      8.verticalSpace,
                      _BuildAmountDisplay(type: type),
                      10.verticalSpace,
                      _BuildPasswordInput(
                        controller: walletPasswordController,
                        formKey: formKey,
                      ),
                      20.verticalSpace,
                      _BuildConfirmButton(onPressed: onPressed),
                      50.verticalSpace,
                    ],
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _BuildStepTile extends StatelessWidget {
  final String title;
  final bool isActive;
  final VoidCallback onTap;
  const _BuildStepTile({
    required this.title,
    required this.isActive,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            title,
            style: FontPalette.normal14.copyWith(
              color: isActive
                  ? myColorScheme(context).primaryColor
                  : myColorScheme(context).greyColor4,
            ),
          ),
          Icon(
            Icons.navigate_next,
            color: (isActive)
                ? myColorScheme(context).primaryColor
                : myColorScheme(context).greyColor3,
          ),
        ],
      ),
    );
  }
}

class _BuildWalletDisplay extends StatelessWidget {
  final InvestmentType type;
  const _BuildWalletDisplay({
    required this.type,
  });
  bool _isEnabled(BuildContext context, BalanceData? balance) {
    if (type == InvestmentType.community) {
      if (!getIt<AppConfig>().showTradingWallet) {
        return (balance != null &&
            balance.cash != null &&
            double.parse(balance.cash ?? '0') > 0);
      }
      return (balance != null &&
          balance.community?.totalAmount != null &&
          balance.community!.totalAmount! > 0);
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        16.verticalSpace,
        Padding(
          padding: EdgeInsets.fromLTRB(20.w, 10.h, 0, 0),
          child: Text(
            StringConstants.availableBalance.tr(),
            style: FontPalette.semiBold18,
          ),
        ),
        20.verticalSpace,
        BlocSelector<HomeCubit, HomeState, BalanceModel?>(
          selector: (state) => state.balanceData,
          builder: (_, balance) {
            if (balance != null) {
              switch (type) {
                case InvestmentType.newInvestment:
                  return InvestmentWalletSlider(
                    fullWidth: 1.sw,
                    width: 377.w,
                    height: 205.h,
                    balance: balance.data,
                  );
                case InvestmentType.withdraw:
                case InvestmentType.append:
                  return Center(
                    child: Padding(
                      padding: EdgeInsets.only(bottom: 10.h),
                      child: CommonWalletSliderItem(
                        width: 377.w,
                        height: 213.w,
                        balance: balance.data,
                        title: StringConstants.fundingWallet,
                      ),
                    ),
                  );
                case InvestmentType.community:
                  return Center(
                    child: Padding(
                      padding: EdgeInsets.only(bottom: 10.h),
                      child: CommonWalletSliderItem(
                        width: 377.w,
                        height: 213.w,
                        balance: balance.data,
                        isCommunity: getIt<AppConfig>().fetchCommunityRecords,
                        title: getIt<AppConfig>().showTradingWallet
                            ? StringConstants.tradingWallet
                            : StringConstants.fundingWallet,
                      ),
                    ),
                  );
              }
            } else {
              return Text(StringConstants.noBalanceInformation.tr());
            }
          },
        ),
        10.verticalSpace,
        Center(
          child: BlocSelector<HomeCubit, HomeState, BalanceModel?>(
            selector: (state) => state.balanceData,
            builder: (_, balance) {
              return CustomButton(
                width: 377.w,
                height: 50.h,
                label: StringConstants.next.tr(),
                isEnabled: _isEnabled(context, balance?.data),
                onPressed: () => _handleNext(balance, context),
              );
            },
          ),
        ),
      ],
    );
  }

  void _handleNext(BalanceModel? balance, BuildContext context) {
    if (balance?.data == null) {
      CommonFunctions()
          .showFlutterToast(StringConstants.noBalanceInformation.tr());
      return;
    }
    if (type == InvestmentType.community) {
      if ((balance?.data.community?.availableBalance ?? 0) <= 0) {
        CommonFunctions()
            .showFlutterToast(StringConstants.noBalanceAvailable.tr());
        return;
      } else {
        context.read<SmartInvestmentCubit>()
          ..setStep(1)
          ..updateWalletSelection(isCommunity: true);
      }
    }
  }
}

class _BuildAmountDisplay extends StatelessWidget {
  final InvestmentType type;
  const _BuildAmountDisplay({
    required this.type,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      padding: EdgeInsets.all(16.w),
      width: double.infinity,
      decoration: BoxDecoration(
        color: myColorScheme(context).backgroundColor1,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            StringConstants.totalAmount.tr(),
            style: FontPalette.normal14.copyWith(
              color: myColorScheme(context).greyColor4,
            ),
          ),
          8.verticalSpace,
          BlocSelector<SmartInvestmentCubit, SmartInvestmentState, String>(
            selector: (state) => state.amount,
            builder: (context, amount) {
              return Text(
                amount.toCurrency(),
                style: FontPalette.semiBold40.copyWith(
                  color: myColorScheme(context).primaryColor,
                ),
              );
            },
          ),
          if (type == InvestmentType.withdraw)
            Text(
              StringConstants.willBeSentToProfitWallet.tr(),
              style: FontPalette.normal12.copyWith(
                color: myColorScheme(context).greyColor4,
              ),
            ),
        ],
      ),
    );
  }
}

class _BuildPasswordInput extends StatelessWidget {
  final TextEditingController controller;
  final GlobalKey<FormState> formKey;
  const _BuildPasswordInput({
    required this.controller,
    required this.formKey,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: formKey,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              StringConstants.enterWalletPassword.tr(),
              style: FontPalette.normal14.copyWith(
                color: myColorScheme(context).primaryColor,
              ),
            ),
            10.verticalSpace,
            CommonPinFiledText(
              controller: controller,
              obscureText: true,
            ),
          ],
        ),
      ),
    );
  }
}

class _BuildConfirmButton extends StatelessWidget {
  final VoidCallback onPressed;
  const _BuildConfirmButton({
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: BlocSelector<SmartInvestmentCubit, SmartInvestmentState,
          (DataStatus, DataStatus, DataStatus)>(
        selector: (state) => (
          state.submitInvestmentStatus,
          state.appendInvestmentStatus,
          state.withdrawInvestmentStatus
        ),
        builder: (context, isSubmitting) {
          return CustomButton(
            width: 377.w,
            height: 50.h,
            label: StringConstants.confirm.tr(),
            isLoading: isSubmitting.$1 == DataStatus.loading ||
                isSubmitting.$2 == DataStatus.loading ||
                isSubmitting.$3 == DataStatus.loading,
            onPressed: onPressed,
          );
        },
      ),
    );
  }
}
