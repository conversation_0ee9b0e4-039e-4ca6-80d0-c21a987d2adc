import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/extention.dart';

import '../../../core/constants/enums.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/theme/color_pallette.dart';
import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';
import '../domain/models/investment_records/investment_records.dart';

class PurchaseRecordCard extends StatelessWidget {
  final InvestmentRecord record;
  final int processStatus;
  final VoidCallback? onAppend;
  final VoidCallback? onWithdraw;
  final VoidCallback? onDetails;

  const PurchaseRecordCard({
    super.key,
    required this.record,
    required this.processStatus,
    this.onAppend,
    this.onWithdraw,
    this.onDetails,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDetailRow(
            '${StringConstants.orderNo.tr()}:',
            record.orderNo ?? '',
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 16.w),
            color: myColorScheme(context).backgroundColor2,
            child: Column(
              children: [
                _buildInfoRow(
                  label1: StringConstants.productName.tr(),
                  value1: record.productName ?? '',
                  label2: StringConstants.followUpPeriod.tr(),
                  value2: (record.applyCreateTime != null && record.applyCreateTime!.isNotEmpty)
                      ? record.applyCreateTime!.split(' ').first
                      : '',
                ),
                8.verticalSpace,
                _buildInfoRow(
                  label1: StringConstants.starMentor.tr(),
                  value1: record.mentorName ?? '',
                  label2: StringConstants.purchasePrice.tr(),
                  value2: '${record.totalAmount}'.toCurrency(),
                ),
                8.verticalSpace,
                _buildInfoRow(
                  label1: StringConstants.cycle.tr(),
                  value1: (record.cycle ?? 0) > 1
                      ? '${record.cycle} ${StringConstants.days.tr()}'
                      : '${record.cycle} ${StringConstants.day.tr()}',
                  label2: StringConstants.tutorCommission.tr(),
                  value2: '${record.commissionRate}%',
                  // valueColor2: ColorPalette.deniedColor,
                ),
              ],
            ),
          ),
          if (processStatus == ProcessStatus.denied.value)
            Padding(
              padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
              child: Text(
                '${StringConstants.reason.tr()}: ${(record.auditContent?.isEmpty ?? true) ? '----' : record.auditContent}',
                style: FontPalette.normal14.copyWith(color: ColorPalette.deniedColor),
              ),
            )
          else
            _buildButtons(context),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Container(
      padding: EdgeInsets.all(16.r),
      child: Row(
        children: [
          Text(
            label,
            style: FontPalette.medium14,
          ),
          8.horizontalSpace,
          Text(
            value,
            style: FontPalette.medium14,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required String label1,
    required String value1,
    required String label2,
    required String value2,
    Color? valueColor1,
    Color? valueColor2,
  }) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label1,
                style: FontPalette.normal14.copyWith(
                  color: ColorPalette.greyColor4,
                ),
              ),
              4.verticalSpace,
              Text(
                value1,
                style: FontPalette.medium14.copyWith(
                  color: valueColor1,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label2,
                style: FontPalette.normal14.copyWith(
                  color: ColorPalette.greyColor4,
                ),
              ),
              4.verticalSpace,
              Text(
                value2,
                style: FontPalette.medium14.copyWith(
                  color: valueColor2,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildButtons(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 5.h ),
      child: switch (processStatus) {
        0 => const SizedBox(), // Initialization/pending - no buttons
        (3 || 4) => Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              _buildButton(context, StringConstants.details.tr(), onDetails),
            ],
          ),
        2 => const SizedBox(), // Review rejected - no buttons, shows reason instead
        _ => const SizedBox(),
      },
    );
  }

  Widget _buildButton(
    BuildContext context,
    String label,
    VoidCallback? onPressed,
  ) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: myColorScheme(context).primaryColor,
        elevation: 0,
        padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 10.w),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
      ),
      child: Text(
        label,
        style: FontPalette.normal14.copyWith(color: Colors.white),
      ),
    );
  }
}
