import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/features/smart_investment/logic/smart_investment/smart_investment_cubit.dart';
import 'package:sf_app_v2/features/smart_investment/widgets/purchase_bottom_sheet.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/common_function.dart';
import '../../../core/constants/enums.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/theme/color_pallette.dart';
import '../../../core/theme/font_pallette.dart';

class AmountInputDialog extends StatefulWidget {
  final AmountInputType type;
  final double minAmount;
  final double maxAmount;
  final double? singleAmount; // Only used for append
  final String productId;
  final String? orderNo;
  final VoidCallback onSuccess;
  final double totalAmount;

  const AmountInputDialog({
    super.key,
    required this.type,
    required this.minAmount,
    required this.maxAmount,
    this.singleAmount,
    required this.productId,
    this.orderNo,
    required this.onSuccess,
    this.totalAmount = 0,
  });

  @override
  State<AmountInputDialog> createState() => _AmountInputDialogState();
}

class _AmountInputDialogState extends State<AmountInputDialog> {
  final TextEditingController _amountController = TextEditingController();

  String get _dialogTitle => widget.type == AmountInputType.append
      ? StringConstants.additionalAmount.tr()
      : StringConstants.withdrawalAmount.tr();

  String get _hintText => widget.type == AmountInputType.append
      ? '${StringConstants.additionalAmount.tr()} ${StringConstants.mustBeMultipleOf.tr()} ${widget.singleAmount}'
      : StringConstants.enterWithdrawalAmount.tr();

  String get _amountRangeText {
    if (widget.type == AmountInputType.append) {
      final remainingAmount = widget.maxAmount - widget.totalAmount;
      return '${StringConstants.max.tr()}: $remainingAmount'.toCurrency();
    } else {
      return '${StringConstants.todayProfit.tr()}: ${widget.maxAmount}'.toCurrency();
    }
  }

  String _getErrorMessage() {
    final amount = double.tryParse(_amountController.text);
    if (amount == null) {
      return StringConstants.pleaseEnterAValidAmount.tr();
    }

    if (widget.type == AmountInputType.append) {
      if (amount < widget.minAmount) {
        return '${StringConstants.minimumAmount.tr()} ${widget.minAmount}'.toCurrency();
      }
      if (amount > widget.maxAmount) {
        return '${StringConstants.maximumAmount.tr()} ${widget.maxAmount}'.toCurrency();
      }
      if (amount % widget.singleAmount! != 0) {
        return '${StringConstants.amountMustBeMultipleOf.tr()} ${widget.singleAmount}'.toCurrency();
      }
    } else {
      if (amount < 1) {
        return '${StringConstants.minimumWithdrawalAmount.tr()} 1'.toCurrency();
      }
      if (amount > widget.maxAmount) {
        return '${StringConstants.maximumWithdrawalAmount.tr()} ${widget.maxAmount}'.toCurrency();
      }
    }

    return StringConstants.invalidAmount.tr();
  }

  @override
  void dispose() {
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _handleConfirm(BuildContext context) async {
    // Validate amount
    final amount = double.tryParse(_amountController.text);
    if (amount == null) {
      CommonFunctions().showFlutterToast(StringConstants.pleaseEnterAValidAmount.tr());
      return;
    }

    if (widget.type == AmountInputType.append) {
      // Append specific validations
      final remainingAmount = widget.maxAmount - widget.totalAmount;
      if (amount > remainingAmount) {
        CommonFunctions().showFlutterToast(StringConstants.amountExceedsMaximumAllowed.tr());
        return;
      }
      if (amount % widget.singleAmount! != 0) {
        CommonFunctions().showFlutterToast(
            '${StringConstants.amountMustBeMultipleOf.tr()} ${widget.singleAmount}'.toCurrency(),
        );
        return;
      }
    } else {
      // Withdraw specific validations
      if (amount < 1) {
        CommonFunctions().showFlutterToast(
          '${StringConstants.minimumWithdrawalAmount.tr()} 1'.toCurrency(),
        );
        return;
      }
      if (amount > widget.maxAmount) {
        CommonFunctions().showFlutterToast(
          StringConstants.insufficientAvailableBalance.tr(),
        );
        return;
      }
    }

    final success = await context.read<SmartInvestmentCubit>().appendAmount(
          amount: _amountController.text,
          minAmount: widget.minAmount,
          maxAmount: widget.maxAmount,
        );

    if (!success) {
      final error = _getErrorMessage();
      CommonFunctions().showFlutterToast(error);
      return;
    }

    if (context.mounted) {
      Navigator.pop(context);
      _showBottomSheet(context);
    }
  }

  void _showBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      builder: (_) => PurchaseBottomSheet(
        type: widget.type == AmountInputType.append ? InvestmentType.append : InvestmentType.withdraw,
        amount: double.parse(_amountController.text),
        productId: widget.productId,
        orderNo: widget.orderNo,
        onClose: () {
          context.read<SmartInvestmentCubit>().resetInvestmentFlow();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Container(
        padding: EdgeInsets.all(24.r),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _dialogTitle,
              style: FontPalette.semiBold18,
            ),
            24.verticalSpace,
            TextFormField(
              controller: _amountController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: _hintText,
                hintStyle: FontPalette.normal11.copyWith(
                  color: ColorPalette.greyColor3,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                  borderSide: BorderSide(
                    color: ColorPalette.greyColor4,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                  borderSide: BorderSide(
                    color: ColorPalette.primaryColor,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                  borderSide: BorderSide(
                    color: ColorPalette.deniedColor,
                  ),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                  borderSide: BorderSide(
                    color: ColorPalette.deniedColor,
                  ),
                ),
              ),
            ),
            12.verticalSpace,
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                _amountRangeText,
                style: FontPalette.normal14.copyWith(
                  color: ColorPalette.deniedColor,
                ),
              ),
            ),
            24.verticalSpace,
            BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
              builder: (context, state) {
                return Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.pop(context),
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          side: BorderSide(
                            color: myColorScheme(context).primaryColor ?? ColorPalette.primaryColor,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          StringConstants.cancel.tr(),
                          style: FontPalette.normal14.copyWith(
                            color: myColorScheme(context).primaryColor ?? ColorPalette.primaryColor,
                          ),
                        ),
                      ),
                    ),
                    12.horizontalSpace,
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _handleConfirm(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: myColorScheme(context).primaryColor ?? ColorPalette.primaryColor,
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child:
                            //  false
                            //     ? SizedBox(
                            //         height: 20.h,
                            //         width: 20.w,
                            //         child: const CircularProgressIndicator(
                            //           strokeWidth: 2,
                            //           valueColor: AlwaysStoppedAnimation<Color>(
                            //             Colors.white,
                            //           ),
                            //         ),
                            //       )
                            //     :
                            Text(
                          StringConstants.confirm.tr(),
                          style: FontPalette.normal14.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
