import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/extention.dart';

import '../../../core/constants/enums.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/theme/color_pallette.dart';
import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';
import '../../../core/widgets/common_shimmer.dart';
import '../domain/models/investment_record_details/record_detail_response.dart';
import '../logic/smart_investment/smart_investment_cubit.dart';

class SummaryCard extends StatelessWidget {
  final RecordDetailMainData? data;
  final String? transactionId;

  const SummaryCard({
    super.key,
    required this.data,
    required this.transactionId,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(16.r, 8.r, 16.r, 8.r),
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${StringConstants.orderNo.tr()}: $transactionId',
            style: FontPalette.normal14.copyWith(
              color: ColorPalette.subTitleColor,
            ),
          ),
          8.verticalSpace,
          Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: SummaryItem(
                      label: StringConstants.initialCapital.tr(),
                      value: '${data?.orderAmount ?? 0}'.toCurrency(),
                    ),
                  ),
                  16.horizontalSpace,
                  Expanded(
                    child: SummaryItem(
                      label: StringConstants.additionalFunds.tr(),
                      value: '${data?.additionalAmount ?? 0}'.toCurrency(),
                    ),
                  ),
                ],
              ),
              8.verticalSpace,
              Row(
                children: [
                  Expanded(
                    child: SummaryItem(
                      label: StringConstants.cumulativeIncome.tr(),
                      value:
                          '${(data?.infoList?.isNotEmpty == true) ? data?.infoList?.first.netProfit ?? 0 : 0}'
                              .toCurrency(),
                    ),
                  ),
                  16.horizontalSpace,
                  Expanded(
                    child: SummaryItem(
                      label: StringConstants.withdrawnProfit.tr(),
                      value: '0.00'.toCurrency(),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class SummaryItem extends StatelessWidget {
  final String label;
  final String value;

  const SummaryItem({super.key, required this.label, required this.value});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontPalette.normal14.copyWith(
            color: ColorPalette.greyColor4,
          ),
        ),
        4.verticalSpace,
        Text(
          value,
          style: FontPalette.medium14.copyWith(),
        ),
      ],
    );
  }
}

class TradeCard extends StatelessWidget {
  final RecordDetail detail;

  const TradeCard({super.key, required this.detail});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with stock name and code
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  '${StringConstants.stockName.tr()}: ${detail.stockName}',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: FontPalette.medium14,
                ),
              ),
              Row(
                children: [
                  Text(
                    StringConstants.stockCode.tr(),
                    style: FontPalette.normal14,
                  ),
                  Text(detail.stockCode ?? '', style: FontPalette.medium14),
                ],
              ),
            ],
          ),
          16.verticalSpace,
          // Buy details
          TradeRow(
            label1: StringConstants.buyingPrice.tr(),
            value1: '${detail.buyPrice}'.toCurrency(),
            label2: StringConstants.buyQuantity.tr(),
            value2: detail.buyQuantity?.toString() ?? '',
            label3: StringConstants.purchaseDate.tr(),
            value3: (detail.buyDate ?? '').toString().formatDate(format: 'yyyy/MM/dd'),
          ),
          12.verticalSpace,
          TradeRow(
            label1: StringConstants.sellingPrice.tr(),
            value1: detail.sellPrice != null
                ? '${detail.sellPrice}'.toCurrency()
                : '--',
            label2: StringConstants.sellQuantity.tr(),
            value2: detail.sellQuantity != null
                ? detail.sellQuantity!.toString()
                : '--',
            label3: StringConstants.sellDate.tr(),
            value3: (detail.sellDate ?? '').toString().formatDate(format: 'yyyy/MM/dd'),
          ),
          12.verticalSpace,
          TradeRow(
            label1: StringConstants.buyPosition.tr(),
            value1: '${(double.parse(detail.buyPosition ?? '0') * 100).floor()}%',
            label2: '',
            value2: '',
            label3: '',
            value3: '',
          ),
        ],
      ),
    );
  }
}

class TradeRow extends StatelessWidget {
  final String label1;
  final String value1;
  final String label2;
  final String value2;
  final String label3;
  final String value3;

  const TradeRow({
    super.key,
    required this.label1,
    required this.value1,
    required this.label2,
    required this.value2,
    required this.label3,
    required this.value3,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label1,
                style: FontPalette.normal14.copyWith(
                  color: ColorPalette.greyColor4,
                ),
              ),
              4.verticalSpace,
              Text(
                value1,
                style: FontPalette.medium14.copyWith(),
              ),
            ],
          ),
        ),
        if (label2.isNotEmpty)
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label2,
                  style: FontPalette.normal14.copyWith(
                    color: ColorPalette.greyColor4,
                  ),
                ),
                4.verticalSpace,
                Text(
                  value2,
                  style: FontPalette.medium14.copyWith(),
                ),
              ],
            ),
          ),
        if (label3.isNotEmpty)
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label3,
                  style: FontPalette.normal14.copyWith(
                    color: ColorPalette.greyColor4,
                  ),
                ),
                4.verticalSpace,
                Text(
                  value3,
                  style: FontPalette.medium14.copyWith(),
                ),
              ],
            ),
          ),
      ],
    );
  }
}

class RevenueDetails extends StatelessWidget {
  final RecordDetail detail;
  final String formula;

  const RevenueDetails({
    super.key,
    required this.detail,
    required this.formula,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 24.w),
      decoration: BoxDecoration(
        color: ColorPalette.primaryColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          Text(
            StringConstants.revenueDetails.tr(),
            style: FontPalette.medium16.copyWith(color: Colors.white),
          ),
          8.verticalSpace,
          Text(
            formula,
            style: FontPalette.normal14.copyWith(color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class EmptyState extends StatelessWidget {
  const EmptyState({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Text(
          StringConstants.noDetailsAvailable.tr(),
          style: FontPalette.normal14.copyWith(
            color: ColorPalette.greyColor3,
          ),
        ),
      ),
    );
  }
}

class ShimmerView extends StatelessWidget {
  const ShimmerView({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const NeverScrollableScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.r),
            child: CommonShimmer(
              width: 200.w,
              height: 20.h,
              br: 4.r,
            ),
          ),
          // Trade card shimmer
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 3, // Show 3 shimmer items
            itemBuilder: (_, __) => Column(
              children: [
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                  child: Column(
                    children: [
                      // Trade card shimmer
                      CommonShimmer(
                        width: double.infinity,
                        height: 180.h,
                        br: 12.r,
                      ),
                      8.verticalSpace,
                      // Revenue details shimmer
                      CommonShimmer(
                        width: double.infinity,
                        height: 80.h,
                        br: 12.r,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class RejectCard extends StatelessWidget {
  const RejectCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(16.r, 8.r, 16.r, 8.r),
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
        builder: (context, state) {
          if (state.recordDetailFetchStatus == DataStatus.loading) {
            return const ShimmerView();
          }
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: StringConstants.appendRejectedAmount.tr(),
                      style: FontPalette.normal14.copyWith(
                        color: ColorPalette.greyColor4,
                      ),
                    ),
                  ],
                ),
              ),
              10.verticalSpace,
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: state.rejectResponse?.data?.list?.length ?? 0,
                itemBuilder: (context, index) {
                  final item = state.rejectResponse?.data?.list?[index];
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            item?.orderAmount.toString() ?? '---',
                            style: FontPalette.normal14.copyWith(
                              color: ColorPalette.greyColor4,
                            ),
                          ),
                          Text(
                            DateFormat('MMM dd, yyyy hh:mm a').format(
                              DateTime.parse(
                                '${item?.createTime}',
                              ),
                            ),
                            style: FontPalette.normal14.copyWith(),
                          ),
                        ],
                      ),
                      4.verticalSpace,
                      Text(
                        (item?.auditContent?.isEmpty ?? true)
                            ? '-----'
                            : item?.auditContent ?? '-----',
                        style: FontPalette.normal14.copyWith(
                          color: ColorPalette.deniedColor,
                        ),
                      ),
                    ],
                  );
                },
              ),
            ],
          );
        },
      ),
    );
  }
}
