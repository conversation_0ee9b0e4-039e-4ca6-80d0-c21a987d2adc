import 'package:carousel_slider/carousel_slider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/constants/string_constants.dart';
import '../../../core/theme/my_color_scheme.dart';
import '../../../core/widgets/common_wallet_slider_item.dart';
import '../../home/<USER>/models/balance/balance_model.dart';
import '../logic/smart_investment/smart_investment_cubit.dart';

class InvestmentWalletSlider extends StatelessWidget {
  final double width;
  final double fullWidth;
  final double height;
  final BalanceData balance;

  final List<String> _walletTitles = [
    StringConstants.bonusWallet.tr(),
    StringConstants.depositWallet.tr(),
  ];

  InvestmentWalletSlider({
    super.key,
    required this.width,
    required this.fullWidth,
    required this.height,
    required this.balance,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
      builder: (context, state) {
        return Column(
          children: [
            CarouselSlider(
              options: CarouselOptions(
                enableInfiniteScroll: false,
                height: (height + 8).w,
                aspectRatio: 377 / 205,
                viewportFraction: 1,
                initialPage: state.walletIndex,
                onPageChanged: (index, reason) {
                  context.read<SmartInvestmentCubit>()
                    ..setWalletIndex(index)
                    ..updateWalletSelection();
                },
              ),
              items: List.generate(
                _walletTitles.length,
                (index) => CommonWalletSliderItem(
                  width: width,
                  height: height,
                  balance: balance,
                  title: _walletTitles[index],
                ),
              ),
            ),
            9.verticalSpace,
            _DotIndicators(currentIndex: state.walletIndex),
          ],
        );
      },
    );
  }
}

class _DotIndicators extends StatelessWidget {
  final int currentIndex;

  const _DotIndicators({required this.currentIndex});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        2,
        (index) => Padding(
          padding: EdgeInsets.symmetric(horizontal: 2.w),
          child: _Dot(index: index, currentIndex: currentIndex),
        ),
      ),
    );
  }
}

class _Dot extends StatelessWidget {
  final int index, currentIndex;

  const _Dot({
    required this.index,
    required this.currentIndex,
  });

  @override
  Widget build(BuildContext context) {
    final isActive = currentIndex == index;
    return Container(
      width: isActive ? 26.w : 16.w,
      height: 6.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20).r,
        color: isActive
            ? myColorScheme(context).primaryVar1
            : myColorScheme(context).borderColor,
      ),
    );
  }
}
