part of 'smart_investment_cubit.dart';

class SmartInvestmentState extends Equatable {
  final MentorResponse? mentorListData;
  final List<Product>? productListData;
  final Map<int, InvestmentRecordModel> investmentRecordsByStatus;
  final RecordDetailMainData? recordDetailData;
  final List<RecordDetail>? recordDetails;
  final Product? selectedProduct;
  final bool isAutoRenewal;
  final bool agreementChecked;
  final String amount;
  final int currentStep;
  final int walletIndex;
  final bool showSuccess;
  final DataStatus mentorListFetchStatus;
  final DataStatus submitInvestmentStatus;
  final DataStatus productListFetchStatus;
  final DataStatus investmentRecordFetchStatus;
  final DataStatus recordDetailFetchStatus;
  final DataStatus purchaseStatus;
  final DataStatus appendInvestmentStatus;
  final DataStatus withdrawInvestmentStatus;
  final DataStatus serviceAgreementFetchStatus;
  final DataStatus rejectResponseFetchStatus;
  final RejectResponse? rejectResponse;
  final String? error;
  final String? selectedWallet;
  final String? serviceAgreement;
  final InvestmentResponse? purchaseProduct;
  final List<PurchasePercentage> purchasePercentage;
  final List<PurchasedListData> purchasedList;
  final DataStatus purchasedListFetchStatus;
  final DataStatus purchasePercentageConfigFetchStatus;
  final OrderRateData? purchasePercentageConfigData;

  const SmartInvestmentState({
    this.mentorListData,
    this.productListData,
    this.investmentRecordsByStatus = const {},
    this.recordDetailData,
    this.selectedProduct,
    this.isAutoRenewal = false,
    this.agreementChecked = false,
    this.amount = '',
    this.currentStep = 0,
    this.walletIndex = 0,
    this.showSuccess = false,
    this.mentorListFetchStatus = DataStatus.idle,
    this.submitInvestmentStatus = DataStatus.idle,
    this.productListFetchStatus = DataStatus.idle,
    this.investmentRecordFetchStatus = DataStatus.idle,
    this.recordDetailFetchStatus = DataStatus.idle,
    this.purchaseStatus = DataStatus.idle,
    this.appendInvestmentStatus = DataStatus.idle,
    this.withdrawInvestmentStatus = DataStatus.idle,
    this.serviceAgreementFetchStatus = DataStatus.idle,
    this.rejectResponseFetchStatus = DataStatus.idle,
    this.purchasedListFetchStatus = DataStatus.idle,
    this.rejectResponse,
    this.error,
    this.selectedWallet,
    this.serviceAgreement,
    this.recordDetails,
    this.purchaseProduct,
    this.purchasePercentage = const [],
    this.purchasedList = const [],
    this.purchasePercentageConfigFetchStatus = DataStatus.idle,
    this.purchasePercentageConfigData,
  });

  @override
  List<Object?> get props => [
        mentorListData,
        productListData,
        investmentRecordsByStatus,
        recordDetailData,
        selectedProduct,
        isAutoRenewal,
        agreementChecked,
        amount,
        currentStep,
        walletIndex,
        showSuccess,
        mentorListFetchStatus,
        submitInvestmentStatus,
        productListFetchStatus,
        investmentRecordFetchStatus,
        recordDetailFetchStatus,
        purchaseStatus,
        appendInvestmentStatus,
        withdrawInvestmentStatus,
        serviceAgreementFetchStatus,
        rejectResponseFetchStatus,
        rejectResponse,
        error,
        selectedWallet,
        serviceAgreement,
        recordDetails,
        purchaseProduct,
        purchasePercentage,
        purchasedList,
        purchasedListFetchStatus,
        purchasePercentageConfigFetchStatus,
        purchasePercentageConfigData,
      ];

  SmartInvestmentState copyWith({
    MentorResponse? mentorListData,
    List<Product>? productListData,
    Map<int, InvestmentRecordModel>? investmentRecordsByStatus,
    RecordDetailMainData? recordDetailData,
    Product? selectedProduct,
    bool? isAutoRenewal,
    bool? agreementChecked,
    String? amount,
    int? currentStep,
    int? walletIndex,
    bool? showSuccess,
    DataStatus? mentorListFetchStatus,
    DataStatus? submitInvestmentStatus,
    DataStatus? productListFetchStatus,
    DataStatus? investmentRecordFetchStatus,
    DataStatus? recordDetailFetchStatus,
    DataStatus? purchaseStatus,
    DataStatus? appendInvestmentStatus,
    DataStatus? withdrawInvestmentStatus,
    DataStatus? serviceAgreementFetchStatus,
    DataStatus? rejectResponseFetchStatus,
    DataStatus? purchasedListFetchStatus,
    RejectResponse? rejectResponse,
    String? error,
    String? selectedWallet,
    String? serviceAgreement,
    List<RecordDetail>? recordDetails,
    InvestmentResponse? purchaseProduct,
    List<PurchasePercentage>? purchasePercentage,
    List<PurchasedListData>? purchasedList,
    DataStatus? purchasePercentageConfigFetchStatus,
    OrderRateData? purchasePercentageConfigData,
  }) {
    return SmartInvestmentState(
      mentorListData: mentorListData ?? this.mentorListData,
      productListData: productListData ?? this.productListData,
      investmentRecordsByStatus: investmentRecordsByStatus ?? this.investmentRecordsByStatus,
      recordDetailData: recordDetailData ?? this.recordDetailData,
      selectedProduct: selectedProduct ?? this.selectedProduct,
      isAutoRenewal: isAutoRenewal ?? this.isAutoRenewal,
      agreementChecked: agreementChecked ?? this.agreementChecked,
      amount: amount ?? this.amount,
      currentStep: currentStep ?? this.currentStep,
      walletIndex: walletIndex ?? this.walletIndex,
      showSuccess: showSuccess ?? this.showSuccess,
      mentorListFetchStatus: mentorListFetchStatus ?? this.mentorListFetchStatus,
      submitInvestmentStatus: submitInvestmentStatus ?? this.submitInvestmentStatus,
      productListFetchStatus: productListFetchStatus ?? this.productListFetchStatus,
      investmentRecordFetchStatus: investmentRecordFetchStatus ?? this.investmentRecordFetchStatus,
      recordDetailFetchStatus: recordDetailFetchStatus ?? this.recordDetailFetchStatus,
      purchaseStatus: purchaseStatus ?? this.purchaseStatus,
      appendInvestmentStatus: appendInvestmentStatus ?? this.appendInvestmentStatus,
      withdrawInvestmentStatus: withdrawInvestmentStatus ?? this.withdrawInvestmentStatus,
      serviceAgreementFetchStatus: serviceAgreementFetchStatus ?? this.serviceAgreementFetchStatus,
      rejectResponseFetchStatus: rejectResponseFetchStatus ?? this.rejectResponseFetchStatus,
      rejectResponse: rejectResponse ?? this.rejectResponse,
      error: error ?? this.error,
      selectedWallet: selectedWallet ?? this.selectedWallet,
      serviceAgreement: serviceAgreement ?? this.serviceAgreement,
      recordDetails: recordDetails ?? this.recordDetails,
      purchaseProduct: purchaseProduct ?? this.purchaseProduct,
      purchasePercentage: purchasePercentage ?? this.purchasePercentage,
      purchasedList: purchasedList ?? this.purchasedList,
      purchasedListFetchStatus: purchasedListFetchStatus ?? this.purchasedListFetchStatus,
      purchasePercentageConfigFetchStatus: purchasePercentageConfigFetchStatus ?? this.purchasePercentageConfigFetchStatus,
      purchasePercentageConfigData: purchasePercentageConfigData ?? this.purchasePercentageConfigData,
    );
  }
}
