import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/features/smart_investment/domain/models/mentor/mentor_model.dart';
import 'package:sf_app_v2/features/smart_investment/domain/repository/smart_investment_repository.dart';

import '../../../../core/api/network/network_helper.dart';
import '../../../../core/common_function.dart';
import '../../../../core/config/app_config.dart';
import '../../../../core/config/feature_configs/investment_feature_config.dart';
import '../../../../core/dependency_injection/injectable.dart';
import '../../../../core/constants/string_constants.dart';
import '../../../../core/services/wallet_balance/wallet_balance_service.dart';
import '../../domain/models/Investment_response/investment_respone.dart';
import '../../domain/models/investment_products/investment_products.dart';
import '../../domain/models/investment_record_details/record_detail_response.dart';
import '../../domain/models/investment_records/investment_records.dart';
import '../../domain/models/order_rate/order_rate.dart';
import '../../domain/models/purchase_percentage/purchase_percentage.dart';
import '../../domain/models/purchased_list/purchased_list.dart';
import '../../domain/models/reject_response/reject_response.dart';

part 'smart_investment_state.dart';

@injectable
class SmartInvestmentCubit extends Cubit<SmartInvestmentState> {
  final SmartInvestmentRepository _investmentService;

  SmartInvestmentCubit(this._investmentService)
      : super(SmartInvestmentState(
          purchasePercentage: _getInitialPurchasePercentage(),
        ));

  // Helper method to get initial purchase percentage based on configuration
  static List<PurchasePercentage> _getInitialPurchasePercentage() {
    final investmentService = getIt<InvestmentFeatureService>();
    return investmentService.getPurchasePercentages();
  }

  /// Initializes the purchase percentage and calculates the initial amount
  /// This should be called after the cubit is created to set up the initial state
  void initializePurchasePercentage() {
    if (state.purchasePercentage.isEmpty) {
      // If no percentages available, refresh the configuration
      _updatePurchasePercentagesFromConfig();
      return;
    }

    final investmentService = getIt<InvestmentFeatureService>();
    final defaultPercentage = investmentService.getDefaultPercentage(
      apiData: state.purchasePercentageConfigData,
    );

    // Find the default percentage or fallback to first available
    final selectedPercentage = state.purchasePercentage.firstWhere(
      (element) => element.percentage == defaultPercentage,
      orElse: () => state.purchasePercentage.first,
    );

    setPurchasePercentage(percentage: selectedPercentage);
  }

  Future<void> getMentorList({int page = 1, bool isLoadMore = false}) async {
    emit(state.copyWith(mentorListFetchStatus: DataStatus.loading));
    try {
      final result = await _investmentService.getMentorList(
        pageNum: page,
        pageSize: 10,
      );

      if (result.data != null) {
        // Create the mentor list with proper type casting
        final List<Mentor> currentList = state.mentorListData?.data?.list ?? [];
        final List<Mentor> newList = result.data?.data?.list ?? [];

        // Combine lists if loading more, otherwise use just the new list
        final List<Mentor> mentorList =
            isLoadMore ? [...currentList, ...newList] : newList;

        // Create the response with the appropriate list
        final mentorResponse = MentorResponse(
          code: result.data?.code ?? 0,
          data: MentorData(
            list: mentorList,
            pageNum: result.data?.data?.pageNum ?? 0,
            pageSize: result.data?.data?.pageSize ?? 0,
            total: result.data?.data?.total ?? 0,
          ),
          msg: result.data?.msg ?? '',
        );

        emit(state.copyWith(
          mentorListFetchStatus: DataStatus.success,
          mentorListData: mentorResponse,
        ));
      } else {
        emit(state.copyWith(
          mentorListFetchStatus: DataStatus.failed,
          error: result.error,
        ));
      }
    } on Exception catch (e) {
      emit(state.copyWith(
        mentorListFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  Future<void> getProducts({required int mentorId, int? productId}) async {
    emit(state.copyWith(productListFetchStatus: DataStatus.loading));
    try {
      final result = await _investmentService.getProducts(mentorId: mentorId);

      if (result.data != null) {
        final products = (result.data?.data ?? []).toList()
          ..sort((a, b) => (a.sort ?? 0).compareTo(b.sort ?? 0));

        // Find the product to select (either by ID or default to first)
        Product? selectedProduct;
        if (products.isNotEmpty) {
          if (productId != null) {
            // Try to find the product with the given ID
            try {
              selectedProduct =
                  products.firstWhere((element) => element.id == productId);
            } catch (e) {
              // If product with ID not found, default to first product
              selectedProduct = products.first;
            }
          } else {
            // No specific product ID, select the first one
            selectedProduct = products.first;
          }
        }

        // Update state with the products and selected product
        emit(state.copyWith(
          productListData: products,
          selectedProduct: selectedProduct,
          productListFetchStatus: DataStatus.success,
        ));
      } else {
        emit(state.copyWith(
          productListFetchStatus: DataStatus.failed,
          error: result.error,
        ));
      }
    } on Exception catch (e) {
      emit(state.copyWith(
        productListFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  Future<void> getPurchasedRecords({int processStatus = 0}) async {
    emit(state.copyWith(investmentRecordFetchStatus: DataStatus.loading));
    try {
      final result = await _investmentService.getPurchasedRecords(
        processStatus: processStatus,
      );

      if (result.data != null) {
        final updatedRecordsByStatus = Map<int, InvestmentRecordModel>.from(
          state.investmentRecordsByStatus,
        );
        updatedRecordsByStatus[processStatus] =
            result.data ?? const InvestmentRecordModel();
        emit(
          state.copyWith(
            investmentRecordsByStatus: updatedRecordsByStatus,
            investmentRecordFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            investmentRecordFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          investmentRecordFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> fetchRecordDetails({
    required String id,
    required int pageNum,
  }) async {
    emit(state.copyWith(recordDetailFetchStatus: DataStatus.loading));
    try {
      final result = await _investmentService.getPurchasedRecordDetails(
        id: id,
      );
      if (result.data != null) {
        emit(
          state.copyWith(
            recordDetailData: result.data?.data,
            recordDetails: result.data?.data?.infoList ?? [],
            recordDetailFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            recordDetailFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          recordDetailFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<bool> appendAmount({
    required String amount,
    required double minAmount,
    required double maxAmount,
  }) async {
    // Validate input format
    final parsedAmount = double.tryParse(amount);
    if (parsedAmount == null) {
      CommonFunctions().showFlutterToast('Please enter a valid amount');
      return false;
    }

    // Validate amount range
    if (parsedAmount < minAmount) {
      CommonFunctions().showFlutterToast(
          'Amount cannot be less than ${minAmount.toStringAsFixed(2).toCurrency()}');
      return false;
    }

    if (parsedAmount > maxAmount) {
      CommonFunctions().showFlutterToast(
          'Amount cannot exceed ${maxAmount.toStringAsFixed(2).toCurrency()}');
      return false;
    }

    // Check if amount is a multiple of single amount if product is selected
    if (state.selectedProduct != null &&
        state.selectedProduct!.singleAmount != null) {
      final singleAmount = state.selectedProduct!.singleAmount!;
      if (parsedAmount % singleAmount != 0) {
        CommonFunctions().showFlutterToast(
            'Amount must be a multiple of ${singleAmount.toStringAsFixed(2).toCurrency()}');
        return false;
      }
    }

    // Update state with validated amount
    emit(state.copyWith(amount: parsedAmount.toStringAsFixed(2)));
    return true;
  }

  Future<bool> submitInvestment({
    required String password,
  }) async {
    try {
      emit(
        state.copyWith(
          submitInvestmentStatus: DataStatus.loading,
        ),
      ); // Start loading
      final response = await _investmentService.purchaseProduct(
        productId: state.selectedProduct?.id ?? 0,
        accountType: walletType[state.walletIndex == 0 ? 'bonus' : 'deposit']!
            .toString(),
        password: password,
        type: state.purchasePercentage
            .firstWhere((element) => element.isSelected)
            .type,
        amount: num.parse(state.amount),
        showRate: false,
      );

      if (response.data != null) {
        emit(state.copyWith(
            purchaseProduct: response.data,
            submitInvestmentStatus: DataStatus.success));
        return true;
      } else {
        emit(
          state.copyWith(
            submitInvestmentStatus: DataStatus.failed,
            error: response.error,
          ),
        );
        return false;
      }
    } catch (e) {
      emit(
        state.copyWith(
          submitInvestmentStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
      return false;
    }
  }

  Future<bool> appendInvestment({
    required String password,
    required int? orderId,
  }) async {
    try {
      emit(
        state.copyWith(
          appendInvestmentStatus: DataStatus.loading,
        ),
      ); // Start loading
      final response = await _investmentService.appendProduct(
        orderId: orderId,
        accountType: walletType[state.walletIndex == 0 ? 'bonus' : 'deposit']!
            .toString(),
        password: password,
        amount: double.parse(state.amount).toInt(),
      );

      if (response.data != null) {
        emit(state.copyWith(appendInvestmentStatus: DataStatus.success));
        return true;
      } else {
        emit(
          state.copyWith(
            appendInvestmentStatus: DataStatus.failed,
            error: response.error,
          ),
        );
        return false;
      }
    } catch (e) {
      emit(
        state.copyWith(
          appendInvestmentStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
      return false;
    }
  }

  Future<bool> withdrawInvestment({
    required String password,
    required String orderId,
  }) async {
    try {
      emit(
        state.copyWith(
          withdrawInvestmentStatus: DataStatus.loading,
        ),
      ); // Start loading
      final response = await _investmentService.withdrawProduct(
        orderNo: orderId,
        password: password,
        amount: double.parse(state.amount).toInt(),
      );

      if (response.data != null) {
        emit(state.copyWith(withdrawInvestmentStatus: DataStatus.success));
        return true;
      }

      emit(
        state.copyWith(
          withdrawInvestmentStatus: DataStatus.failed,
          error: response.error,
        ),
      );
      return false;
    } catch (e) {
      emit(
        state.copyWith(
          withdrawInvestmentStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
      return false;
    }
  }

  Future<bool> submitCommunityInvestment({
    required String password,
  }) async {
    try {
      emit(
        state.copyWith(
          submitInvestmentStatus: DataStatus.loading,
        ),
      ); // Start loading
      final response = await _investmentService.purchaseProduct(
        productId: state.selectedProduct?.id ?? 0,
        accountType: getIt<AppConfig>().accountType,
        password: password,
        type: state.purchasePercentage.firstWhere((e) => e.isSelected).type,
        amount: num.parse(state.amount),
        showRate: shouldShowPurchasePercentageButtons,
      );

      if (response.data != null) {
        emit(state.copyWith(
            purchaseProduct: response.data,
            submitInvestmentStatus: DataStatus.success));
        return true;
      } else {
        emit(
          state.copyWith(
            submitInvestmentStatus: DataStatus.failed,
            error: response.error,
          ),
        );
        return false;
      }
    } catch (e) {
      emit(
        state.copyWith(
          submitInvestmentStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
      return false;
    }
  }

  Future<void> getServiceAgreement() async {
    try {
      emit(state.copyWith(serviceAgreementFetchStatus: DataStatus.loading));
      var response = await _investmentService.getServiceAgreement();
      if (response.data != null) {
        emit(
          state.copyWith(
            serviceAgreement: response.data,
            serviceAgreementFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            serviceAgreementFetchStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } on Exception {
      emit(
        state.copyWith(
          serviceAgreementFetchStatus: DataStatus.failed,
          error: 'Failed to fetch service agreement',
        ),
      );
    }
  }

  void clearRecordDetails() {
    emit(
      state.copyWith(
        recordDetailData: null,
        recordDetailFetchStatus: DataStatus.idle,
      ),
    );
  }

  void updateWalletSelection({bool isCommunity = false}) {
    if (isCommunity) {
      emit(
        state.copyWith(
          selectedWallet: 'community',
        ),
      );
    } else {
      emit(
        state.copyWith(
          selectedWallet: state.walletIndex == 0 ? 'bonus' : 'deposit',
        ),
      );
    }
  }

  void setStep(int step) {
    emit(state.copyWith(currentStep: step));
  }

  void setWalletIndex(int index) {
    emit(state.copyWith(walletIndex: index));
  }

  void setAutoRenewal(bool value) {
    emit(state.copyWith(isAutoRenewal: value));
  }

  void setAgreementChecked(bool value) {
    emit(state.copyWith(agreementChecked: value));
  }

  /// Validates the investment parameters and shows appropriate error messages
  ///
  /// Parameters:
  /// - [context]: BuildContext for showing error messages
  /// - [mentorLevel]: Optional VIP level required by the mentor
  /// - [userLevel]: Optional current VIP level of the user
  ///
  /// Returns [true] if all validations pass, [false] otherwise
  ///
  /// Validates:
  /// - Service agreement acceptance
  /// - Product selection
  /// - VIP level requirements (if enabled)
  /// - Investment amount format and range
  /// - Available balance (if trading wallet disabled)
  /// - Min/max amount constraints
  bool validateInvestment(BuildContext context,
      {int? mentorLevel, int? userLevel}) {
    // Check if agreement is checked
    if (!state.agreementChecked) {
      CommonFunctions().showFlutterToast(
          StringConstants.pleaseAcceptTheServiceAgreement.tr());
      return false;
    }

    // Check if a product is selected
    if (state.selectedProduct == null) {
      CommonFunctions().showFlutterToast(
          StringConstants.pleaseSelectAnInvestmentProduct.tr());
      return false;
    }

    if (mentorLevel != null &&
        mentorLevel > 0 &&
        userLevel != null &&
        userLevel > 0 &&
        userLevel < mentorLevel &&
        getIt<AppConfig>().showMentorVipLevel) {
      CommonFunctions().showFlutterToast(
          StringConstants.vipLevelError.tr(args: [mentorLevel.toString()]));
      return false;
    }

    // Validate amount format
    final amount = double.tryParse(state.amount);
    if (amount == null || amount <= 0) {
      CommonFunctions().showFlutterToast(
          StringConstants.pleaseEnterAValidInvestmentAmount.tr());
      return false;
    }

    if (!getIt<AppConfig>().showTradingWallet) {
      final balance = WalletBalanceService().cashBalance;
      if (amount > balance) {
        NetworkHelper.handleMessage(
          StringConstants.insufficientBalance.tr(),
          context,
          type: HandleTypes.customDialog,
          snackBarType: SnackBarType.error,
          actionButtonText: StringConstants.ok.tr(),
        );
        return false;
      }
    }
    final minAmount = state.selectedProduct?.minAmount ?? 0;
    final maxAmount = state.selectedProduct?.maxAmount ?? 0;
    if (amount < minAmount || amount > maxAmount) {
      NetworkHelper.handleMessage(
        'purchaseAmountBetween'.tr(args: [
          minAmount.toStringAsFixed(2).toCurrency(),
          maxAmount.toStringAsFixed(2).toCurrency(),
        ]),
        context,
        type: HandleTypes.customDialog,
        snackBarType: SnackBarType.error,
        actionButtonText: StringConstants.ok.tr(),
      );
      return false;
    }

    // // Validate amount is a multiple of single amount
    // if (singleAmount > 0 && amount % singleAmount != 0) {
    //   CommonFunctions().showFlutterToast(
    //     'Amount must be a multiple of ${singleAmount.toStringAsFixed(2).toUSD()}',
    //   );
    //   return false;
    // }

    // All validations passed
    return true;
  }

  void setAmount(String value) {
    emit(state.copyWith(amount: value));
  }

  void selectProduct(Product? product) {
    emit(state.copyWith(selectedProduct: product));
  }

  void resetInvestmentFlow() {
    // Get the default purchase percentages based on current configuration
    final investmentService = getIt<InvestmentFeatureService>();
    final defaultPercentages = investmentService.getPurchasePercentages(
      apiData: state.purchasePercentageConfigData,
    );

    // Use the default percentage instead of always using the last one
    final defaultPercentage = investmentService.getDefaultPercentage(
      apiData: state.purchasePercentageConfigData,
    );
    final selectedPercentage = defaultPercentages.firstWhere(
      (element) => element.percentage == defaultPercentage,
      orElse: () => defaultPercentages.first,
    );

    emit(
      state.copyWith(
        currentStep: 0,
        walletIndex: 0,
        showSuccess: false,
        isAutoRenewal: false,
        agreementChecked: false,
        selectedProduct: null,
        purchasePercentage: defaultPercentages,
      ),
    );

    setPurchasePercentage(percentage: selectedPercentage);
  }

  void resetBottomSheet() {
    emit(
      state.copyWith(
        submitInvestmentStatus: DataStatus.idle,
        appendInvestmentStatus: DataStatus.idle,
        withdrawInvestmentStatus: DataStatus.idle,
        currentStep: 0,
        walletIndex: 0,
      ),
    );
  }

  void clearError() {
    emit(state.copyWith(error: null));
  }

  void clearInvestmentState() {
    // Get the current purchase percentages based on configuration
    final investmentService = getIt<InvestmentFeatureService>();
    final defaultPercentages = investmentService.getPurchasePercentages(
      apiData: state.purchasePercentageConfigData,
    );

    emit(
      state.copyWith(
        isAutoRenewal: false,
        agreementChecked: false,
        amount: '',
        selectedProduct: null,
        purchasePercentage: defaultPercentages,
      ),
    );

    // Initialize purchase percentage to calculate initial amount
    initializePurchasePercentage();
  }

  Future<void> getProductOrderRejectPage({required String orderNo}) async {
    try {
      emit(
        state.copyWith(
          rejectResponseFetchStatus: DataStatus.loading,
        ),
      ); // Start loading
      final response = await _investmentService.getProductOrderRejectPage(
        orderNo: orderNo,
      );

      if (response.data != null) {
        emit(
          state.copyWith(
            rejectResponse: response.data,
            rejectResponseFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            rejectResponseFetchStatus: DataStatus.failed,
            error: response.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          rejectResponseFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  void setPurchasePercentage({PurchasePercentage? percentage}) {
    percentage ??= state.purchasePercentage.firstWhere(
      (element) => element.isSelected,
      orElse: () => state.purchasePercentage.first,
    );

    // Validate the percentage is allowed
    final investmentService = getIt<InvestmentFeatureService>();
    if (!investmentService.isPercentageEnabled(
      percentage.percentage,
      apiData: state.purchasePercentageConfigData,
    )) {
      return; // Don't update if percentage is not valid
    }

    final maxAmount =  WalletBalanceService().communityBalance.totalAmount ?? 0;
    final calculatedAmount = maxAmount.toStringAsFixed(2);

    // Update the selected percentage
    final updatedPercentages = state.purchasePercentage
        .map((p) => PurchasePercentage(
              percentage: p.percentage,
              isSelected: p.percentage == percentage?.percentage,
              type: p.type,
            ))
        .toList();

    // Calculate the investment amount based on percentage
    final percentageValue = double.tryParse(percentage.percentage) ?? 100;
    final investmentAmount =
        (double.parse(calculatedAmount) * percentageValue / 100)
            .toStringAsFixed(2);

    // Update both the amount and the purchase percentage list
    emit(state.copyWith(
      amount: investmentAmount,
      purchasePercentage: updatedPercentages,
    ));
  }

  Future<void> getPurchasedList({int? mentorId}) async {
    emit(state.copyWith(purchasedListFetchStatus: DataStatus.loading));
    final response =
        await _investmentService.getPurchasedList(mentorId: mentorId ?? 0);
    if (response.data != null) {
      emit(state.copyWith(
          purchasedList: response.data?.data ?? [],
          purchasedListFetchStatus: DataStatus.success));
    } else {
      emit(state.copyWith(
          purchasedListFetchStatus: DataStatus.failed, error: response.error));
    }
  }

  /// Fetches purchase percentage configuration from API if enabled
  Future<void> getPurchasePercentageConfig() async {
    // Only fetch if the feature is enabled
    final investmentService = getIt<InvestmentFeatureService>();
    if (!investmentService.shouldFetchFromApi) {
      return;
    }

    emit(state.copyWith(
        purchasePercentageConfigFetchStatus: DataStatus.loading));
    try {
      final response = await _investmentService.getOrderRate();
      if (response.data != null) {
        emit(state.copyWith(
          purchasePercentageConfigData: OrderRateData(
            orderRates: (response.data?.data?.orderRates ?? []).toList()
              ..sort((a, b) => a.compareTo(b)),
            userId: response.data?.data?.userId,
          ),
          purchasePercentageConfigFetchStatus: DataStatus.success,
        ));
      } else {
        emit(state.copyWith(
          purchasePercentageConfigFetchStatus: DataStatus.failed,
          error: response.error,
        ));
      }
    } on Exception catch (e) {
      emit(state.copyWith(
        purchasePercentageConfigFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    } finally {
      _updatePurchasePercentagesFromConfig();
    }
  }

  /// Updates purchase percentages based on current configuration
  void _updatePurchasePercentagesFromConfig() {
    final investmentService = getIt<InvestmentFeatureService>();
    final updatedPercentages = investmentService.getPurchasePercentages(
      apiData: state.purchasePercentageConfigData,
    );

    emit(state.copyWith(purchasePercentage: updatedPercentages));

    // Initialize purchase percentage with the default value
    final defaultPercentage = investmentService.getDefaultPercentage(
      apiData: state.purchasePercentageConfigData,
    );
    final selectedPercentage = updatedPercentages.firstWhere(
      (element) => element.percentage == defaultPercentage,
      orElse: () => updatedPercentages.first,
    );
    setPurchasePercentage(percentage: selectedPercentage);
  }

  /// Determines if smart investment purchase percentage should be shown in the UI
  ///
  /// This considers both the investment service setting and the current API configuration state.
  /// Returns false if the feature is disabled or if API config is empty.
  bool get shouldShowPurchasePercentageButtons {
    final investmentService = getIt<InvestmentFeatureService>();
    return investmentService.shouldShowPurchasePercentages(
      apiConfigData: state.purchasePercentageConfigData,
    );
  }
}
