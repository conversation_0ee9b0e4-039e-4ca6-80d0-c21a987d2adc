import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/api/endpoint/api_endpoints.dart';
import 'package:sf_app_v2/core/api/network/network.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/smart_investment/domain/models/mentor/mentor_model.dart';
import 'package:sf_app_v2/features/smart_investment/domain/repository/smart_investment_repository.dart';
import '../../../../core/config/app_config.dart';
import '../../../../core/dependency_injection/injectable.dart';
import '../models/Investment_response/investment_respone.dart';
import '../models/investment_products/investment_products.dart';
import '../models/investment_record_details/record_detail_response.dart';
import '../models/investment_records/investment_records.dart';
import '../models/order_rate/order_rate.dart';
import '../models/purchased_list/purchased_list.dart';
import '../models/reject_response/reject_response.dart';

/// Service class that implements the SmartInvestmentRepository interface to handle smart investment related API calls
@Injectable(as: SmartInvestmentRepository)
class SmartInvestmentService implements SmartInvestmentRepository {
  /// Fetches the list of mentors with pagination
  ///
  /// [pageNum] - The page number to fetch
  /// [pageSize] - Number of items per page
  /// Returns a [ResponseResult] containing [MentorResponse] on success
  @override
  Future<ResponseResult<MentorResponse>> getMentorList({
    required int pageNum,
    required int pageSize,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.mentorList}?pageNum=$pageNum&pageSize=$pageSize',
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: MentorResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get mentor list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches investment products for a specific mentor
  ///
  /// [mentorId] - ID of the mentor whose products to fetch
  /// Returns a [ResponseResult] containing [ProductResponse] on success
  @override
  Future<ResponseResult<ProductResponse>> getProducts({
    required int mentorId,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.productList}/$mentorId',
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: ProductResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get products');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Purchases an investment product
  ///
  /// [productId] - ID of the product to purchase
  /// [accountType] - Type of account making the purchase
  /// [password] - User's password for verification
  /// [type] - Type of purchase
  /// [amount] - Amount to invest
  /// Returns a [ResponseResult] containing [InvestmentResponse] on success
  @override
  Future<ResponseResult<InvestmentResponse>> purchaseProduct({
    required int productId,
    required String accountType,
    required String password,
    required int type,
    required num amount,
    required bool showRate,
  }) async {
    bool showPurchaseProductFields =
        getIt<AppConfig>().showPurchaseProductFields;
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.productPay,
        data: {
          if (showPurchaseProductFields && !showRate) 'amount': amount,
          'accountType': accountType,
          'password': password,
          'productId': productId,
          if (accountType == '3' && !showPurchaseProductFields) 'a': type,
          if (showRate) 'rate': type,
        },
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
              data: InvestmentResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to purchase product');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Appends additional investment to an existing order
  ///
  /// [orderId] - ID of the order to append to
  /// [accountType] - Type of account making the addition
  /// [password] - User's password for verification
  /// [amount] - Amount to add
  /// Returns a [ResponseResult<bool>] indicating success/failure
  @override
  Future<ResponseResult<bool>> appendProduct({
    required int? orderId,
    required String accountType,
    required String password,
    required int amount,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.increaseContract,
        data: {
          'accountType': accountType,
          'amount': amount,
          'password': password,
          'orderId': orderId,
        },
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to append product');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches the user's purchased investment records
  ///
  /// [processStatus] - Status of records to fetch
  /// Returns a [ResponseResult] containing [InvestmentRecordModel] on success
  @override
  Future<ResponseResult<InvestmentRecordModel>> getPurchasedRecords({
    required int processStatus,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.productRecords}?processStatus=$processStatus',
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
            data: InvestmentRecordModel.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get purchased records');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches detailed information about a specific purchased record
  ///
  /// [id] - ID of the record to fetch details for
  /// Returns a [ResponseResult] containing [RecordDetailResponse] on success
  @override
  Future<ResponseResult<RecordDetailResponse>> getPurchasedRecordDetails({
    required String id,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.transactionDetails(id),
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
            data: RecordDetailResponse.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get record details');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Withdraws funds from an investment product
  ///
  /// [orderNo] - Order number to withdraw from
  /// [password] - User's password for verification
  /// [amount] - Amount to withdraw
  /// Returns a [ResponseResult<bool>] indicating success/failure
  @override
  Future<ResponseResult<bool>> withdrawProduct({
    required String orderNo,
    required String password,
    required int amount,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.withdrawContract,
        data: {
          'orderNo': orderNo,
          'payPassword': password,
          'profitAmount': amount,
        },
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to withdraw product');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches the service agreement text
  ///
  /// Returns a [ResponseResult<String>] containing the agreement text on success
  @override
  Future<ResponseResult<String>> getServiceAgreement() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.serviceAgreement,
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
            data: response.data['data'],
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get service agreement');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches the rejection page for a product order
  ///
  /// [orderNo] - Order number to get rejection page for
  /// Returns a [ResponseResult] containing [RejectResponse] on success
  @override
  Future<ResponseResult<RejectResponse>> getProductOrderRejectPage({
    required String orderNo,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.productOrderRejectPage(orderNo),
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: RejectResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get product order reject page');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches the list of purchased investments for a mentor
  ///
  /// [mentorId] - Optional ID of mentor to filter purchases by
  /// Returns a [ResponseResult] containing [PurchasedList] on success
  @override
  Future<ResponseResult<PurchasedList>> getPurchasedList(
      {int? mentorId}) async {
    try {
      final String url = '${ApiEndpoints.contractsList}?mentorId=$mentorId';
      final Response response = await NetworkProvider().get(
        url,
        isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: PurchasedList.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get purchased list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches the order rate configuration
  ///
  /// Returns a [ResponseResult] containing [OrderRate] on success
  @override
  Future<ResponseResult<OrderRate>> getOrderRate() async {
    try {
      final Response response = await NetworkProvider()
          .get(ApiEndpoints.orderRateConfig, isSigninRequired: true);
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: OrderRate.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get order rate');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

}
