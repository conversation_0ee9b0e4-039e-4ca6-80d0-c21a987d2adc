// To parse this JSON data, do
//
//     final rejectResponse = rejectResponseFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'reject_response.freezed.dart';
part 'reject_response.g.dart';

RejectResponse rejectResponseFromJson(str) => RejectResponse.fromJson((str));

String rejectResponseToJson(RejectResponse data) => json.encode(data.toJson());

@freezed
class RejectResponse with _$RejectResponse {
  const factory RejectResponse({
    @JsonKey(name: "code") int? code,
    @Json<PERSON>ey(name: "data") Data? data,
    @Json<PERSON>ey(name: "msg") String? msg,
  }) = _RejectResponse;

  factory RejectResponse.fromJson(Map<String, dynamic> json) =>
      _$RejectResponseFromJson(json);
}

@freezed
class Data with _$Data {
  const factory Data({
    @JsonKey(name: "list") List<ListElement>? list,
    @JsonKey(name: "pageNum") int? pageNum,
    @JsonKey(name: "pageSize") int? pageSize,
    @JsonKey(name: "total") int? total,
  }) = _Data;

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);
}

@freezed
class ListElement with _$ListElement {
  const factory ListElement({
    @JsonKey(name: "auditContent") String? auditContent,
    @JsonKey(name: "bindingStatus") int? bindingStatus,
    @JsonKey(name: "commissionRate") dynamic commissionRate,
    @JsonKey(name: "createTime") DateTime? createTime,
    @JsonKey(name: "cycle") dynamic cycle,
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "mentor") String? mentor,
    @JsonKey(name: "orderAmount") int? orderAmount,
    @JsonKey(name: "orderNo") String? orderNo,
    @JsonKey(name: "productName") String? productName,
    @JsonKey(name: "productNumber") dynamic productNumber,
    @JsonKey(name: "productType") int? productType,
    @JsonKey(name: "totalIncome") dynamic totalIncome,
    @JsonKey(name: "unbindTimeEnd") dynamic unbindTimeEnd,
    @JsonKey(name: "unbindTimeStar") dynamic unbindTimeStar,
    @JsonKey(name: "unbindingDays") dynamic unbindingDays,
    @JsonKey(name: "yesterdayIncome") dynamic yesterdayIncome,
  }) = _ListElement;

  factory ListElement.fromJson(Map<String, dynamic> json) =>
      _$ListElementFromJson(json);
}
