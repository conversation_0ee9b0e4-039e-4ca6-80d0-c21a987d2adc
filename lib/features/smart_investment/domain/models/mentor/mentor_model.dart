import 'package:freezed_annotation/freezed_annotation.dart';

part 'mentor_model.freezed.dart';
part 'mentor_model.g.dart';

@freezed
class MentorResponse with _$MentorResponse {
  const factory MentorResponse({
    int? code,
    MentorData? data,
    String? msg,
  }) = _MentorResponse;

  factory MentorResponse.fromJson(Map<String, dynamic> json) =>
      _$MentorResponseFromJson(json);
}

@freezed
class MentorData with _$MentorData {
  const factory MentorData({
    List<Mentor>? list,
    int? pageNum,
    int? pageSize,
    int? total,
  }) = _MentorData;

  factory MentorData.fromJson(Map<String, dynamic> json) =>
      _$MentorDataFromJson(json);
}

@freezed
class Mentor with _$Mentor {
  const factory Mentor({
    String? avatar,
    String? bio,
    String? company,
    int? id,
    double? maxDrawdown,
    int? mentorAllow,
    double? monthlyProfit,
    String? name,
    String? nickname,
    String? portfolio,
    String? position,
    int? productAllowState,
    int? status,
    double? winRate,
    int? yearsOfExperience,
    int? vipLevel,  
  }) = _Mentor;

  factory Mentor.fromJson(Map<String, dynamic> json) => _$MentorFromJson(json);
}
