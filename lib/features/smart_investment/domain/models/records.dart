import 'package:sf_app_v2/features/smart_investment/domain/models/investment_records/investment_records.dart';

class Records {
  final Map<int, InvestmentRecordModel> recordsByStatus;

  Records({Map<int, InvestmentRecordModel>? recordsByStatus})
      : recordsByStatus = recordsByStatus ?? {};

  // Add a record for a specific status
  Records addRecordForStatus(int status, InvestmentRecordModel record) {
    final updatedRecords =
        Map<int, InvestmentRecordModel>.from(recordsByStatus);
    updatedRecords[status] = record;
    return Records(recordsByStatus: updatedRecords);
  }

  // Get records for a specific status
  InvestmentRecordModel? getRecordsForStatus(int status) {
    return recordsByStatus[status];
  }

  // Check if records exist for a specific status
  bool hasRecordsForStatus(int status) {
    return recordsByStatus.containsKey(status);
  }

  // Get all statuses
  List<int> get availableStatuses => recordsByStatus.keys.toList();
}
