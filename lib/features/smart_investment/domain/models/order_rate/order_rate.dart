// To parse this JSON data, do
//
//     final orderRate = orderRateFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'order_rate.freezed.dart';
part 'order_rate.g.dart';

OrderRate orderRateFromJson(String str) => OrderRate.fromJson(json.decode(str));

String orderRateToJson(OrderRate data) => json.encode(data.toJson());

@freezed
class OrderRate with _$OrderRate {
    const factory OrderRate({
        int? code,
        OrderRateData? data,
        String? msg,
    }) = _OrderRate;

    factory OrderRate.fromJson(Map<String, dynamic> json) => _$OrderRateFromJson(json);
}

@freezed
class OrderRateData with _$OrderRateData {
    const factory OrderRateData({
        List<int>? orderRates,
        int? userId,
    }) = _OrderRateData;

    factory OrderRateData.fromJson(Map<String, dynamic> json) => _$OrderRateDataFromJson(json);
}
