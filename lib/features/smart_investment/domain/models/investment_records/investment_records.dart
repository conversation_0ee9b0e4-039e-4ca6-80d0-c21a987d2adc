import 'package:freezed_annotation/freezed_annotation.dart';

part 'investment_records.freezed.dart';
part 'investment_records.g.dart';

@freezed
class InvestmentRecord with _$InvestmentRecord {
  const factory InvestmentRecord({
    String? applyCreateTime,
    String? auditContent,
    int? commissionRate,
    int? cycle,
    double? dailyCanWithdrawAmount,
    String? expireTime,
    int? id,
    double? maxAmount,
    String? mentorName,
    double? minAmount,
    String? orderNo,
    int? processStatus,
    String? productName,
    double? singleAmount,
    String? startTime,
    double? totalAmount,
  }) = _InvestmentRecord;

  factory InvestmentRecord.fromJson(Map<String, dynamic> json) =>
      _$InvestmentRecordFromJson(json);
}

@freezed
class RecordData with _$RecordData {
  const factory RecordData({
    List<InvestmentRecord>? list,
    int? pageNum,
    int? pageSize,
    int? total,
  }) = _RecordData;

  factory RecordData.fromJson(Map<String, dynamic> json) =>
      _$RecordDataFromJson(json);
}

@freezed
class InvestmentRecordModel with _$InvestmentRecordModel {
  const factory InvestmentRecordModel({
    int? code,
    RecordData? data,
    String? msg,
  }) = _InvestmentRecordModel;

  factory InvestmentRecordModel.fromJson(Map<String, dynamic> json) =>
      _$InvestmentRecordModelFromJson(json);
}
