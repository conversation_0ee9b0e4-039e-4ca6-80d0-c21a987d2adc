// To parse this JSON data, do
//
//     final investmentResponse = investmentResponseFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'investment_respone.freezed.dart';
part 'investment_respone.g.dart';

InvestmentResponse investmentResponseFromJson( str) => InvestmentResponse.fromJson((str));

String investmentResponseToJson(InvestmentResponse data) => json.encode(data.toJson());

@freezed
class InvestmentResponse with _$InvestmentResponse {
    const factory InvestmentResponse({
        @JsonKey(name: "code")
        int? code,
        @JsonKey(name: "data")
        Data? data,
        @Json<PERSON>ey(name: "msg")
        String? msg,
    }) = _InvestmentResponse;

    factory InvestmentResponse.fromJson(Map<String, dynamic> json) => _$InvestmentResponseFromJson(json);
}

@freezed
class Data with _$Data {
    const factory Data({
        @JsonKey(name: "additionalMount")
        dynamic additionalMount,
        @JsonKey(name: "applyCreateTime")
        int? applyCreateTime,
        @JsonKey(name: "auditContent")
        String? auditContent,
        @JsonKey(name: "commissionRate")
        int? commissionRate,
        @JsonKey(name: "cycle")
        int? cycle,
        @JsonKey(name: "dailyCanWithdrawAmount")
        dynamic dailyCanWithdrawAmount,
        @JsonKey(name: "expireTime")
        String? expireTime,
        @JsonKey(name: "id")
        int? id,
        @JsonKey(name: "maxAmount")
        dynamic maxAmount,
        @JsonKey(name: "mentorId")
        int? mentorId,
        @JsonKey(name: "mentorName")
        String? mentorName,
        @JsonKey(name: "minAmount")
        dynamic minAmount,
        @JsonKey(name: "orderNo")
        String? orderNo,
        @JsonKey(name: "processStatus")
        dynamic processStatus,
        @JsonKey(name: "productName")
        String? productName,
        @JsonKey(name: "singleAmount")
        dynamic singleAmount,
        @JsonKey(name: "startTime")
        String? startTime,
        @JsonKey(name: "totalAmount")
        int? totalAmount,
        @JsonKey(name: "productId")
        int? productId,
    }) = _Data;

    factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);
}
