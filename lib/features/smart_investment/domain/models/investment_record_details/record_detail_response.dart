import 'package:freezed_annotation/freezed_annotation.dart';

part 'record_detail_response.freezed.dart';
part 'record_detail_response.g.dart';

@freezed
class RecordDetailResponse with _$RecordDetailResponse {
  const factory RecordDetailResponse({
    int? code,
    RecordDetailMainData? data,
    String? msg,
  }) = _RecordDetailResponse;

  factory RecordDetailResponse.fromJson(Map<String, dynamic> json) =>
      _$RecordDetailResponseFromJson(json);
}

@freezed
class RecordDetailMainData with _$RecordDetailMainData {
  const factory RecordDetailMainData({
    double? additionalAmount,
    List<RecordDetail>? infoList,
    double? orderAmount,
    String? orderNo,
    double? rejectAmount,
    double? totalAmount,
  }) = _RecordDetailMainData;

  factory RecordDetailMainData.fromJson(Map<String, dynamic> json) =>
      _$RecordDetailMainDataFromJson(json);
}

@freezed
class RecordDetail with _$RecordDetail {
  const factory RecordDetail({
    dynamic buyDate,
    String? buyPosition,
    double? buyPrice,
    double? buyQuantity,
    double? mentorCommission,
    double? netProfit,
    String? orderNo,
    double? platformCommission,
    dynamic sellDate,
    double? sellPrice,
    double? sellQuantity,
    String? stockCode,
    String? stockName,
    double? totalProfit,
  }) = _RecordDetail;

  factory RecordDetail.fromJson(Map<String, dynamic> json) =>
      _$RecordDetailFromJson(json);
}
