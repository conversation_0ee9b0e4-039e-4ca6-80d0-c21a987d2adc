import 'package:freezed_annotation/freezed_annotation.dart';

part 'investment_products.freezed.dart';
part 'investment_products.g.dart';

@freezed
class ProductResponse with _$ProductResponse {
  const factory ProductResponse({
    int? code,
    List<Product>? data,
    String? msg,
  }) = _ProductResponse;

  factory ProductResponse.fromJson(Map<String, dynamic> json) =>
      _$ProductResponseFromJson(json);
}

@freezed
class Product with _$Product {
  const factory Product({
    double? commissionRate,
    int? cycle,
    int? id,
    double? maxAmount,
    String? mentor,
    double? minAmount,
    String? name,
    double? singleAmount,
    int? type,
    int? sort,
  }) = _Product;

  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);
}
