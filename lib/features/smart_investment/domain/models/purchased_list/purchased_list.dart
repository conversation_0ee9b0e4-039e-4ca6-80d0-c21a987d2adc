// To parse this JSON data, do
//
//     final purchasedList = purchasedListFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'purchased_list.freezed.dart';
part 'purchased_list.g.dart';

PurchasedList purchasedListFromJson( str) => PurchasedList.fromJson((str));

String purchasedListToJson(PurchasedList data) => json.encode(data.toJson());

@freezed
class PurchasedList with _$PurchasedList {
    const factory PurchasedList({
        @Json<PERSON>ey(name: "code")
        required int code,
        @Json<PERSON>ey(name: "data")
        required List<PurchasedListData> data,
        @Json<PERSON>ey(name: "msg")
        required String msg,
    }) = _PurchasedList;

    factory PurchasedList.fromJson(Map<String, dynamic> json) => _$PurchasedListFromJson(json);
}

@freezed
class PurchasedListData with _$PurchasedListData {
    const factory PurchasedListData({
        @JsonKey(name: "buyPrice")
        required dynamic buyPrice,
        @JsonKey(name: "createTime")
        required DateTime createTime,
        @JsonKey(name: "id")
        required dynamic id,
        @JsonKey(name: "orderAmount")
        required double orderAmount,
        @JsonKey(name: "orderNo")
        required String orderNo,
        @JsonKey(name: "sellPrice")
        required dynamic sellPrice,
        @JsonKey(name: "status")
        required int status,
        @JsonKey(name: "stockName")
        required String stockName,
        @JsonKey(name: "totalProfit")
        required dynamic totalProfit,
    }) = _PurchasedListData;

    factory PurchasedListData.fromJson(Map<String, dynamic> json) => _$PurchasedListDataFromJson(json);
}
