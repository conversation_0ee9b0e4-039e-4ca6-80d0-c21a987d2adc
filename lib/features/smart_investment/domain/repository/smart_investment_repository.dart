import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/smart_investment/domain/models/investment_record_details/record_detail_response.dart';
import 'package:sf_app_v2/features/smart_investment/domain/models/mentor/mentor_model.dart';
import '../models/Investment_response/investment_respone.dart';
import '../models/investment_products/investment_products.dart';
import '../models/investment_records/investment_records.dart';
import '../models/order_rate/order_rate.dart';
import '../models/purchased_list/purchased_list.dart';
import '../models/reject_response/reject_response.dart';

abstract class SmartInvestmentRepository {
  const SmartInvestmentRepository();

  Future<ResponseResult<MentorResponse>> getMentorList({
    required int pageNum,
    required int pageSize,
  });

  Future<ResponseResult<ProductResponse>> getProducts({
    required int mentorId,
  });

  Future<ResponseResult<InvestmentResponse>> purchaseProduct({
    required int productId,
    required String accountType,
    required String password,
    required int type,
    required num amount,
    required bool showRate
  });

  Future<ResponseResult<bool>> appendProduct({
    required int? orderId,
    required String accountType,
    required String password,
    required int amount,
  });

  Future<ResponseResult<InvestmentRecordModel>> getPurchasedRecords({
    required int processStatus,
  });

  Future<ResponseResult<RecordDetailResponse>> getPurchasedRecordDetails({
    required String id,
  });

  // New methods added
  Future<ResponseResult<bool>> withdrawProduct({
    required String orderNo,
    required String password,
    required int amount,
  });

  Future<ResponseResult<String>> getServiceAgreement();

  Future<ResponseResult<RejectResponse>> getProductOrderRejectPage({
    required String orderNo,
  });

  Future<ResponseResult<PurchasedList>> getPurchasedList({int? mentorId});

  Future<ResponseResult<OrderRate>> getOrderRate();
}
