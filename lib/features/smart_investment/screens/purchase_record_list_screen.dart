import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/api/network/network_helper.dart';
import '../../../core/common_function.dart';
import '../../../core/constants/enums.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/routes/routes.dart';
import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';
import '../../../core/utils/utils.dart';
import '../../../core/widgets/common_appbar.dart';
import '../../../core/widgets/common_empty_data.dart';
import '../../../core/widgets/common_shimmer.dart';
import '../domain/models/investment_record_details/record_detail_response.dart';
import '../domain/models/investment_records/investment_records.dart';
import '../logic/smart_investment/smart_investment_cubit.dart';
import '../widgets/append_amount_dialog.dart';
import '../widgets/record_card.dart';

class PurchaseHistoryScreen extends StatefulWidget {
  const PurchaseHistoryScreen({super.key});

  @override
  State<PurchaseHistoryScreen> createState() => _PurchaseHistoryScreenState();
}

// In PurchaseHistoryScreen
class _PurchaseHistoryScreenState extends State<PurchaseHistoryScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  final List<String> tabTitles = [
    StringConstants.inProcess, // 3: Following
    StringConstants.completed, // 4: Completed
    StringConstants.canceled, // 6: Cancelled
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: tabTitles.length, vsync: this);
    _tabController.addListener(_handleTabChange);
    _fetchAllRecords();
  }

  void _handleTabChange() {
    if (!_tabController.indexIsChanging) {
      _fetchRecords();
    }
  }

  // Map tab index to backend process status
  int _getProcessStatus(int tabIndex) => switch (tabIndex) {
        0 => 3, //inProcess
        1 => 4, //completed
        2 => 6, //cancelled
        _ => 0
      };

  void _fetchRecords() {
    // if (mounted) {
    //   context.read<SmartInvestmentCubit>().getPurchasedRecords(
    //         processStatus: _getProcessStatus(_tabController.index),
    //       );
    // }
  }

  void _fetchAllRecords() {
    if (mounted) {
      context.read<SmartInvestmentCubit>()
        ..getPurchasedRecords(
          processStatus: _getProcessStatus(0),
        )
        ..getPurchasedRecords(
          processStatus: _getProcessStatus(1),
        )
        ..getPurchasedRecords(
          processStatus: _getProcessStatus(2),
        );
       
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        buildContext: context,
        enableNavBack: true,
        titleWidget: Text(StringConstants.followPurchaseRecords.tr()),
        alignCenter: true,
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<SmartInvestmentCubit, SmartInvestmentState>(
            listenWhen: (previous, current) => previous.appendInvestmentStatus != current.appendInvestmentStatus,
            listener: (context, state) {
              if (state.appendInvestmentStatus == DataStatus.failed) {
                NetworkHelper.handleMessage(
                  state.error,
                  context,
                  type: HandleTypes.customDialog,
                  snackBarType: SnackBarType.error,
                );
              }
            },
          ),
          BlocListener<SmartInvestmentCubit, SmartInvestmentState>(
            listenWhen: (previous, current) => previous.withdrawInvestmentStatus != current.withdrawInvestmentStatus,
            listener: (context, state) {
              if (state.withdrawInvestmentStatus == DataStatus.failed) {
                NetworkHelper.handleMessage(
                  state.error,
                  context,
                  type: HandleTypes.customDialog,
                  snackBarType: SnackBarType.error,
                );
              }
            },
          ),
        ],
        child: BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
          builder: (context, state) {
            if (state.investmentRecordFetchStatus == DataStatus.loading) {
              return _buildShimmerList();
            }
            return Column(
              children: [
                TabBar(
                  indicatorColor: myColorScheme(context).primaryColor,
                  indicatorWeight: 1,
                  indicatorSize: TabBarIndicatorSize.label,
                  labelPadding: EdgeInsets.zero,
                  dividerHeight: .5,
                  dividerColor: myColorScheme(context).greyColor3,
                  controller: _tabController,
                  tabs: List.generate(
                    tabTitles.length,
                    (index) => Tab(
                      child: Text(
                        tabTitles[index].tr(),
                        style: FontPalette.medium16.copyWith(
                          color: getStatusColor(_getProcessStatus(index)),
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: List.generate(tabTitles.length, (index) {
                      int processStatus = _getProcessStatus(index);
                      return RefreshIndicator(
                        onRefresh: () => context.read<SmartInvestmentCubit>().getPurchasedRecords(
                              processStatus: processStatus, // Use the mapping function
                            ),
                        child: state.investmentRecordsByStatus[processStatus]?.data?.list?.isEmpty ?? true
                            ? _buildEmptyState()
                            : _buildRecordsList(
                                state.investmentRecordsByStatus[processStatus]?.data?.list ?? [],
                                state.recordDetails ?? [],
                              ),
                      );
                    }),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildRecordsList(
    List<InvestmentRecord> records,
    List<RecordDetail> recordDetails,
  ) {
    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: records.length,
      itemBuilder: (context, index) {
        final record = records[index];
        return PurchaseRecordCard(
          record: record,
          processStatus: record.processStatus ?? 0,
          onAppend: () => _showAppendDialog(context, record),
          onWithdraw: () => _showWithdrawDialog(context, record),
          onDetails: () => _showDetails(context, record),
        );
      },
    );
  }

  Widget _buildShimmerList() {
    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 5, // Show 5 shimmer items
      itemBuilder: (_, __) => _buildShimmerCard(),
    );
  }

  Widget _buildShimmerCard() {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with order number and status
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CommonShimmer(
                width: 120.w,
                height: 16.h,
                br: 4.r,
              ),
              CommonShimmer(
                width: 80.w,
                height: 16.h,
                br: 4.r,
              ),
            ],
          ),
          16.verticalSpace,
          // Details rows
          ...List.generate(
            3,
            (index) => Padding(
              padding: EdgeInsets.only(bottom: 12.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonShimmer(
                    width: 100.w,
                    height: 14.h,
                    br: 4.r,
                  ),
                  CommonShimmer(
                    width: 80.w,
                    height: 14.h,
                    br: 4.r,
                  ),
                ],
              ),
            ),
          ),
          16.verticalSpace,
          // Action buttons
          Row(
            children: [
              Expanded(
                child: CommonShimmer(
                  width: double.infinity,
                  height: 40.h,
                  br: 20.r,
                ),
              ),
              12.horizontalSpace,
              Expanded(
                child: CommonShimmer(
                  width: double.infinity,
                  height: 40.h,
                  br: 20.r,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(child: CommonEmpty(topPadding: 0));
  }

  void _showAppendDialog(BuildContext context, InvestmentRecord record) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => AmountInputDialog(
        type: AmountInputType.append,
        minAmount: record.singleAmount ?? 0,
        maxAmount: record.maxAmount ?? 0,
        singleAmount: record.singleAmount ?? 0,
        productId: record.id.toString(),
        totalAmount: record.totalAmount ?? 0,
        onSuccess: () => context.read<SmartInvestmentCubit>().getPurchasedRecords(),
      ),
    );
  }

  void _showWithdrawDialog(BuildContext context, InvestmentRecord record) {
    if (record.dailyCanWithdrawAmount == null || record.dailyCanWithdrawAmount == 0) {
      CommonFunctions().showFlutterToast('No profit available for withdrawal');
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => AmountInputDialog(
        type: AmountInputType.withdraw,
        minAmount: 1,
        maxAmount: record.dailyCanWithdrawAmount!,
        orderNo: record.orderNo,
        productId: record.id.toString(),
        onSuccess: () => context.read<SmartInvestmentCubit>().getPurchasedRecords(),
      ),
    );
  }

  void _showDetails(BuildContext context, InvestmentRecord record) {
    Navigator.pushNamed(
      context,
      routeSmartInvestmentRecordDetailScreen,
      arguments: {
        'id': record.orderNo,
      },
    );
  }
}
