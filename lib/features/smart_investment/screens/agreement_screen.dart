import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../core/constants/assets.dart';
import '../../../core/constants/enums.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/theme/color_pallette.dart';
import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';
import '../../../core/widgets/common_appbar.dart';
import '../../../core/widgets/common_empty_data.dart';
import '../logic/smart_investment/smart_investment_cubit.dart';

class ServiceAgreementScreen extends StatefulWidget {
  const ServiceAgreementScreen({super.key});

  @override
  State<ServiceAgreementScreen> createState() => _ServiceAgreementScreenState();
}

class _ServiceAgreementScreenState extends State<ServiceAgreementScreen> {
  @override
  void initState() {
    super.initState();
    context.read<SmartInvestmentCubit>().getServiceAgreement();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        buildContext: context,
        titleWidget: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                SvgPicture.asset(Assets.termsAndConditionIcon),
                6.horizontalSpace,
                Text(
                  StringConstants.serviceAgreement.tr(),
                  style: FontPalette.semiBold16.copyWith(
                    color: myColorScheme(context).titleColor ?? Colors.black,
                  ),
                ),
              ],
            ),
            Text(
              StringConstants.datePrivacy.tr(),
              style: FontPalette.normal12
                  .copyWith(color: ColorPalette.secondaryVar1),
            ),
          ],
        ),
      ),
      body: SafeArea(
        child: BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
          builder: (context, state) {
            if (state.serviceAgreementFetchStatus == DataStatus.success) {
              return SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(20).r,
                  child: Html(data: state.serviceAgreement ?? ''),
                ),
              );
            } else if (state.serviceAgreementFetchStatus == DataStatus.failed) {
              return const Center(child: CommonEmpty());
            } else {
              return Padding(
                padding: const EdgeInsets.fromLTRB(0, 40, 0, 0).r,
                child:
                    const Center(child: CircularProgressIndicator.adaptive()),
              );
            }
          },
        ),
      ),
    );
  }
}
