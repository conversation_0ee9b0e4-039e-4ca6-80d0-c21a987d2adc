import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app_v2/core/common_function.dart';
import 'package:sf_app_v2/core/extention.dart';

import '../../../core/constants/enums.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/widgets/common_appbar.dart';
import '../domain/models/investment_record_details/record_detail_response.dart';
import '../logic/smart_investment/smart_investment_cubit.dart';
import '../widgets/purchase_detail_widgets.dart';

/// Screen that displays detailed information about a purchase record
/// including transaction details, trade information, and revenue calculations
class PurchaseDetailScreen extends StatefulWidget {
  final String transactionId;

  const PurchaseDetailScreen({
    super.key,
    required this.transactionId,
  });

  @override
  State<PurchaseDetailScreen> createState() => _PurchaseDetailScreenState();
}

class _PurchaseDetailScreenState extends State<PurchaseDetailScreen> {
  @override
  void initState() {
    super.initState();
    CommonFunctions.afterInit(() {
      _fetchData();
    });
  }

  /// Fetches both record details and reject response data
  void _fetchData() {
    final cubit = context.read<SmartInvestmentCubit>();
    cubit.fetchRecordDetails(
      id: widget.transactionId,
      pageNum: 1,
    );
    cubit.getProductOrderRejectPage(orderNo: widget.transactionId);
  }

  String _generateRevenueFormula(RecordDetail detail) {
    final totalCommission =
        (detail.mentorCommission ?? 0) + (detail.platformCommission ?? 0);
    final isOngoingTrade = detail.sellDate == null;

    if (isOngoingTrade) {
      return [
        '${StringConstants.revenue.tr()} (0.00'.toCurrency(),
        ') = ${StringConstants.totalRevenue.tr()} (0.00'.toCurrency(),
        ') - ${StringConstants.commission.tr()} (',
        totalCommission.toStringAsFixed(2).toCurrency(),
        ')',
      ].join();
    }

    return [
      '${StringConstants.revenue.tr()} (',
      detail.netProfit?.toStringAsFixed(2).toCurrency() ?? '0.00'.toCurrency(),
      ') = ${StringConstants.totalRevenue.tr()} (',
      detail.totalProfit?.toStringAsFixed(2).toCurrency() ?? '0.00'.toCurrency(),
      ') - ${StringConstants.tutorCommission.tr()} (',
      detail.mentorCommission?.toStringAsFixed(2).toCurrency() ?? '0.00'.toCurrency(),
      ') - ${StringConstants.platformCommission.tr()} (',
      detail.platformCommission?.toStringAsFixed(2).toCurrency() ?? '0.00'.toCurrency(),
      ')',
    ].join();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonAppBar(
        buildContext: context,
        enableNavBack: true,
        titleWidget: Text(StringConstants.transactionRecords.tr()),
        alignCenter: true,
      ),
      body: BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
        builder: (context, state) {
          if (state.recordDetailFetchStatus == DataStatus.loading) {
            return const ShimmerView();
          }

          return RefreshIndicator(
            onRefresh: () async => _fetchData(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SummaryCard(
                    data: state.recordDetailData,
                    transactionId: widget.transactionId,
                  ),
                  if ((state.recordDetailData?.rejectAmount ?? 0) > 0)
                    const RejectCard(),
                  if (state.recordDetails?.isEmpty ?? true)
                    const EmptyState()
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.recordDetails!.length,
                      itemBuilder: (context, index) {
                        final detail = state.recordDetails![index];
                        return Column(
                          children: [
                            TradeCard(detail: detail),
                            RevenueDetails(
                              detail: detail,
                              formula: _generateRevenueFormula(detail),
                            ),
                          ],
                        );
                      },
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

}
