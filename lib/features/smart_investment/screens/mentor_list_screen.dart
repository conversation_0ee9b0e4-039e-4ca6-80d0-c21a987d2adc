import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/widgets/common_appbar.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';
import 'package:sf_app_v2/core/widgets/pagination_widget.dart';
import 'package:sf_app_v2/features/smart_investment/domain/models/mentor/mentor_model.dart';
import 'package:sf_app_v2/features/smart_investment/logic/smart_investment/smart_investment_cubit.dart';
import 'package:sf_app_v2/features/smart_investment/widgets/mentor_card.dart';

import '../../../core/constants/string_constants.dart';

class MentorListScreen extends StatefulWidget {
  final bool showBackButton;

  const MentorListScreen({
    super.key,
    this.showBackButton = false,
  });

  @override
  State<MentorListScreen> createState() => _MentorListScreenState();
}

class _MentorListScreenState extends State<MentorListScreen> {
  @override
  void initState() {
    super.initState();
    _fetchInitialData();
  }

  void _fetchInitialData() {
    context.read<SmartInvestmentCubit>().getMentorList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            await context.read<SmartInvestmentCubit>().getMentorList();
          },
          child: BlocSelector<SmartInvestmentCubit, SmartInvestmentState, (DataStatus, MentorResponse?)>(
            selector: (state) => (state.mentorListFetchStatus, state.mentorListData),
            builder: (context, state) {
              return PaginationWidget(
                isPaginating: state.$1 == DataStatus.loading,
                next: (state.$2?.data?.list?.length ?? 0) <= (state.$2?.data?.total ?? 0),
                onPagination: (notification) {
                  if ((state.$2?.data?.list?.length ?? 0) == (state.$2?.data?.total ?? 0)) {
                    return false;
                  }
                  context.read<SmartInvestmentCubit>().getMentorList(
                        page: (state.$2?.data?.pageNum ?? 0) + 1,
                        isLoadMore: true,
                      );
                  return true;
                },
                child: CustomScrollView(
                  physics: const BouncingScrollPhysics(
                    parent: AlwaysScrollableScrollPhysics(),
                  ),
                  slivers: [
                    _AppBarWidget(
                      context: context,
                      showBackButton: widget.showBackButton,
                    ),
                    const _MentorListWidget(),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

class _AppBarWidget extends StatelessWidget {
  final BuildContext context;
  final bool showBackButton;

  const _AppBarWidget({
    required this.context,
    required this.showBackButton,
  });

  @override
  Widget build(BuildContext context) {
    return CommonSliverAppBar(
      buildContext: this.context,
      enableNavBack: showBackButton,
      titleWidget: Text(
        StringConstants.mentors.tr(),
        style: FontPalette.semiBold20.copyWith(
          color: Theme.of(context).textTheme.titleLarge?.color,
        ),
      ),
      centerTitleText: true,
    );
  }
}

class _MentorListWidget extends StatelessWidget {
  const _MentorListWidget();

  void _navigateToDetail(Mentor mentor, BuildContext context) {
    Navigator.pushNamed(
      context,
      routeSmartInvestmentScreen,
      arguments: {'mentor': mentor},
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SmartInvestmentCubit, SmartInvestmentState>(
      builder: (context, state) {
        if (state.mentorListFetchStatus == DataStatus.loading && state.mentorListData == null) {
          return SliverPadding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) => const _ShimmerMentorCard(),
                childCount: 5,
              ),
            ),
          );
        }
        final mentors = state.mentorListData?.data?.list ?? [];

        return SliverPadding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          sliver: AnimationLimiter(
            child: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final mentor = mentors[index];
                  return AnimationConfiguration.staggeredList(
                    position: index,
                    duration: const Duration(milliseconds: 500),
                    child: SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(
                        child: Padding(
                          padding: EdgeInsets.only(bottom: 16.h),
                          child: MentorCard(
                            mentor: mentor,
                            onFollowTap: () => _navigateToDetail(
                              mentor,
                              context,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
                childCount: mentors.length,
              ),
            ),
          ),
        );
      },
    );
  }
}

class _ShimmerMentorCard extends StatelessWidget {
  const _ShimmerMentorCard();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Container(
        padding: EdgeInsets.all(16.r),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CommonShimmer(
                  width: 50.w,
                  height: 50.w,
                  br: 25.r,
                ),
                12.horizontalSpace,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonShimmer(
                        width: 120.w,
                        height: 16.h,
                        br: 4.r,
                      ),
                      8.verticalSpace,
                      CommonShimmer(
                        width: 80.w,
                        height: 14.h,
                        br: 4.r,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            24.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: List.generate(
                3,
                (index) => Column(
                  children: [
                    CommonShimmer(
                      width: 60.w,
                      height: 14.h,
                      br: 4.r,
                    ),
                    8.verticalSpace,
                    CommonShimmer(
                      width: 40.w,
                      height: 12.h,
                      br: 4.r,
                    ),
                  ],
                ),
              ),
            ),
            24.verticalSpace,
            Center(
              child: CommonShimmer(
                width: 1.sw,
                height: 40.h,
                br: 20.r,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
