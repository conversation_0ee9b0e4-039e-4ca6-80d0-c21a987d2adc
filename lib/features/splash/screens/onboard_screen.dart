import 'package:action_slider/action_slider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/constants/keys.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/utils/shared_preference_helper.dart';

import '../../../core/constants/assets.dart';
import '../../../core/constants/string_constants.dart';

class OnBoardScreen extends StatelessWidget {
  const OnBoardScreen({super.key});

  Future<void> _manageOnBoard(BuildContext context) async {
    await SharedPreferenceHelper().writeBoolData(
      SharedPreferencesKeys.isOnBoard,
      true,
    );
    if (!context.mounted) return;
    Navigator.pushReplacementNamed(context, routeMainScreen);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(0),
        child: AppBar(
          systemOverlayStyle: SystemUiOverlayStyle.dark,
          elevation: 0,
          backgroundColor: Colors.transparent,
        ),
      ),
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Spacer(),
              Padding(
                padding: const EdgeInsets.all(30).r,
                child: Hero(
                  tag: Assets.logoSvg,
                  child: SvgPicture.asset(
                    Assets.logoSvg,
                    width: 166.w,
                    height: 202.h,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                StringConstants.buildYourProfile.tr(),
                style: FontPalette.extraBold25.copyWith(fontSize: 30.sp),
              ),
              16.verticalSpace,
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 73.w),
                child: Text(
                  StringConstants.onBoardScreenSubtitle.tr(),
                  textAlign: TextAlign.center,
                  style: FontPalette.normal14,
                ),
              ),
              const Spacer(),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 28.0),
                child: SlideButton(
                  text: StringConstants.letsStart.tr(),
                  boxInsideColor: myColorScheme(context).primaryColor ??
                      ColorPalette.primaryColor,
                  boxOutsideColor: myColorScheme(context).greyColor1 ??
                      ColorPalette.greyColor1,
                  onSubmit: () async => await _manageOnBoard(context),
                ),
              ),
              16.verticalSpace,
            ],
          ),
        ),
      ),
    );
  }
}

class SlideButton extends StatelessWidget {
  final void Function()? onSubmit;
  final Color boxInsideColor;
  final Color boxOutsideColor;
  final String text;

  const SlideButton({
    super.key,
    required this.text,
    this.onSubmit,
    required this.boxInsideColor,
    required this.boxOutsideColor,
  });

  @override
  Widget build(BuildContext context) {
    return ActionSlider.standard(
      backgroundColor: boxOutsideColor,
      icon: Icon(
        Icons.arrow_forward_ios_outlined,
        color: Colors.white,
        size: 20.sp,
      ),
      loadingIcon: const CircularProgressIndicator(
        color: Colors.white,
      ),
      successIcon: const Icon(
        Icons.check,
        color: Colors.white,
      ),
      toggleColor: boxInsideColor,
      action: (controller) async {
        controller.loading();
        await Future.delayed(const Duration(milliseconds: 500));
        controller.success();
        await Future.delayed(const Duration(seconds: 1));
        onSubmit?.call();
      },
      child: SizedBox(
        width: 250.w,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            36.horizontalSpace,
            Expanded(child: Text(text, style: FontPalette.medium16)),
            SvgPicture.asset(
              Assets.arrowForward,
              width: 10.w,
              height: 17.h,
            ),
            4.horizontalSpace,
            SvgPicture.asset(
              Assets.arrowForward,
              width: 10.w,
              height: 17.h,
            ),
            10.horizontalSpace,
            SvgPicture.asset(
              Assets.arrowForward,
              width: 10.w,
              height: 17.h,
              color: ColorPalette.white,
            ),
          ],
        ),
      ),
    );
  }
}
