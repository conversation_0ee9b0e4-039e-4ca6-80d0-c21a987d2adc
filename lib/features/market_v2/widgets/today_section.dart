import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/widgets/shared_error.dart';
import 'package:sf_app_v2/features/market_v2/logic/market/market_cubit.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/dist_response.dart';
import 'package:sf_app_v2/features/market_v2/widgets/today_market_bar.dart';

class TodaySection extends StatelessWidget {
  const TodaySection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<MarketCubit, MarketState, (DataStatus, DistResponse?)>(
      selector: (state) => (state.distFetchStatus, state.distResponse),
      builder: (context, state) {
        final list = state.$2?.data?.list;
        final low = state.$2?.data?.low;
        final up = state.$2?.data?.up;
        final zero = state.$2?.data?.zero;

        if (state.$1 == DataStatus.loading && list == null) {
          return _buildLoading(context);
        }

        if (state.$1 == DataStatus.failed || list == null) {
          return const SharedError();
        }

        return TodayMarketBar(
          list: list,
          low: low,
          up: up,
          zero: zero,
        );
      },
    );
  }

  Widget _buildLoading(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: const CommonShimmer(
        height: 160,
        width: double.infinity,
      ),
    );
  }
}
