import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lucide/flutter_lucide.dart';

import '../../../core/constants/string_constants.dart';


class TodayMarketStatus extends StatelessWidget {
  final int? upCount;
  final int? flatCount;
  final int? downCount;

  const TodayMarketStatus({
    super.key,
    this.upCount,
    this.flatCount,
    this.downCount,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildStatus(
          icon: LucideIcons.arrow_up,
          label: StringConstants.up.tr(),
          count: upCount,
          color: Colors.green.shade600,
        ),
        const SizedBox(width: 10),
        _buildStatus(
          icon: LucideIcons.minus,
          label: StringConstants.flat.tr(),
          count: flatCount,
          color: Colors.grey,
        ),
        const SizedBox(width: 10),
        _buildStatus(
          icon: LucideIcons.arrow_down,
          label: StringConstants.down.tr(),
          count: downCount,
          color: Colors.red.shade600,
        ),
      ],
    );
  }

  Widget _buildStatus({
    required IconData icon,
    required String label,
    required int? count,
    required Color color,
  }) {
    if (count == null) return Container();
    return Row(
      children: [
        // Icon(icon, color: color, size: 12),
        // const SizedBox(width: 4),
        Text(
          "$label: ",
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: color,
            fontSize: 11,
          ),
        ),
        Text(
          "$count",
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: color,
            fontSize: 11,
          ),
        ),
      ],
    );
  }
}
