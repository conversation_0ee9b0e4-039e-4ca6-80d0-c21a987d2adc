import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';
import 'package:sf_app_v2/core/widgets/shared_error.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';
import 'package:sf_app_v2/features/market_v2/logic/market/market_cubit.dart';
import 'package:sf_app_v2/features/market_v2/widgets/visual_graph_card.dart';

class VisualGraphSection extends StatelessWidget {
  const VisualGraphSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<MarketCubit, MarketState, (DataStatus, StockResponse?)>(
      selector: (state) => (state.stockFetchStatus, state.stockResponse),
      builder: (context, state) {
        final list = state.$2?.data?.list;

        if (state.$1 == DataStatus.loading && list == null) {
          return _buildLoading();
        }

        if (state.$1 == DataStatus.failed || list == null) {
          return const SharedError();
        }

        return IntrinsicHeight(
          child: Row(
            children: list
                .map(
                  (stock) => Expanded(
                    child: VisualGraphCard(
                      data: stock,
                    ),
                  ),
                )
                .toList(),
          ),
        );
      },
    );
  }

  Widget _buildLoading() {
    return Row(
      children: List.generate(
        3,
        (index) => Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 3),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: const CommonShimmer(
                height: 160,
                width: 30,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
