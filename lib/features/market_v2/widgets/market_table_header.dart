import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

class MarketTableHeader extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback onTap;

  const MarketTableHeader({
    super.key,
    required this.title,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: onTap,
          child: Text(
            title,
            style: FontPalette.medium14.copyWith(
              color: isSelected
                  ? myColorScheme(context).primaryColor
                  : myColorScheme(context).titleColor,
            ),
          ),
        ),
        if (isSelected) ...[
          5.verticalSpace,
          Container(
            width: 30.w,
            height: 3.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.0),
              color: myColorScheme(context).primaryColor,
            ),
          ),
        ],
      ],
    );
  }
}
