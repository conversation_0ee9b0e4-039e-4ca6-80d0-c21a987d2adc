import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/constants/app_constants.dart';
import 'package:sf_app_v2/core/utils/functions.dart';
import 'package:sf_app_v2/core/utils/utils.dart';
import 'package:sf_app_v2/features/market_v2/logic/market/market_cubit.dart';
import 'package:sf_app_v2/features/market_v2/kline_detail_screen.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';

import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';

class MarketGeneralCard extends StatelessWidget {
  final StockItem? data;

  const MarketGeneralCard({
    required this.data,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (data == null) return const SizedBox.shrink();
    final title = data?.name ?? "N/A";
    final percentage = '${(data!.gain ?? 0.0) * 100}';
    final symbol = data?.symbol ?? "N/A";
    final price = '${data?.latestPrice ?? 0.0}';
    final leadupGain = '${(data!.leadupGain ?? 0.0) * 100}';
    
    // Create an AutoSizeGroup to synchronize text sizes
    final autoSizeGroup = AutoSizeGroup();

    return GestureDetector(
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute(
          builder: (ctx) => BlocProvider.value(
            value: context.read<MarketCubit>(),
            child: KlineDetailScreen(
              stock: data,
            ),
          ),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: myColorScheme(context).cardColor ?? Colors.white,
          border: Border.all(
            color: Colors.black.withValues(alpha: 0.1),
          ),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  symbol,
                  style: FontPalette.semiBold12.copyWith(
                    color: myColorScheme(context).subTitleColor,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 8.h),
                Text(
                  '${(data!.gain ?? 0.0) > 0 ? "+" : ""}${formatNumber(percentage)}%',
                  style: FontPalette.semiBold16.copyWith(
                    color: (data!.gain ?? 0.0).getValueColor(context),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4.h),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 10.sp,
                    fontWeight: FontWeight.w400,
                    height: 1,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.start,
                ),
              ],
            ),
            SizedBox(
              width: double.infinity,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 1,
                    child: AutoSizeText(
                      '${(data!.latestPrice ?? 0.0) > 0 ? "+" : ""}${AppConstants.currencySymbol} ${formatNumber(price)}',
                      group: autoSizeGroup,
                      textAlign: TextAlign.start,
                      minFontSize: 8,
                      maxFontSize: 11,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: (data!.latestPrice ?? 0.0).getValueColor(context),
                      ),
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Expanded(
                    flex: 1,
                    child: AutoSizeText(
                      '${(data!.leadupGain ?? 0.0) > 0 ? "+" : ""}${formatNumber(leadupGain)}%',
                      group: autoSizeGroup,
                      textAlign: TextAlign.end,
                      minFontSize: 8,
                      maxFontSize: 11,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: (data!.leadupGain ?? 0.0).getValueColor(context),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
