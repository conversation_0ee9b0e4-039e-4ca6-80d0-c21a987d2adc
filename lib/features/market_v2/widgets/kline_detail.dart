import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/utils/utils.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';
import 'package:sf_app_v2/features/market_v2/widgets/ui/sub_heading.dart';

import '../../../core/constants/string_constants.dart';

class KlineDetail extends StatelessWidget {
  const KlineDetail({
    super.key,
    required this.detail,
  });

  final StockItem? detail;

  @override
  Widget build(BuildContext context) {
    final market = detail?.market ?? "N/A";
    final symbol = detail?.symbol ?? "N/A";
    final latestPrice = detail?.latestPrice?.toStringAsFixed(2) ?? "N/A";
    final high = detail?.high ?? 0.0;
    final low = detail?.low ?? 0.0;
    final open = detail?.open ?? 0.0;
    final close = detail?.close ?? 0.0;

    final chg = detail?.chg ?? 0.0;

    final dif = (detail?.latestPrice ?? 0) - close;
    final difOpenClose = close - open;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(market: market, symbol: symbol, context: context),
        _buildPrice(latestPrice, context),
        _buildChanges(
            chg: chg, difOpenClose: difOpenClose, dif: dif, context: context),
        _buildStats(
          open: open,
          close: close,
          high: high,
          low: low,
          context: context,
        ),
      ],
    );
  }

  Widget _buildHeader({
    required String market,
    required String symbol,
    required BuildContext context,
  }) {
    return Wrap(
      children: [
        Text(
          symbol,
          style: FontPalette.bold14
              .copyWith(color: myColorScheme(context).titleColor),
        ),
      ],
    );
  }

  Widget _buildPrice(String price, BuildContext context) {
    final latestPrice = detail?.latestPrice ?? 0.0;
    final close = detail?.close ?? 0.0;
    final dif = latestPrice - close;
    // final textColor = dif > 0 ? const Color.fromRGBO(61, 189, 134, 1) : Colors.red.shade600;
    final textColor = dif.getValueColor(context,
        greenColor: const Color.fromRGBO(61, 189, 134, 1));

    return Transform.translate(
      offset: const Offset(0, -10),
      child: Text(
        price,
        style: TextStyle(
          fontSize: 36,
          fontWeight: FontWeight.bold,
          color: textColor,
        ),
      ),
    );
  }

  Widget _buildChanges({
    required double chg,
    required double difOpenClose,
    required double dif,
    required BuildContext context,
  }) {
    final textColor = dif.getValueColor(context,
        greenColor: const Color.fromRGBO(61, 189, 134, 1));
    final close = detail?.close ?? 0.0;
    return Transform.translate(
      offset: const Offset(0, -15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            // '${(dif) > 0 ? "+" : ""}${(dif).toStringAsFixed(2)}',
            (dif).toStringAsFixed(3),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: textColor,
              height: 1.2,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            // '${dif > 0 ? '+' : ''}${(dif / close * 100).toStringAsFixed(2)}',
            "${(dif / close * 100).toStringAsFixed(3)}%",
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: textColor,
              height: 1.2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStats({
    required double open,
    required double close,
    required double high,
    required double low,
    required BuildContext context,
  }) {
    return Transform.translate(
      offset: const Offset(0, -10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              children: [
                SubHeading(
                  icon: Icons.arrow_circle_up,
                  label: StringConstants.open.tr(),
                  value: open.toStringAsFixed(3),
                ),
                10.verticalSpace,
                SubHeading(
                  icon: Icons.close,
                  label: StringConstants.close.tr(),
                  value: close.toStringAsFixed(3),
                ),
              ],
            ),
          ),
          15.horizontalSpace,
          Expanded(
            child: Column(
              children: [
                SubHeading(
                  icon: Icons.trending_up,
                  label: StringConstants.high.tr(),
                  value: high.toStringAsFixed(3),
                ),
                10.verticalSpace,
                SubHeading(
                  icon: Icons.trending_down,
                  label: StringConstants.low.tr(),
                  value: low.toStringAsFixed(3),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
