import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_kline_data.dart';
import 'package:k_chart_plus/k_chart_plus.dart';


class KlineList extends StatefulWidget {
  const KlineList(
      {super.key, required this.list, required this.selectedPeriod});

  final List<KlineItem>? list;
  final String selectedPeriod;

  @override
  State<KlineList> createState() => _KlineListState();
}

class _KlineListState extends State<KlineList> {
  List<KLineEntity>? datas;
  final MainState _mainState = MainState.COMBINE;
  final List<SecondaryState> _secondaryStateLi = [];
  ChartStyle chartStyle = ChartStyle();

  ChartColors chartColors = ChartColors(

    upColor: Colors.redAccent,
    dnColor: const Color(0xFF00C853),
    gridColor: Colors.transparent,
  );

  void _processData(bool isRealTime) {
    if (isRealTime) {
      List<KLineEntity> klineEntities = [];
      double? previousPrice;

      for (var item in widget.list ?? []) {
        final openPrice =
            previousPrice ?? (isRealTime ? item.price ?? 0 : item.open ?? 0);
        final klineEntity = KLineEntity.fromCustom(
          time: item.time != null ? item.time! * 1000 : 0,
          close: isRealTime ? item.price ?? 0 : item.close ?? 0,
          open: openPrice,
          high: isRealTime ? item.price ?? 0 : item.high ?? 0,
          low: isRealTime ? item.price ?? 0 : item.low ?? 0,
          vol: item.volume ?? 0,
          amount: item.price ?? 0,
        );

        klineEntities.add(klineEntity);
        previousPrice = item.price ?? 0;
      }

      setState(() {
        try {
          datas = klineEntities;
          if (datas != null) {
            DataUtil.calculate(datas!);
          }
        } catch (e) {
          log(e.toString());
        }
      });
    } else {
      final klineEntities = (widget.list ?? []).map((item) {
        return KLineEntity.fromCustom(
          time: item.time != null ? item.time! * 1000 : 0,
          close: item.close ?? 0,
          open: item.open ?? 0,
          high: item.high ?? 0,
          low: item.low ?? 0,
          vol: item.volume ?? 0,
          amount: item.price ?? 0,
        );
      }).toList();

      setState(() {
        datas = klineEntities;
        if (datas != null) {
          DataUtil.calculate(datas!);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isRealTime = widget.selectedPeriod == "realtime";
    _processData(isRealTime);

    return KChartWidget(
      datas,
      chartStyle,
      chartColors.copyWith(bgColor: myColorScheme(context).cardColor),
      mBaseHeight: 0.35.sh,
      isTrendLine: true,
      mainState: _mainState,
      volHidden: false,
      isTapShowInfoDialog: true,
      secondaryStateLi: _secondaryStateLi.toSet(),
      fixedLength: 2,
      timeFormat: TimeFormat.YEAR_MONTH_DAY_WITH_HOUR,
      verticalTextAlignment: VerticalTextAlignment.right,
      isLine: isRealTime,
      xFrontPadding: 100,
    );
  }
}
