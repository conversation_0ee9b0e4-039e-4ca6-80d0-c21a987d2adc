import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app_v2/features/market_v2/logic/market/market_cubit.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/plate_response.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';
import 'package:sf_app_v2/features/market_v2/widgets/general_card.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/widgets/shared_error.dart';
import 'package:shimmer_animation/shimmer_animation.dart';

import '../../../core/theme/color_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';

class MarketGeneralSection extends StatelessWidget {
  const MarketGeneralSection({
    super.key,
    required this.type,
  });

  final int type;

  @override
  Widget build(BuildContext context) {
    return BlocSelector<MarketCubit, MarketState, (DataStatus, PlateResponse?)>(
      selector: (state) =>
          type == 1 ? (state.plateFetchStatusA, state.plateResponseA) : (state.plateFetchStatusB, state.plateResponseB),
      builder: (context, state) {
        final list = state.$2?.data?.list;
        if (state.$1 == DataStatus.loading && list == null) {
          return _buildLoading(context);
        }
        if (state.$1 == DataStatus.failed || list == null) {
          return const SharedError();
        }

        return _buildGrid(context, list);
      },
    );
  }

  Widget _buildLoading(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 0.98,
        ),
        itemCount: 8,
        itemBuilder: (context, index) {
          return Shimmer(
            color: Theme.of(context).brightness == Brightness.dark ? ColorPalette.shadowColorDark : Colors.white,
            colorOpacity: 0.1,
            child: SizedBox(
              child: Container(
                height: 130,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: myColorScheme(context).shimmerColor,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildGrid(BuildContext context, List<StockItem> data) {
    return Container(
      alignment: Alignment.center,
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 0.98,
        ),
        itemCount: data.length,
        itemBuilder: (context, index) {
          return MarketGeneralCard(data: data[index]);
        },
      ),
    );
  }
}
