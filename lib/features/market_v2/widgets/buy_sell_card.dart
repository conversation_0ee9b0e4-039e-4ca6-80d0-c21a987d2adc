import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/depth_quote_model.dart';
import 'package:sf_app_v2/features/market_v2/logic/market/market_cubit.dart';

class DiagonalProgressPainter extends CustomPainter {
  final double buyPercentage;
  final double sellPercentage;

  DiagonalProgressPainter({
    required this.buyPercentage,
    required this.sellPercentage,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();

    // Calculate the split point with gap
    final splitX = size.width * (buyPercentage / 100);
    const gapWidth = 4.0; // Gap between sections

    // Draw green (buy) section with diagonal right edge
    paint.color = const Color(0xFF4CAF50);
    final greenPath = Path();
    greenPath.moveTo(0, 0);
    greenPath.lineTo(splitX - gapWidth - 4, 0);
    greenPath.lineTo(splitX - gapWidth + 4, size.height);
    greenPath.lineTo(0, size.height);
    greenPath.close();
    canvas.drawPath(greenPath, paint);

    // Draw red (sell) section with diagonal left edge
    paint.color = const Color(0xFFE53E3E);
    final redPath = Path();
    redPath.moveTo(splitX + gapWidth - 4, 0);
    redPath.lineTo(size.width, 0);
    redPath.lineTo(size.width, size.height);
    redPath.lineTo(splitX + gapWidth + 4, size.height);
    redPath.close();
    canvas.drawPath(redPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class BuySellCard extends StatelessWidget {
  const BuySellCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocSelector<MarketCubit, MarketState,
        (DataStatus, DepthQuoteModel?)>(
      selector: (state) =>
          (state.depthQuoteFetchStatus, state.depthQuoteResponse),
      builder: (context, state) {
        final data = state.$2;

        if (state.$1 == DataStatus.loading && data == null) {
          return Container();
        }

        final bidVolume = data!.data?.bid?.first.vol ?? 0;
        final askVolume = data.data?.ask?.first.vol ?? 0;

        final buyPercentage = (bidVolume / (bidVolume + askVolume)) * 100;
        final sellPercentage = 100 - buyPercentage;

        final buyPrice = data.data?.bid?.first.price ?? 0;
        final sellPrice = data.data?.ask?.first.price ?? 0;
        final buyVolume = bidVolume;
        final sellVolume = askVolume;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Buy/Sell labels
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  StringConstants.buy.tr(),
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                Text(
                  StringConstants.sell.tr(),
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            // Percentage bar with diagonal separation
            Row(
              children: [
                // Buy percentage (left side)
                Text(
                  '${buyPercentage.toStringAsFixed(2)}%',
                  style: const TextStyle(
                    color: Color(0xFF4CAF50), // Green color
                    fontWeight: FontWeight.w600,
                    fontSize: 10,
                  ),
                ),
                const SizedBox(width: 4),
                // Progress bar
                Expanded(
                  child: Container(
                    height: 10,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: CustomPaint(
                        painter: DiagonalProgressPainter(
                          buyPercentage: buyPercentage,
                          sellPercentage: sellPercentage,
                        ),
                        child: Container(),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 4),
                // Sell percentage (right side)
                Text(
                  '${sellPercentage.toStringAsFixed(2)}%',
                  style: const TextStyle(
                    color: Color(0xFFE53E3E), // Red color
                    fontWeight: FontWeight.w600,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // BBO information section
            Row(
              children: [
                Expanded(
                  child: Container(
                    color: const Color(0xFFE8F5E9),
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        const Text(
                          "BBO",
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        Text(
                          buyPrice.toStringAsFixed(4),
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        Text(
                          buyVolume.toString(),
                          style: const TextStyle(color: Colors.black),
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    color: const Color(0xFFFDECEA),
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        const Text(
                          "BBO",
                          style: TextStyle(fontWeight: FontWeight.w500),
                        ),
                        Text(
                          sellPrice.toStringAsFixed(4),
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        Text(
                          sellVolume.toString(),
                          style: const TextStyle(color: Colors.black),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}
