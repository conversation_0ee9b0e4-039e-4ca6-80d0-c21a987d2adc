import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';
import 'package:sf_app_v2/features/market_v2/widgets/market_table_row.dart';

import '../../../core/constants/enums.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/theme/font_pallette.dart';
import '../logic/market/market_cubit.dart';
import 'market_table_header.dart';

class MarketDataTable extends StatefulWidget {
  const MarketDataTable({super.key});

  @override
  State<MarketDataTable> createState() => _MarketDataTableState();
}

class _MarketDataTableState extends State<MarketDataTable> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        BlocSelector<MarketCubit, MarketState, int>(
          selector: (state) => state.selectedTableTab,
          builder: (context, selectedTab) {
            return Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                MarketTableHeader(
                  title: StringConstants.hotStocks.tr(),
                  isSelected: selectedTab == 0,
                  onTap: () => context.read<MarketCubit>().updateTableTab(0),
                ),
                15.horizontalSpace,
                MarketTableHeader(
                  title: StringConstants.gainers.tr(),
                  isSelected: selectedTab == 1,
                  onTap: () => context.read<MarketCubit>().updateTableTab(1),
                ),
                15.horizontalSpace,
                MarketTableHeader(
                  title: StringConstants.losers.tr(),
                  isSelected: selectedTab == 2,
                  onTap: () => context.read<MarketCubit>().updateTableTab(2),
                ),
              ],
            );
          },
        ),
        10.verticalSpace,
        Divider(color: myColorScheme(context).borderColor),
        10.verticalSpace,
        Container(
          decoration: BoxDecoration(
            color: myColorScheme(context).cardColor,
            borderRadius: BorderRadius.circular(8),
          ),
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 15.h),
          child: Column(
            children: [
              _buildHeader(),
              Divider(color: myColorScheme(context).borderColor),
              BlocSelector<MarketCubit, MarketState,
                  (DataStatus, List<StockItem>?, bool)>(
                selector: (state) => (
                  state.tableFetchStatus,
                  state.tableData?.data?.list,
                  state.isPaginating,
                ),
                builder: (context, data) {
                  final (status, items, isPaginating) = data;

                  if (status == DataStatus.loading &&
                      !isPaginating &&
                      (items?.isEmpty ?? false)) {
                    return _buildLoadingList();
                  }

                  if (items == null || items.isEmpty) {
                    return _buildEmptyState();
                  }

                  return ListView.separated(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: items.length,
                    separatorBuilder: (context, index) => Divider(
                      color: myColorScheme(context).borderColor,
                      height: 1,
                      thickness: 0.5,
                    ),
                    itemBuilder: (context, index) =>
                        MarketTableRow(data: items[index]),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingList() {
    return Column(
      children: List.generate(
        6,
        (_) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: const CommonShimmer(
              height: 40,
              width: double.infinity,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Text(
        StringConstants.noDataAvailable.tr(),
        style: FontPalette.normal14.copyWith(
          color: myColorScheme(context).subTitleColor,
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          flex: 3,
          child: Text(
            StringConstants.name.tr(),
            style: FontPalette.semiBold14.copyWith(
              color: myColorScheme(context).subTitleColor,
            ),
          ),
        ),
        Expanded(
          flex: 2,
          child: Text(
            StringConstants.latestPrice.tr(),
            textAlign: TextAlign.center,
            style: FontPalette.semiBold14.copyWith(
              color: myColorScheme(context).subTitleColor,
              
            ),
          ),
        ),
        Expanded(
          flex: 2,
          child: Text(
            StringConstants.change.tr(),
            textAlign: TextAlign.center,
            style: FontPalette.semiBold14.copyWith(
              color: myColorScheme(context).subTitleColor,
            ),
          ),
        ),
      ],
    );
  }
}
