import 'package:flutter/material.dart';

import '../../../../core/theme/font_pallette.dart';

class SubHeading extends StatelessWidget {
  final IconData? icon;
  final String? label;
  final String? value;

  const SubHeading({
    super.key,
    this.icon,
    this.label,
    this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            // if (icon != null)
            //   Icon(
            //     icon,
            //     size: 12,
            //     color: myColorScheme(context).titleColor,
            //   ),
            // if (icon != null) const SizedBox(width: 4),
            if (label != null) Text(label!, style: FontPalette.medium12),
          ],
        ),
        if (value != null)
          Text(
            value!,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
      ],
    );
  }
}
