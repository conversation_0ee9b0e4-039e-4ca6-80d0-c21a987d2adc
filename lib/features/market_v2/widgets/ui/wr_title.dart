import 'package:flutter/material.dart';

import '../../../../core/theme/color_pallette.dart';
import '../../../../core/theme/font_pallette.dart';
import '../../../../core/theme/my_color_scheme.dart';

class WR_Title extends StatelessWidget {
  const WR_Title({
    super.key,
    required this.title,
    this.onTap,
    this.showArrow = false,
  });

  final String title;
  final VoidCallback? onTap;
  final bool showArrow;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: FontPalette.medium14.copyWith(
              color: myColorScheme(context).titleColor,
            ),
          ),
          if (showArrow)
            InkWell(
              onTap: onTap,
              child: Icon(
                Icons.arrow_forward_ios_rounded,
                size: 16,
                color: ColorPalette.greyColor4,
              ),
            ),
        ],
      ),
    );
  }
}
