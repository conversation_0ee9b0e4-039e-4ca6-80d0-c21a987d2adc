import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/constants/string_constants.dart';
import '../../../../core/routes/routes.dart';
import '../../../../core/theme/color_pallette.dart';
import '../../../../core/theme/font_pallette.dart';
import '../../../../core/theme/my_color_scheme.dart';

class Header extends StatelessWidget implements PreferredSizeWidget {
  const Header({
    super.key,
    this.showBackButton,
  });

  final bool? showBackButton;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: myColorScheme(context).cardColor,
      surfaceTintColor: Colors.transparent,
      elevation: 0,
      centerTitle: true,
      leading: showBackButton ?? false
          ? IconButton(
              icon: Icon(
                Icons.arrow_back_ios_outlined,
                color: ColorPalette.greyColor4,
              ),
              onPressed: () {
                Navigator.pop(context);
              },
            )
          : const SizedBox(),
      title: Text(
        StringConstants.stocks.tr(),
        style: FontPalette.semiBold16.copyWith(
          color: myColorScheme(context).titleColor,
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(
            Icons.search,
            color: ColorPalette.greyColor4,
          ),
          onPressed: () =>
              Navigator.pushNamed(context, routeMarketSearchScreen),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(60.h);
}
