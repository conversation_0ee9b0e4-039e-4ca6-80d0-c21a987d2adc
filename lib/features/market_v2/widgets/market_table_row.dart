import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/utils/utils.dart';
import 'package:sf_app_v2/features/market_v2/logic/market/market_cubit.dart';
import 'package:sf_app_v2/features/market_v2/kline_detail_screen.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';

import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';

class MarketTableRow extends StatelessWidget {
  const MarketTableRow({
    super.key,
    required this.data,
  });

  final StockItem data;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (ctx) => BlocProvider.value(
              value: context.read<MarketCubit>(),
              child: KlineDetailScreen(
                stock: data,
              ),
            ),
          ),
        );
      },
      child: Container(
        color: Colors.transparent,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Name and Symbol Column
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      data.name ?? 'N/A',
                      style: FontPalette.medium14.copyWith(
                        color: myColorScheme(context).subTitleColor,
                      ),
                    ),
                    // Text(
                    //   data.symbol ?? 'N/A',
                    //   style: TextStyle(
                    //     color: Colors.grey[600],
                    //     fontWeight: FontWeight.w500,
                    //     fontSize: 12,
                    //   ),
                    // ),
                  ],
                ),
              ),

              // Latest Price Column
              Expanded(
                flex: 2,
                child: Text(
                  (data.latestPrice?.toStringAsFixed(2) ?? '0').toCurrency(),
                  textAlign: TextAlign.center,
                  style: FontPalette.semiBold14.copyWith(
                    color: myColorScheme(context).subTitleColor,
                  ),
                ),
              ),

              // Change Percentage Column
              Expanded(
                flex: 2,
                child: Center(
                  child: SizedBox(
                    width: 60,
                    height: 27,
                    child: Container(
                      decoration: BoxDecoration(
                        color: (data.gain ?? 0.0).getValueColor(context),
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: Center(
                        child: Text(
                          "${(data.gain ?? 0) >= 0 ? '+' : ''}${(data.gain ?? 0).toStringAsFixed(2)}%",
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          
                          style: FontPalette.semiBold13.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
