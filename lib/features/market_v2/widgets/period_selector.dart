import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';
import 'package:sf_app_v2/features/market_v2/logic/market/market_cubit.dart';

import '../../../core/constants/string_constants.dart';
import '../../../core/theme/font_pallette.dart';

class PeriodSelector extends StatefulWidget {
  final bool showExtendedPeriods;
  final StockItem? stock;

  const PeriodSelector({
    super.key,
    required this.stock,
    this.showExtendedPeriods = true,
  });

  @override
  State<PeriodSelector> createState() => _PeriodSelectorState();
}

class _PeriodSelectorState extends State<PeriodSelector> {
  final ScrollController _scrollController = ScrollController();

  List<String> get _periods => widget.showExtendedPeriods
      ? [
          StringConstants.realtime,
          StringConstants.daily,
          StringConstants.week,
          StringConstants.month,
          StringConstants.oneMin,
          StringConstants.fiveMin,
          StringConstants.fifteenMin,
          StringConstants.thirtyMin,
        ]
      : [StringConstants.daily, StringConstants.week, StringConstants.month];

  // void _scrollLeft() {
  //   final double currentPosition = _scrollController.offset;
  //   _scrollController.animateTo(
  //     currentPosition - 100,
  //     duration: const Duration(milliseconds: 300),
  //     curve: Curves.easeOut,
  //   );
  // }

  // void _scrollRight() {
  //   final double currentPosition = _scrollController.offset;
  //   _scrollController.animateTo(
  //     currentPosition + 100,
  //     duration: const Duration(milliseconds: 300),
  //     curve: Curves.easeOut,
  //   );
  // }

  void _handlePeriodSelected(String period) {
    context
        .read<MarketCubit>()
        .changePeriodKline(stock: widget.stock, period: period.toLowerCase());
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // IconButton(
        //   onPressed: _scrollLeft,
        //   icon: const Icon(Icons.chevron_left, size: 20),
        // ),
        Expanded(
          child: BlocSelector<MarketCubit, MarketState, String>(
              selector: (state) => state.selectedPeriod,
              builder: (_, data) {
                return SingleChildScrollView(
                  controller: _scrollController,
                  // scrollDirection: Axis.horizontal,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Wrap(
                      spacing: 4,
                      runSpacing: 8,
                      children: _periods.map(
                        (period) {
                          return _buildPeriodChip(
                            period,
                            isSelected:
                                period.toLowerCase() == data.toLowerCase(),
                          );
                        },
                      ).toList(),
                    ),
                  ),
                );
              }),
        ),
        // IconButton(
        //   onPressed: _scrollRight,
        //   icon: const Icon(Icons.chevron_right, size: 20),
        // ),
      ],
    );
  }

  Widget _buildPeriodChip(String period, {required bool isSelected}) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 3),
      height: 27,
      width: 42,
      child: OutlinedButton(
        onPressed: () => _handlePeriodSelected(period),
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 5),
          side: BorderSide(
            color: isSelected
                ? myColorScheme(context).primaryColor!
                : myColorScheme(context).borderColor!,
            width: 1,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5),
          ),
        ),
        child: Text(
          period.tr(),
          style: FontPalette.medium12.copyWith(
            color: isSelected
                ? myColorScheme(context).primaryColor!
                : myColorScheme(context).titleColor!,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
