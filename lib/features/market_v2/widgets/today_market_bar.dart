import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

class TodayMarketBar extends StatelessWidget {
  const TodayMarketBar({
    super.key,
    required this.list,
    this.low,
    this.up,
    this.zero,
  });

  final List<int> list;
  final int? low;
  final int? up;
  final int? zero;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(5),
      child: Column(
        children: [
          SizedBox(
            height: height,
            child: Stack(
              children: [
                BarChart(
                  BarChartData(
                    alignment: BarChartAlignment.spaceAround,
                    barGroups: _createBarGroups(list),
                    titlesData: const FlTitlesData(show: false),
                    gridData: const FlGridData(show: false),
                    borderData: FlBorderData(show: false),
                    barTouchData: BarTouchData(enabled: false),
                  ),
                ),
                // Positioned.fill(
                //   child: LayoutBuilder(
                //     builder: (context, constraints) {
                //       final barGroups = _createBarGroups(list);
                //       final maxValue = barGroups
                //           .map((group) => group.barRods.first.toY)
                //           .reduce((a, b) => a > b ? a : b);
                //       return Stack(
                //         children: barGroups.asMap().entries.map((entry) {
                //           final index = entry.key;
                //           final group = entry.value;
                //           final barHeight =
                //               (group.barRods.first.toY / maxValue) * height;
                //           final value =
                //               (group.barRods.first.toY - variate).toInt();

                //           return Positioned(
                //             left: (constraints.maxWidth / barGroups.length) *
                //                     index +
                //                 (constraints.maxWidth / barGroups.length / 2) -
                //                 (barWidth / 2),
                //             bottom: barHeight - 10,
                //             child: Container(
                //               width: barWidth,
                //               color: myColorScheme(context).backgroundColor2,
                //               alignment: Alignment.center,
                //               child: Text(
                //                 '$value',
                //                 style: TextStyle(
                //                   fontSize: 10,
                //                   fontWeight: FontWeight.w500,
                //                   color: myColorScheme(context).titleColor,
                //                 ),
                //               ),
                //             ),
                //           );
                //         }).toList(),
                //       );
                //     },
                //   ),
                // ),
              ],
            ),
          ),
          // Bottom Labels
          Padding(
            padding: const EdgeInsets.fromLTRB(0, 4, 0, 0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: labels
                  .map(
                    (label) => SizedBox(
                      width: barWidth,
                      child: Text(
                        label,
                        textAlign: TextAlign.center,
                        style: FontPalette.normal10.copyWith(
                          color: myColorScheme(context).titleColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  )
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }
}

List<BarChartGroupData> _createBarGroups(List list) {
  if (list.isEmpty) {
    return [];
  }
  final colorsList = getColorsList();
  return List.generate(
    list.length,
    (index) => BarChartGroupData(
      x: index,
      barRods: [
        BarChartRodData(
          toY: list[index].toDouble() + variate,
          color: colorsList[index],
          width: barWidth,
          borderRadius: BorderRadius.circular(0),
        ),
      ],
      showingTooltipIndicators: [],
      barsSpace: 0,
    ),
  );
}

final labels = [
  '-10%',
  '-8%',
  '-6%',
  '-4%',
  '-2%',
  '0%',
  '2%',
  '4%',
  '6%',
  '8%',
  '10%',
];
final colors = [
  Colors.red.shade100,
  Colors.red.shade200,
  Colors.red.shade300,
  Colors.red.shade400,
  Colors.red.shade600,
  Colors.grey,
  const Color(0xFF16A34A),
  const Color(0xFF38B75F),
  const Color(0xFF5BCB74),
  const Color(0xFF7EDF89),
  const Color(0xFFA1F39E),
];

List<Color> getColorsList() => colors.reversed.toList();

const height = 100.0;
const variate = 300.0;
final barWidth = 25.w;
