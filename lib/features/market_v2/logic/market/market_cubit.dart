import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/utils/utils.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/component_stock_response.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/depth_quote_model.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/dist_response.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/plate_response.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/utils/log.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_kline_data.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';
import 'package:sf_app_v2/features/market_v2/domain/repositories/market_repository.dart';
import 'package:sf_app_v2/features/market_v2/utils/utils.dart';

import '../../domain/models/stock_table_response.dart';

part 'market_state.dart';

@injectable
class MarketCubit extends Cubit<MarketState> {
  MarketCubit(this.marketRepo) : super(const MarketState());

  final MarketRepository marketRepo;

  // final webSocketService = WebSocketService();

  // @override
  // Future<void> close() {
  //   webSocketService.disconnect();
  //   return super.close();
  // }

  void init() {
    fetchPlateList(1);
    fetchStockList();
    fetchTableData(sortType: 1, order: 'DESC');
    fetchDist();
    // fetchPlateList(2);
    // socketInit();
  }

  void pollMarket() {
    logDev('MARKET', 'polling', alien: true);
    fetchPlateList(1);
    fetchStockList();
    fetchDist();
    updateTableTab(state.selectedTableTab);
  }

  // void socketInit() {
  //   webSocketService.connect(
  //     onMessage: (message) {
  //       _processStockList(message);
  //       _processMarketTable(message);
  //       _processPlateList(message);
  //     },
  //     onError: (error) {},
  //     onDone: () {},
  //   );
  // }

  // void _processStockList(String message) {
  //   final newMsg = formatMessage(message);
  //   if (newMsg == null) return;

  //   final list0 = state.stockResponse?.data?.list ?? [];
  //   final list = List<StockItem>.from(list0);
  //   if (list.isEmpty) return;

  //   final updatedItem = updateStockItem(newMsg, list);
  //   if (updatedItem == null) return;

  //   final itemIndex = getStockItemIndex(updatedItem, newMsg, list);
  //   if (itemIndex == null) return;

  //   list[itemIndex] = updatedItem;

  //   final coreKlineList = List<StockKlineData>.from(state.coreKlineList ?? []);
  //   if (coreKlineList.isEmpty) return;

  //   final updatedData = state.stockResponse?.copyWith(data: StockData(list: list));
  //   final updatedCoreKlineList = updateCoreKlineListFromSocket(updatedItem, newMsg, coreKlineList);

  //   emit(
  //     state.copyWith(
  //       stockResponse: updatedData,
  //       coreKlineList: updatedCoreKlineList,
  //     ),
  //   );
  // }

  // void _processMarketTable(String message) {
  //   final newMsg = formatMessage(message);
  //   if (newMsg == null) return;

  //   final list = List<StockItem>.from(state.tableData?.data?.list ?? []);
  //   if (list.isEmpty) return;

  //   final updatedItem = updateStockItem(newMsg, list);
  //   if (updatedItem == null) return;

  //   final itemIndex = getStockItemIndex(updatedItem, newMsg, list);
  //   if (itemIndex == null) return;

  //   list[itemIndex] = updatedItem;

  //   final coreKlineList = List<StockKlineData>.from(state.coreKlineList ?? []);
  //   if (coreKlineList.isEmpty) return;

  //   final updatedData = state.tableData?.copyWith(
  //     data: state.tableData?.data?.copyWith(list: list),
  //   );
  //   final updatedCoreKlineList = updateCoreKlineListFromSocket(updatedItem, newMsg, coreKlineList);

  //   emit(
  //     state.copyWith(
  //       tableData: updatedData,
  //       coreKlineList: updatedCoreKlineList,
  //     ),
  //   );
  // }

  // void _processPlateList(String message) {
  //   final newMsg = formatMessage(message);
  //   if (newMsg == null) return;

  //   final list = List<StockItem>.from(state.plateResponseA?.data?.list ?? []);
  //   if (list.isEmpty) return;

  //   final updatedItem = updateStockItem(newMsg, list);
  //   if (updatedItem == null) return;

  //   final itemIndex = getStockItemIndex(updatedItem, newMsg, list);
  //   if (itemIndex == null) return;

  //   list[itemIndex] = updatedItem;

  //   final coreKlineList = List<StockKlineData>.from(state.coreKlineList ?? []);
  //   if (coreKlineList.isEmpty) return;

  //   final updatedData = state.plateResponseA?.copyWith(
  //     data: state.plateResponseA?.data?.copyWith(list: list),
  //   );
  //   final updatedCoreKlineList = updateCoreKlineListFromSocket(updatedItem, newMsg, coreKlineList);

  //   emit(
  //     state.copyWith(
  //       plateResponseA: updatedData,
  //       coreKlineList: updatedCoreKlineList,
  //     ),
  //   );
  // }

  void fetchPlateList(int type) async {
    if (type == 1) emit(state.copyWith(plateFetchStatusA: DataStatus.loading));
    if (type == 2) emit(state.copyWith(plateFetchStatusB: DataStatus.loading));

    try {
      final result = await marketRepo.fetchPlateList(type);

      if (type == 1) {
        emit(
          state.copyWith(
            plateFetchStatusA: DataStatus.success,
            plateResponseA: result.data,
          ),
        );
      }
      if (type == 2) {
        emit(
          state.copyWith(
            plateFetchStatusB: DataStatus.success,
            plateResponseB: result.data,
          ),
        );
      }

      // final stockList = result.data?.data?.list;

      // if (stockList != null) {
      //   for (final stock in stockList) {
      //     if (stock.market != null && stock.securityType != null && stock.symbol != null) {
      //       final socketId = getSocketId(stock);
      //       subscribeSymbol(socketId, webSocketService);
      //     }
      //   }
      // }
    } catch (e) {
      if (type == 1) emit(state.copyWith(plateFetchStatusA: DataStatus.failed));
      if (type == 2) emit(state.copyWith(plateFetchStatusB: DataStatus.failed));
      logDev(e, 'fetchPlateList', error: true);
    }
  }

  Future<DistResponse> fetchDist() async {
    emit(state.copyWith(distFetchStatus: DataStatus.loading));
    try {
      final result = await marketRepo.fetchDist();
      emit(
        state.copyWith(
          distFetchStatus: DataStatus.success,
          distResponse: result.data,
        ),
      );
      return result.data ?? const DistResponse();
    } catch (e) {
      emit(state.copyWith(distFetchStatus: DataStatus.failed));
      logDev(e, 'fetchDist', error: true);
      rethrow;
    }
  }

  Future<StockResponse> fetchStockList() async {
    emit(state.copyWith(stockFetchStatus: DataStatus.loading));
    try {
      final result = await marketRepo.fetchStockList();

      final stockList = result.data;
      if (stockList != null) {
        for (final stock in stockList) {
          if (stock.market != null && stock.securityType != null && stock.symbol != null) {
            fetchMiniKline(stock);
            // final socketId = getSocketId(stock);
            // subscribeSymbol(socketId, webSocketService);
          }
        }
      }

      emit(
        state.copyWith(
          stockFetchStatus: DataStatus.success,
          stockResponse: StockResponse(data: StockData(list: stockList)),
        ),
      );
      return StockResponse(data: StockData(list: stockList));
    } catch (e) {
      emit(state.copyWith(stockFetchStatus: DataStatus.failed));
      logDev(e, 'fetchStockList', error: true);
      rethrow;
    }
  }

  void fetchMiniKline(StockItem? stock) async {
    if (stock == null) return;
    emit(state.copyWith(klineFetchStatus: DataStatus.loading));
    try {
      final result = await marketRepo.fetchMiniKline(stock);
      final modifiedResult = result.data?.copyWith(
        data: StockKlineData(detail: stock, list: result.data?.data?.list),
      );
      final updatedList = updateKlineList(
        result: modifiedResult,
        coreKlineList: state.miniKlineList,
      );

      emit(
        state.copyWith(
          klineFetchStatus: DataStatus.success,
          miniKlineList: updatedList,
        ),
      );
    } catch (e) {
      emit(state.copyWith(klineFetchStatus: DataStatus.failed));
      logDev(e, 'fetchKlineData', error: true);
    }
  }

  void changePeriodKline({StockItem? stock, String? period}) {
    emit(state.copyWith(selectedPeriod: period));
    fetchKlineData(stock: stock);
  }

  void fetchKlineData({StockItem? stock}) async {
    if (stock == null) return;
    emit(state.copyWith(klineFetchStatus: DataStatus.loading));
    try {
      final result = await marketRepo.fetchKlineData(stock, state.selectedPeriod);
      final updatedList = updateKlineList(
        result: result.data,
        coreKlineList: state.coreKlineList,
      );

      logDev('KLINE ${getInstrumentId(stock)}', 'fetched', alien: true);

      emit(
        state.copyWith(
          klineFetchStatus: DataStatus.success,
          coreKlineList: updatedList,
        ),
      );
    } catch (e) {
      emit(state.copyWith(klineFetchStatus: DataStatus.failed));
      logDev(e, 'fetchKlineData', error: true);
    }
  }

  void fetchDepthQuote({StockItem? stock}) async {
    if (stock == null) return;
    emit(state.copyWith(depthQuoteFetchStatus: DataStatus.loading));
    try {
      final result = await marketRepo.fetchDepthQuote(stock);

      emit(
        state.copyWith(
          depthQuoteFetchStatus: DataStatus.success,
          depthQuoteResponse: result.data,
        ),
      );
    } catch (e) {
      emit(state.copyWith(depthQuoteFetchStatus: DataStatus.failed));
      logDev(e, 'fetchDepthQuote', error: true);
    }
  }

  void fetchTableData({
    required int sortType,
    required String order,
    int page = 1,
    bool isLoadMore = false,
  }) async {
    emit(state.copyWith(tableFetchStatus: DataStatus.loading));
    if (isLoadMore) emit(state.copyWith(isPaginating: true));
    try {
      final result = await marketRepo.fetchTableData(
        sortType: sortType,
        order: order,
        pageNum: page,
      );
      // final stockList = result.data?.data?.list;

      // if (stockList != null) {
      //   for (final stock in stockList) {
      //     if (stock.market != null && stock.securityType != null && stock.symbol != null) {
      //       final socketId = getSocketId(stock);
      //       subscribeSymbol(socketId, webSocketService);
      //     }
      //   }
      // }

      if (isLoadMore) {
        emit(
          state.copyWith(
            tableData: StockTableResponse(
              code: result.data?.code ?? 0,
              data: StockTableData(
                list: [
                  ...?state.tableData?.data?.list,
                  ...?result.data?.data?.list,
                ],
                pageNum: result.data?.data?.pageNum ?? 0,
                pageSize: result.data?.data?.pageSize ?? 0,
                total: result.data?.data?.total ?? 0,
              ),
              msg: result.data?.msg ?? '',
            ),
            tableFetchStatus: DataStatus.success,
            isPaginating: false,
          ),
        );
      } else {
        emit(
          state.copyWith(
            tableFetchStatus: DataStatus.success,
            tableData: result.data,
            isPaginating: false,
          ),
        );
      }
    } on Exception catch (e) {
      emit(state.copyWith(tableFetchStatus: DataStatus.failed));
      logDev(e, 'fetchTableData', error: true);
    }
  }

  void updateTableTab(int tab) {
    emit(state.copyWith(selectedTableTab: tab));

    switch (tab) {
      case 0:
        fetchTableData(sortType: 1, order: 'DESC');
        break;
      case 1:
        fetchTableData(sortType: 2, order: 'DESC');
        break;
      case 2:
        fetchTableData(sortType: 2, order: 'ASC');
        break;
    }
  }

  (int, String) getTableSortTypeAndOrder() => switch (state.selectedTableTab) {
        0 => (1, 'DESC'),
        1 => (2, 'DESC'),
        2 => (2, 'ASC'),
        _ => (1, 'DESC'),
      };
}
