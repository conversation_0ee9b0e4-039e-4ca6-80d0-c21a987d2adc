part of 'market_cubit.dart';

class MarketState extends Equatable {
  const MarketState({
    this.depthQuoteFetchStatus = DataStatus.idle,
    this.depthQuoteResponse,
    this.plateFetchStatusA = DataStatus.idle,
    this.plateResponseA,
    this.plateFetchStatusB = DataStatus.idle,
    this.plateResponseB,
    this.stockFetchStatus = DataStatus.idle,
    this.stockResponse,
    this.componentStockResponse,
    this.klineFetchStatus = DataStatus.idle,
    this.coreKlineList,
    this.miniKlineList,
    this.distFetchStatus = DataStatus.idle,
    this.distResponse,
    this.tableData,
    this.tableFetchStatus = DataStatus.idle,
    this.selectedTableTab = 0, // 0: Hot List, 1: Gainers, 2: Losers
    this.isPaginating = false,
    this.selectedPeriod = "realtime",
  });

  final DataStatus depthQuoteFetchStatus;
  final DepthQuoteModel? depthQuoteResponse;
  final DataStatus plateFetchStatusA;
  final PlateResponse? plateResponseA;
  final DataStatus plateFetchStatusB;
  final PlateResponse? plateResponseB;
  final DataStatus stockFetchStatus;
  final ComponentStockResponse? componentStockResponse;
  final StockResponse? stockResponse;
  final DataStatus klineFetchStatus;
  final List<StockKlineData>? coreKlineList;
  final List<StockKlineData>? miniKlineList;
  final DataStatus distFetchStatus;
  final DistResponse? distResponse;
  final StockTableResponse? tableData;
  final DataStatus tableFetchStatus;
  final int selectedTableTab;
  final bool isPaginating;
  final String selectedPeriod;
  @override
  List<Object?> get props => [
        depthQuoteFetchStatus,
        depthQuoteResponse,
        plateFetchStatusA,
        plateResponseA,
        plateFetchStatusB,
        plateResponseB,
        stockFetchStatus,
        stockResponse,
        klineFetchStatus,
        componentStockResponse,
        coreKlineList,
        miniKlineList,
        distFetchStatus,
        distResponse,
        tableData,
        tableFetchStatus,
        selectedTableTab,
        selectedPeriod,
        isPaginating,
      ];

  MarketState copyWith({
    DataStatus? depthQuoteFetchStatus,
    DepthQuoteModel? depthQuoteResponse,
    DataStatus? plateFetchStatusA,
    PlateResponse? plateResponseA,
    DataStatus? plateFetchStatusB,
    PlateResponse? plateResponseB,
    DataStatus? stockFetchStatus,
    StockResponse? stockResponse,
    ComponentStockResponse? componentStockResponse,
    DataStatus? klineFetchStatus,
    List<StockKlineData>? coreKlineList,
    List<StockKlineData>? miniKlineList,
    DataStatus? distFetchStatus,
    DistResponse? distResponse,
    StockTableResponse? tableData,
    DataStatus? tableFetchStatus,
    int? selectedTableTab,
    bool? isPaginating,
    String? selectedPeriod,
  }) {
    return MarketState(
      depthQuoteFetchStatus: depthQuoteFetchStatus ?? this.depthQuoteFetchStatus,
      depthQuoteResponse: depthQuoteResponse ?? this.depthQuoteResponse,
      plateFetchStatusA: plateFetchStatusA ?? this.plateFetchStatusA,
      plateResponseA: plateResponseA ?? this.plateResponseA,
      plateFetchStatusB: plateFetchStatusB ?? this.plateFetchStatusB,
      plateResponseB: plateResponseB ?? this.plateResponseB,
      stockFetchStatus: stockFetchStatus ?? this.stockFetchStatus,
      stockResponse: stockResponse ?? this.stockResponse,
      klineFetchStatus: klineFetchStatus ?? this.klineFetchStatus,
      coreKlineList: coreKlineList ?? this.coreKlineList,
      miniKlineList: miniKlineList ?? this.miniKlineList,
      distFetchStatus: distFetchStatus ?? this.distFetchStatus,
      distResponse: distResponse ?? this.distResponse,
      componentStockResponse: componentStockResponse ?? this.componentStockResponse,
      tableData: tableData ?? this.tableData,
      tableFetchStatus: tableFetchStatus ?? this.tableFetchStatus,
      selectedTableTab: selectedTableTab ?? this.selectedTableTab,
      isPaginating: isPaginating ?? this.isPaginating,
      selectedPeriod: selectedPeriod ?? this.selectedPeriod,
    );
  }
}
