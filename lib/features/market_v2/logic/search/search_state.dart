part of 'search_cubit.dart';

class SearchState extends Equatable {
  const SearchState({
    this.searchFetchStatus = DataStatus.idle,
    this.searchData,
    this.error,
  });

  final DataStatus searchFetchStatus;
  final SearchData? searchData;
  final String? error;

  @override
  List<Object?> get props => [
        searchFetchStatus,
        searchData,
        error,
      ];

  SearchState copyWith({
    DataStatus? searchFetchStatus,
    SearchData? searchData,
    String? error,
  }) {
    return SearchState(
      searchFetchStatus: searchFetchStatus ?? this.searchFetchStatus,
      searchData: searchData ?? this.searchData,
      error: error ?? this.error,
    );
  }
}
