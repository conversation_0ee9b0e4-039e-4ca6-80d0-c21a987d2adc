import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/utils/log.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/search/search_response.dart';

import '../../domain/repositories/market_repository.dart';

part 'search_state.dart';

@injectable
class SearchCubit extends Cubit<SearchState> {
  SearchCubit(this.marketRepo) : super(const SearchState());

  final MarketRepository marketRepo;

  void fetchSearchResults(String query) async {
    emit(state.copyWith(searchFetchStatus: DataStatus.loading));
    try {
      final result = await marketRepo.fetchSearchResults(query);
      emit(state.copyWith(
          searchFetchStatus: DataStatus.success,
          searchData: result.data?.data,),);
    } catch (e) {
      emit(state.copyWith(
          searchFetchStatus: DataStatus.failed, error: e.toString(),),);
      logDev(e, 'fetchSearchResults', error: true);
    }
  }
}
