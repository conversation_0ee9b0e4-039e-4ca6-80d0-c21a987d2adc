import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/component_stock_response.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/depth_quote_model.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/dist_response.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/plate_response.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_kline_data.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_table_response.dart';

import '../models/search/search_response.dart';

abstract class MarketRepository {
  const MarketRepository();

  Future<ResponseResult<PlateResponse>> fetchPlateList(int type);

  Future<ResponseResult<List<StockItem>>> fetchStockList();

  Future<ResponseResult<StockTableResponse>> fetchTableData({
    required int sortType,
    required String order,
    required int pageNum,
  });

  Future<ResponseResult<DistResponse>> fetchDist();

  Future<ResponseResult<StockKlineResponse>> fetchMiniKline(StockItem stock);

  Future<ResponseResult<StockKlineResponse>> fetchKlineData(StockItem stock, String period);

  Future<ResponseResult<ComponentStockResponse>> fetchComponentStock();

  Future<ResponseResult<SearchResponse>> fetchSearchResults(String query);

  Future<ResponseResult<DepthQuoteModel>> fetchDepthQuote(StockItem stock);
}
