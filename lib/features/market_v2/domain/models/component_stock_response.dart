import 'package:freezed_annotation/freezed_annotation.dart';

part 'component_stock_response.freezed.dart';
part 'component_stock_response.g.dart';

@freezed
class ComponentStockResponse with _$ComponentStockResponse {
  const factory ComponentStockResponse({
    int? code,
    String? msg,
    List<ComponentStockData>? data,
  }) = _ComponentStockResponse;

  factory ComponentStockResponse.fromJson(Map<String, dynamic> json) =>
      _$ComponentStockResponseFromJson(json);
}

@freezed
class ComponentStockData with _$ComponentStockData {
  const factory ComponentStockData({
    List<double>? kline,
    double? latestPrice,
    double? priceChange,
    double? priceChangePercent,
    String? symbol,
  }) = _ComponentStockData;

  factory ComponentStockData.fromJson(Map<String, dynamic> json) =>
      _$ComponentStockDataFromJson(json);
}
