import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';

part 'stock_kline_data.freezed.dart';
part 'stock_kline_data.g.dart';

@freezed
class StockKlineResponse with _$StockKlineResponse {
  const factory StockKlineResponse({
    int? code,
    StockKlineData? data,
    String? msg,
  }) = _StockKlineResponse;

  factory StockKlineResponse.fromJson(Map<String, dynamic> json) =>
      _$StockKlineResponseFromJson(json);
}

@freezed
class StockKlineData with _$StockKlineData {
  const factory StockKlineData({
    StockItem? detail,
    List<KlineItem>? list,
  }) = _StockKlineData;

  factory StockKlineData.fromJson(Map<String, dynamic> json) =>
      _$StockKlineDataFromJson(json);
}

@freezed
class KlineItem with _$KlineItem {
  const factory KlineItem({
    double? volume,
    double? price,
    double? avgPrice,
    int? time,
    double? open,
    double? close,
    double? high,
    double? low,
    int? apiEndTime,
  }) = _KlineItem;

  factory KlineItem.fromJson(Map<String, dynamic> json) =>
      _$KlineItemFromJson(json);
}
