import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';

part 'plate_response.freezed.dart';
part 'plate_response.g.dart';

@freezed
class PlateResponse with _$PlateResponse {
  const factory PlateResponse({
    int? code,
    PlateData? data,
    String? msg,
  }) = _PlateResponse;

  factory PlateResponse.fromJson(Map<String, dynamic> json) => _$PlateResponseFromJson(json);
}

@freezed
class PlateData with _$PlateData {
  const factory PlateData({
    @JsonKey(fromJson: _mapPlateItemToStockItem) List<StockItem>? list,
  }) = _PlateData;

  factory PlateData.fromJson(Map<String, dynamic> json) => _$PlateDataFromJson(json);
}

List<StockItem>? _mapPlateItemToStockItem(dynamic json) {
  if (json == null) return null;
  final list = json as List<dynamic>;
  return list
      .map((item) {
        final plateItem = item as Map<String, dynamic>;
        final leadUp = plateItem['leadUp'] as Map<String, dynamic>?;
        if (leadUp == null) return null;
        return StockItem.fromJson({
          ...leadUp,
          'name': plateItem['name'],
          'key': plateItem['key'],
          'gain': plateItem['gain'],
          'leadupGain': leadUp['gain'],
        });
      })
      .whereType<StockItem>()
      .toList();
}
