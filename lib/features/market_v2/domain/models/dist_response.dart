import 'package:freezed_annotation/freezed_annotation.dart';

part 'dist_response.freezed.dart';
part 'dist_response.g.dart';

@freezed
class DistResponse with _$DistResponse {
  const factory DistResponse({
    int? code,
    String? msg,
    DistData? data,
  }) = _DistResponse;

  factory DistResponse.fromJson(Map<String, dynamic> json) =>
      _$DistResponseFromJson(json);
}

@freezed
class DistData with _$DistData {
  const factory DistData({
    List<int>? list,
    int? low,
    int? up,
    int? zero,
  }) = _DistData;

  factory DistData.fromJson(Map<String, dynamic> json) =>
      _$DistDataFromJson(json);
}
