import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';

part 'stock_table_response.freezed.dart';
part 'stock_table_response.g.dart';

@freezed
class StockTableResponse with _$StockTableResponse {
  const factory StockTableResponse({
    int? code,
    StockTableData? data,
    String? msg,
  }) = _StockTableResponse;

  factory StockTableResponse.fromJson(Map<String, dynamic> json) =>
      _$StockTableResponseFromJson(json);
}

@freezed
class StockTableData with _$StockTableData {
  const factory StockTableData({
    List<StockItem>? list,
    int? pageNum,
    int? pageSize,
    int? total,
  }) = _StockTableData;

  factory StockTableData.fromJson(Map<String, dynamic> json) =>
      _$StockTableDataFromJson(json);
}
