import 'package:freezed_annotation/freezed_annotation.dart';

part 'depth_quote_model.freezed.dart';
part 'depth_quote_model.g.dart';

@freezed
class DepthQuoteModel with _$DepthQuoteModel {
  const factory DepthQuoteModel({
    int? code,
    String? msg,
    DepthQuoteData? data,
  }) = _DepthQuoteModel;

  factory DepthQuoteModel.fromJson(Map<String, dynamic> json) => _$DepthQuoteModelFromJson(json);
}

@freezed
class DepthQuoteData with _$DepthQuoteData {
  const factory DepthQuoteData({
    List<Bid>? bid,
    List<Ask>? ask,
    String? symbol,
    String? market,
    String? securityType,
    double? close,
    double? latestPrice,
  }) = _DepthQuoteData;

  factory DepthQuoteData.fromJson(Map<String, dynamic> json) => _$DepthQuoteDataFromJson(json);
}

@freezed
class Bid with _$Bid {
  const factory Bid({
    double? price,
    int? vol,
    int? depthNo,
    int? no,
  }) = _Bid;

  factory Bid.fromJson(Map<String, dynamic> json) => _$BidFromJson(json);
}

@freezed
class Ask with _$Ask {
  const factory Ask({
    double? price,
    int? vol,
    int? depthNo,
    int? no,
  }) = _Ask;

  factory Ask.fromJson(Map<String, dynamic> json) => _$AskFromJson(json);
}
