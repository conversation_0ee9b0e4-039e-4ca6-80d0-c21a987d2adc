import 'package:freezed_annotation/freezed_annotation.dart';

part 'stock_response.freezed.dart';
part 'stock_response.g.dart';

@freezed
class StockResponse with _$StockResponse {
  const factory StockResponse({
    int? code,
    String? msg,
    StockData? data,
  }) = _StockResponse;

  factory StockResponse.fromJson(Map<String, dynamic> json) =>
      _$StockResponseFromJson(json);
}

@freezed
class StockData with _$StockData {
  const factory StockData({
    List<StockItem>? list,
  }) = _StockData;

  factory StockData.fromJson(Map<String, dynamic> json) =>
      _$StockDataFromJson(json);
}

@freezed
class StockItem with _$StockItem {
  const factory StockItem({
    @JsonKey(fromJson: _parseStringToDouble) double? peStatic,
    @JsonKey(fromJson: _parseStringToDouble) double? high52w,
    @JsonKey(fromJson: _parseStringToDouble) double? peLyr,
    @JsonKey(fromJson: _parseStringToInt) int? precision,
    @JsonKey(fromJson: _parseStringToInt) int? securityStatus,
    @JsonKey(fromJson: _parseStringToDouble) double? gain,
    @JsonKey(fromJson: _parseStringToDouble) double? high,
    @JsonKey(fromJson: _parseStringToDouble) double? low,
    @JsonKey(fromJson: _parseStringToDouble) double? close,
    @JsonKey(fromJson: _parseStringToDouble) double? turnover,
    @JsonKey(fromJson: _parseStringToDouble) double? latestPrice,
    @JsonKey(fromJson: _parseStringToDouble) double? amount,
    @JsonKey(fromJson: _parseStringToDouble) double? chg,
    @JsonKey(fromJson: _parseStringToInt) int? lotSize,
    @JsonKey(fromJson: _parseStringToDouble) double? marketValue,
    @JsonKey(fromJson: _parseStringToDouble) double? dividendRate,
    @JsonKey(fromJson: _parseStringToDouble) double? priceUpLimited,
    @JsonKey(fromJson: _parseStringToDouble) double? priceDownLimited,
    @JsonKey(fromJson: _parseStringToDouble) double? volume,
    @JsonKey(fromJson: _parseStringToDouble) double? pb,
    @JsonKey(fromJson: _parseStringToDouble) double? leadupGain,
    String? symbol,
    String? industryPlate,
    String? currency,
    String? market,
    String? securityType,
    String? name,
    @JsonKey(fromJson: _parseStringToInt) int? latestTime,
    @JsonKey(fromJson: _parseStringToDouble) double? open,
  }) = _StockItem;

  factory StockItem.fromJson(Map<String, dynamic> json) =>
      _$StockItemFromJson(json);
}

double? _parseStringToDouble(dynamic value) {
  if (value == null) return null;
  return double.tryParse(value.toString());
}

int? _parseStringToInt(dynamic value) {
  if (value == null) return null;
  return int.tryParse(value.toString());
}
