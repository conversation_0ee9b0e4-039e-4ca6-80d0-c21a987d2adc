import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/api/endpoint/api_endpoints.dart';
import 'package:sf_app_v2/core/api/network/network.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/depth_quote_model.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/search/search_response.dart';
import 'package:sf_app_v2/features/market_v2/domain/repositories/market_repository.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/component_stock_response.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/dist_response.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/plate_response.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_kline_data.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_table_response.dart';

import '../../../../core/utils/utils.dart';

/// Service class that implements the MarketRepository interface to handle market-related API calls
@Injectable(as: MarketRepository)
class MarketService implements MarketRepository {
  /// Fetches list of market plates by type
  /// [type] - Type of plates to fetch
  /// Returns a [ResponseResult] containing [PlateResponse] on success
  @override
  Future<ResponseResult<PlateResponse>> fetchPlateList(int type) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getPlateList}?type=$type&pageSize=10',
         isSigninRequired: true,
        force: false,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: PlateResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch plate list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches list of major US stock indices (Dow Jones, NASDAQ, S&P 500)
  /// Returns a [ResponseResult] containing List of [StockItem] on success
  @override
  Future<ResponseResult<List<StockItem>>> fetchStockList() async {
    try {
      final List<Future<Response>> requests = [];
      
      for (final symbol in ['US|2|.DJI', 'US|2|.IXIC', 'US|2|.INX']) {
        requests.add(
          NetworkProvider().get(
            '${ApiEndpoints.getStockList}?instrument=$symbol',
            options: Options(
              headers: {'auth': true},
            ),
            force: false,
          )
        );
      }

      final responses = await Future.wait(requests);
      final List<Map<String, dynamic>> combinedData = [];

      for (final response in responses) {
        if (response.statusCode == 200 || response.statusCode == 201) {
          combinedData.add(response.data);
        }
      }

      if (combinedData.isNotEmpty) {
        final stockList = <StockItem>[];
        for (final data in combinedData) {
          if (data['data'] != null) {
            final stockData = StockItem.fromJson(data['data']);
            stockList.add(stockData);
          }
        }
        return ResponseResult(data: stockList);
      } else {
        return ResponseResult(error: 'Failed to fetch stock list');
      }

    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches paginated table data of stocks with sorting
  /// [sortType] - Type of sorting to apply
  /// [order] - Sort order (asc/desc)
  /// [pageNum] - Page number for pagination
  /// Returns a [ResponseResult] containing [StockTableResponse] on success
  @override
  Future<ResponseResult<StockTableResponse>> fetchTableData({
    required int sortType,
    required String order,
    required int pageNum,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getStockListV2}?sortType=$sortType&order=$order&pageNum=$pageNum&pageSize=20',
         isSigninRequired: true,
        force: false,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
            data: StockTableResponse.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch table data');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches gain distribution data
  /// Returns a [ResponseResult] containing [DistResponse] on success
  @override
  Future<ResponseResult<DistResponse>> fetchDist() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getGainDistribution,
         isSigninRequired: true,
        force: false,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: DistResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch distribution');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches mini kline data for a stock
  /// [stock] - Stock to fetch data for
  /// Returns a [ResponseResult] containing [StockKlineResponse] on success
  @override
  Future<ResponseResult<StockKlineResponse>> fetchMiniKline(
    StockItem stock,
  ) async {
    try {
      final instrument = getInstrumentId(stock);
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.timeLineMini}?instrument=$instrument&period=day',
         isSigninRequired: true,
        force: false,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
          return ResponseResult(
            data: StockKlineResponse.fromJson(response.data),
          );
      } else {
        return ResponseResult(error: 'Failed to fetch mini kline');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches kline data for a stock with specified period
  /// [stock] - Stock to fetch data for
  /// [period] - Time period for kline data
  /// Returns a [ResponseResult] containing [StockKlineResponse] on success
  @override
  Future<ResponseResult<StockKlineResponse>> fetchKlineData(
    StockItem stock,
    String period,
  ) async {
    try {
      final instrument = getInstrumentId(stock);
      final newPeriod = period == "realtime" ? "month" : period;
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getTimeline}?period=$newPeriod&instrument=$instrument&right=0',
         isSigninRequired: true,
        force: false,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final result = StockKlineResponse.fromJson(response.data);
        return ResponseResult(
          data: result,
        );
      } else {
        return ResponseResult(error: 'Failed to fetch kline data');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches component stock data
  /// Returns a [ResponseResult] containing [ComponentStockResponse] on success
  @override
  Future<ResponseResult<ComponentStockResponse>> fetchComponentStock() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getComponentStock,
         isSigninRequired: true,
        force: false,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
            data: ComponentStockResponse.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch component stock');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Searches for stocks matching query
  /// [query] - Search query string
  /// Returns a [ResponseResult] containing [SearchResponse] on success
  @override
  Future<ResponseResult<SearchResponse>> fetchSearchResults(
    String query,
  ) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getSearch}?pageNum=1&pageSize=20&keyword=$query',
         isSigninRequired: true,
        force: false,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: SearchResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch search results');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches depth quote data for a stock
  /// [stock] - Stock to fetch depth quote for
  /// Returns a [ResponseResult] containing [DepthQuoteModel] on success
  @override
  Future<ResponseResult<DepthQuoteModel>> fetchDepthQuote(
    StockItem stock,
  ) async {
    try {
      final instrument = getInstrumentId(stock);
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.depthQuote}?instrument=$instrument&depth=10',
         isSigninRequired: true,
        force: false,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {

          return ResponseResult(
            data: DepthQuoteModel.fromJson(response.data),
          );
        
      } else {
        return ResponseResult(error: 'Failed to depth quote');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
