import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:sf_app_v2/features/market_v2/logic/market/market_cubit.dart';
import 'package:sf_app_v2/features/market_v2/widgets/general_section.dart';
import 'package:sf_app_v2/features/market_v2/widgets/market_data_table.dart';
import 'package:sf_app_v2/features/market_v2/widgets/today_section.dart';
import 'package:sf_app_v2/features/market_v2/widgets/ui/header.dart';
import 'package:sf_app_v2/features/market_v2/widgets/ui/wr_market.dart';
import 'package:sf_app_v2/features/market_v2/widgets/ui/wr_title.dart';
import 'package:sf_app_v2/features/market_v2/widgets/visual_graph_section.dart';

import '../../core/constants/enums.dart';
import '../../core/constants/string_constants.dart';
import '../../core/utils/mixin/animation.dart';
import '../../core/widgets/pagination_widget.dart';
import 'domain/models/dist_response.dart';
import 'widgets/today_market_status.dart';

class MarketSectionScreen extends StatefulWidget {
  final bool showBackButton;
  final ScrollController? scrollController;
  const MarketSectionScreen({
    super.key,
    this.showBackButton = false,
    this.scrollController,
  });

  @override
  State<MarketSectionScreen> createState() => _MarketSectionScreenState();
}

class _MarketSectionScreenState extends State<MarketSectionScreen>
    with StaggeredAnimation {
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    context.read<MarketCubit>().init();

    _timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted) {
        context.read<MarketCubit>().pollMarket();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: Header(showBackButton: widget.showBackButton),
      body: SafeArea(
        child: BlocBuilder<MarketCubit, MarketState>(
          builder: (context, state) {
            return PaginationWidget(
              isPaginating: state.tableFetchStatus == DataStatus.loading &&
                  state.isPaginating,
              next: (state.tableData?.data?.list?.length ?? 0) <=
                  (state.tableData?.data?.total ?? 0),
              onPagination: (notification) {
                if (state.tableData?.data?.list?.length ==
                    (state.tableData?.data?.total ?? 0)) {
                  return false;
                }
                final (sortType, order) =
                    context.read<MarketCubit>().getTableSortTypeAndOrder();
                context.read<MarketCubit>().fetchTableData(
                      page: (state.tableData?.data?.pageNum ?? 0) + 1,
                      isLoadMore: true,
                      sortType: sortType,
                      order: order,
                    );
                return true;
              },
              child: SingleChildScrollView(
                controller: widget.scrollController,
                child: Padding(
                  padding: const EdgeInsets.only(left: 13, right: 13, top: 8),
                  child: AnimationLimiter(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: staggeredAnimation(
                        children: [
                          const WR_Market(
                            child: VisualGraphSection(),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              WR_Title(
                                title: StringConstants.todaysStockMarket.tr(),
                              ),
                              Builder(builder: (context) {
                                DistResponse? distResponse = state.distResponse;
                                return TodayMarketStatus(
                                  upCount: distResponse?.data?.up ?? 0,
                                  flatCount: distResponse?.data?.zero ?? 0,
                                  downCount: distResponse?.data?.low ?? 0,
                                );
                              }),
                            ],
                          ),
                          const WR_Market(
                            child: TodaySection(),
                          ),
                          WR_Title(title: StringConstants.leadingConcept.tr()),
                          const WR_Market(
                            child: MarketGeneralSection(type: 1),
                          ),
                          // WR_Title(title: StringConstants.leadingIndustry.tr()),
                          // const WR_Market(
                          //   child: MarketGeneralSection(type: 2),
                          // ),
                          // WR_Title(title: StringConstants.hotStocks.tr()),
                          const SizedBox(height: 8),
                          const WR_Market(
                            child: MarketDataTable(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
