import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/utils/utils.dart';
import 'package:sf_app_v2/core/widgets/shared_error.dart';
import 'package:sf_app_v2/core/widgets/shared_loading.dart';
import 'package:sf_app_v2/features/market_v2/logic/market/market_cubit.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_kline_data.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';
import 'package:sf_app_v2/features/market_v2/utils/utils.dart';
import 'package:sf_app_v2/features/market_v2/widgets/buy_sell_card.dart';
import 'package:sf_app_v2/features/market_v2/widgets/kline_detail.dart';
import 'package:sf_app_v2/features/market_v2/widgets/kline_list.dart';
import 'package:sf_app_v2/features/market_v2/widgets/period_selector.dart';

import '../../core/theme/font_pallette.dart';
import '../../core/theme/my_color_scheme.dart';

class KlineDetailScreen extends StatefulWidget {
  const KlineDetailScreen(
      {super.key, required this.stock, this.disableQuotes = false});

  final StockItem? stock;
  final bool disableQuotes;

  @override
  State<KlineDetailScreen> createState() => _KlineDetailScreenState();
}

class _KlineDetailScreenState extends State<KlineDetailScreen> {
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _init();
    _timer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (mounted) {
        _init();
      }
    });
  }

  _init() {
    context.read<MarketCubit>().fetchKlineData(stock: widget.stock);
    context.read<MarketCubit>().fetchMiniKline(widget.stock);
    context.read<MarketCubit>().fetchDepthQuote(stock: widget.stock);
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final instrument = getInstrumentId(widget.stock);
    if (instrument == null) return const SharedError();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          StringConstants.stocks.tr(),
          style: FontPalette.semiBold16.copyWith(
            color: myColorScheme(context).titleColor,
          ),
        ),
        backgroundColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios_new_rounded),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BlocSelector<MarketCubit, MarketState, StockKlineData?>(
              selector: (state) => checkAndGetKlineItem(
                instrument: instrument,
                coreKlineList: state.coreKlineList,
              ),
              builder: (context, klineData) {
                final detail = klineData?.detail;
                final list = klineData?.list;

                if (detail == null && list == null) {
                  return Padding(
                    padding: EdgeInsets.only(top: 0.2.sh),
                    child: const SharedLoading(),
                  );
                }

                return Column(
                  children: [
                    if (detail != null)
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 16, 16, 4),
                        child: KlineDetail(detail: detail),
                      ),
                    PeriodSelector(
                      stock: widget.stock,
                    ),
                    Padding(
                      padding: EdgeInsets.only(
                        bottom: 30.h,
                        left: 16.w,
                        right: 16.w,
                      ),
                      child: BlocSelector<MarketCubit, MarketState,
                              (List<StockKlineData>?, String)>(
                          selector: (state) =>
                              (state.miniKlineList, state.selectedPeriod),
                          builder: (context, state) {
                            if (state.$2 == "realtime") {
                              final existingItem =
                                  state.$1?.firstWhereOrNull((e) {
                                final id = getInstrumentId(e.detail);
                                if (id == null) return false;
                                return instrument == id;
                              });
                              final dataList = existingItem?.list;
                              return KlineList(
                                  list: dataList, selectedPeriod: state.$2);
                            } else {
                              return KlineList(
                                  list: list, selectedPeriod: state.$2);
                            }
                          }),
                    ),
                    if (widget.disableQuotes != true)
                      const Padding(
                        padding: EdgeInsets.fromLTRB(20, 0, 20, 10),
                        child: BuySellCard(),
                      ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
