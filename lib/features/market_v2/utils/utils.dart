import 'dart:convert';
import 'dart:math';

import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/services/ws.dart';
import 'package:sf_app_v2/core/utils/log.dart';
import 'package:sf_app_v2/core/utils/utils.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_kline_data.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';

StockItem? updateStockItem(List parts, List list) {
  final market = parts[0];
  final symbol = parts[3];

  final itemIndex =
      list.indexWhere((item) => item.market == market && item.symbol == symbol);
  if (itemIndex == -1) return null;

  final latestPrice = double.tryParse(parts[7]);
  final high = double.tryParse(parts[5]);
  final low = double.tryParse(parts[6]);
  final open = double.tryParse(parts[4]);
  final close = double.tryParse(parts[7]);

  final stockList = List<StockItem>.from(list);
  final existingItem = stockList.firstWhereOrNull(
    (item) => item.market == market && item.symbol == symbol,
  );
  if (existingItem == null) return null;

  final updatedItem = existingItem.copyWith(
    latestPrice: latestPrice ?? existingItem.latestPrice,
    high: high ?? existingItem.high,
    low: low ?? existingItem.low,
    open: open ?? existingItem.open,
    close: close ?? existingItem.close,
  );

  final isChanged = updatedItem.latestPrice != existingItem.latestPrice ||
      updatedItem.high != existingItem.high ||
      updatedItem.low != existingItem.low ||
      updatedItem.open != existingItem.open ||
      updatedItem.close != existingItem.close;
  if (!isChanged) return null;

  return updatedItem;
}

int? getStockItemIndex(StockItem item, List parts, List<StockItem> list) {
  final itemIndex = list
      .indexWhere((item) => item.market == parts[0] && item.symbol == parts[3]);
  if (itemIndex == -1) return null;
  return itemIndex;
}

List<StockItem>? updateStockList(
  int index,
  StockItem item,
  List<StockItem> list,
) {
  final stockList = List<StockItem>.from(list);
  stockList[index] = item;
  return stockList;
}

List? formatMessage(String data) {
  final jsonData = jsonDecode(data);
  if (jsonData['data'] == null) return null;

  List parts = jsonData['data'].split('|');

  if (parts.length < 11) return null;
  return parts;
}

List<StockKlineData>? updateCoreKlineListFromSocket(
  StockItem item,
  List parts,
  List<StockKlineData> coreKlineList,
) {
  try {
    final instrument = getInstrumentId(item);
    final timestamp = int.tryParse(parts[2]);

    if (timestamp == null || item.latestPrice == null || instrument == null) {
      return null;
    }

    final existingIndex = coreKlineList.indexWhere((e) {
      final id = getInstrumentId(e.detail);
      if (id == null) return false;
      return instrument == id;
    });
    if (existingIndex == -1) return null;

    final list = List<KlineItem>.from(coreKlineList[existingIndex].list ?? []);
    if (list.isEmpty) return null;

    // final lastApiEndTime = list.last.apiEndTime;
    // final dif = timestamp - (lastApiEndTime ?? 0);

    // List<KlineItem> newList = [];

    // if (dif < 60) {
    //   logDev(dif, 'OLD >>>', error: true);
    //   list[list.length - 1] = KlineItem(
    //     time: timestamp,
    //     price: item.latestPrice,
    //     close: item.close ?? 0,
    //     open: item.open ?? 0,
    //     high: item.high ?? 0,
    //     low: item.low ?? 0,
    //     volume: item.volume ?? 0,
    //     apiEndTime: lastApiEndTime,
    //   );
    // } else {
    //   logDev(dif, 'NEW >>>', error: true);
    //   list.add(
    //     KlineItem(
    //       time: timestamp,
    //       price: item.latestPrice,
    //       close: item.close ?? 0,
    //       open: item.open ?? 0,
    //       high: item.high ?? 0,
    //       low: item.low ?? 0,
    //       volume: item.volume ?? 0,
    //       apiEndTime: timestamp,
    //     ),
    //   );
    // }

    final updatedList = list
      ..add(
        KlineItem(
          time: timestamp,
          price: item.latestPrice,
          close: item.close ?? 0,
          open: item.open ?? 0,
          high: item.high ?? 0,
          low: item.low ?? 0,
          volume: item.volume ?? 0,
        ),
      );

    final updatedCoreKlineList = coreKlineList.map((e) {
      final id = getInstrumentId(e.detail);
      if (id == instrument) {
        return e.copyWith(list: updatedList, detail: item);
      }
      return e;
    }).toList();
    return updatedCoreKlineList;
  } catch (e) {
    logDev(e, 'updateCoreKlineList', error: true);
    return null;
  }
}

void subscribeSymbol(String? symbol, WebSocketService webSocketService) {
  if (symbol != "US|1|Q|GOOGL|R") return;
  if (symbol == null) return;
  final subscription = {"action": "SUBSCRIBE", "symbols": "US|1|Q|GOOGL|R"};
  webSocketService.sendMessage("subscribe", subscription);
}

List<String> getSocketSymbols(List<String?>? data) {
  return data?.where((e) => e != null).map((e) => "US|2|Q|${e!}|R").toList() ??
      [];
}

String testSocketMessage(String market, String symbol) {
  final now = DateTime.now().millisecondsSinceEpoch;
  final random = Random();

  final latestPrice = (random.nextDouble() * 10 + 3300).toStringAsFixed(2);
  final high =
      (double.parse(latestPrice) + random.nextDouble() * 2).toStringAsFixed(2);
  final low =
      (double.parse(latestPrice) - random.nextDouble() * 2).toStringAsFixed(2);
  final open = (random.nextDouble() * 1000 + 2000).toStringAsFixed(2);
  final close = (random.nextDouble() * 1000 + 2000).toStringAsFixed(2);
  final volume = random.nextInt(1000000000);
  final amount = random.nextInt(1000000000);

  return '{"action":"Q","data":"$market|2|$now|$symbol|$latestPrice|$high|$low|$open|$close|$volume|$amount"}';
}

List<StockKlineData>? updateKlineList({
  StockKlineResponse? result,
  List<StockKlineData>? coreKlineList,
}) {
  final modifiedList = result?.data?.list?.map((item) {
    return item.copyWith(apiEndTime: item.time);
  }).toList();

  if (modifiedList == null) {
    return null;
  }

  final stockKlineData = result?.data?.copyWith(list: modifiedList);
  if (stockKlineData == null) {
    return null;
  }
  final instrument = getInstrumentId(stockKlineData.detail);
  final list = List<StockKlineData>.from(coreKlineList ?? []);
  final existingIndex = list.indexWhere((e) {
    final id = getInstrumentId(e.detail);
    if (id == null) return false;
    return instrument == id;
  });
  if (existingIndex != -1) {
    list[existingIndex] = stockKlineData;
  } else {
    list.add(stockKlineData);
  }
  return list;
}

StockKlineData? checkAndGetKlineItem({
  List<StockKlineData>? coreKlineList,
  String? instrument,
}) {
  final existingItem = coreKlineList?.firstWhereOrNull((e) {
    final id = getInstrumentId(e.detail);
    if (id == null) return false;
    return instrument == id;
  });
  return existingItem;
}
