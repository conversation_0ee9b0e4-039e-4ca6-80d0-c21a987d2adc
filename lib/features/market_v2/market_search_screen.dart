import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/market_v2/logic/search/search_cubit.dart';
import 'package:sf_app_v2/features/market_v2/kline_detail_screen.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/search/search_response.dart';
import 'package:sf_app_v2/features/market_v2/domain/models/stock_response.dart';
import '../../core/constants/string_constants.dart';
import '../../core/theme/color_pallette.dart';
import '../../core/theme/font_pallette.dart';
import '../../core/theme/my_color_scheme.dart';
import 'logic/market/market_cubit.dart';

class MarketSearchScreen extends StatefulWidget {
  const MarketSearchScreen({super.key});

  @override
  State<MarketSearchScreen> createState() => _MarketSearchScreenState();
}

class _MarketSearchScreenState extends State<MarketSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce;

  @override
  void dispose() {
    _searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      if (query.isNotEmpty) {
        context.read<SearchCubit>().fetchSearchResults(query);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: myColorScheme(context).cardColor,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_outlined,
            color: ColorPalette.greyColor4,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: TextField(
          controller: _searchController,
          autofocus: true,
          onChanged: _onSearchChanged,
          style: FontPalette.normal16,
          decoration: InputDecoration(
            hintText: StringConstants.search.tr(),
            hintStyle: FontPalette.normal16.copyWith(
              color: ColorPalette.greyColor3,
            ),
            border: InputBorder.none,
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: Icon(
                      Icons.close,
                      color: ColorPalette.greyColor4,
                    ),
                    onPressed: () => _searchController.clear(),
                  )
                : null,
          ),
        ),
      ),
      body: BlocBuilder<SearchCubit, SearchState>(
        builder: (context, state) {
          if (state.searchFetchStatus == DataStatus.loading) {
            return const Center(child: CircularProgressIndicator.adaptive());
          }

          final searchResults = state.searchData?.list;
          if (searchResults == null || searchResults.isEmpty) {
            return Center(
              child: Text(
                StringConstants.noResultsFound.tr(),
                style: FontPalette.normal14.copyWith(
                  color: ColorPalette.greyColor4,
                ),
              ),
            );
          }

          return ListView.builder(
            itemCount: searchResults.length,
            itemBuilder: (context, index) {
              final item = searchResults[index];
              return _SearchResultTile(item: item);
            },
          );
        },
      ),
    );
  }
}

class _SearchResultTile extends StatelessWidget {
  final SearchItem item;

  const _SearchResultTile({required this.item});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (ctx) => BlocProvider.value(
              value: context.read<MarketCubit>(),
              child: KlineDetailScreen(
                stock: StockItem(
                  market: item.market,
                  securityType: item.securityType,
                  symbol: item.symbol,
                ),
              ),
            ),
          ),
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 20.w,
          vertical: 12.h,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              item.name ?? '',
              style: FontPalette.semiBold14,
            ),
            4.verticalSpace,
            Row(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 8.w,
                    vertical: 2.h,
                  ),
                  decoration: BoxDecoration(
                    color: ColorPalette.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    'US',
                    style: FontPalette.normal12.copyWith(
                      color: myColorScheme(context).primaryColor,
                    ),
                  ),
                ),
                8.horizontalSpace,
                Text(
                  item.symbol ?? '',
                  style: FontPalette.normal12.copyWith(
                    color: ColorPalette.greyColor4,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
