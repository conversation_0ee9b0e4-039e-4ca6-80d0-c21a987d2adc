import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/widgets/modern_app_bar.dart';
import 'package:sf_app_v2/core/widgets/modern_button.dart';
import 'package:sf_app_v2/core/widgets/modern_card.dart';
import 'package:sf_app_v2/core/widgets/modern_text_field.dart';

class ModernDesignDemoScreen extends StatefulWidget {
  const ModernDesignDemoScreen({super.key});

  @override
  State<ModernDesignDemoScreen> createState() => _ModernDesignDemoScreenState();
}

class _ModernDesignDemoScreenState extends State<ModernDesignDemoScreen> {
  final TextEditingController _textController = TextEditingController();

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      appBar: GradientAppBar(
        title: 'Modern Design Demo',
        gradientColors: isDark 
            ? ColorPalette.primaryGradientDark 
            : ColorPalette.primaryGradient,
        actions: [
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.settings_outlined),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Typography Section
            _buildTypographySection(),
            SizedBox(height: 32.h),
            
            // Cards Section
            _buildCardsSection(),
            SizedBox(height: 32.h),
            
            // Buttons Section
            _buildButtonsSection(),
            SizedBox(height: 32.h),
            
            // Text Fields Section
            _buildTextFieldsSection(),
            SizedBox(height: 32.h),
            
            // Color Palette Section
            _buildColorPaletteSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildTypographySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Typography', style: FontPalette.headlineMedium),
        SizedBox(height: 16.h),
        
        Text('Display Large', style: FontPalette.displayLarge),
        Text('Headline Medium', style: FontPalette.headlineMedium),
        Text('Title Large', style: FontPalette.titleLarge),
        Text('Body Large - This is a sample body text to demonstrate the typography scale.', 
             style: FontPalette.bodyLarge),
        Text('Label Medium', style: FontPalette.labelMedium),
        Text('Body Small - Smaller text for captions and secondary information.', 
             style: FontPalette.bodySmall),
      ],
    );
  }

  Widget _buildCardsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Cards', style: FontPalette.headlineMedium),
        SizedBox(height: 16.h),
        
        // Elevated Card
        ModernCard(
          style: ModernCardStyle.elevated,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Elevated Card', style: FontPalette.titleMedium),
              SizedBox(height: 8.h),
              Text('This is an elevated card with shadow effects.', 
                   style: FontPalette.bodyMedium),
            ],
          ),
        ),
        SizedBox(height: 16.h),
        
        // Glass Card
        GlassCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Glass Card', style: FontPalette.titleMedium),
              SizedBox(height: 8.h),
              Text('This is a glass morphism card with blur effects.', 
                   style: FontPalette.bodyMedium),
            ],
          ),
        ),
        SizedBox(height: 16.h),
        
        // Neumorphism Card
        NeumorphismCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Neumorphism Card', style: FontPalette.titleMedium),
              SizedBox(height: 8.h),
              Text('This is a neumorphism card with soft shadows.', 
                   style: FontPalette.bodyMedium),
            ],
          ),
        ),
        SizedBox(height: 16.h),
        
        // Gradient Card
        ModernCard(
          style: ModernCardStyle.gradient,
          gradientColors: ColorPalette.primaryGradient,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Gradient Card', 
                   style: FontPalette.titleMedium.copyWith(color: Colors.white)),
              SizedBox(height: 8.h),
              Text('This is a gradient card with beautiful colors.', 
                   style: FontPalette.bodyMedium.copyWith(color: Colors.white)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildButtonsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Buttons', style: FontPalette.headlineMedium),
        SizedBox(height: 16.h),
        
        // Primary Button
        ModernButton(
          text: 'Primary Button',
          style: ModernButtonStyle.primary,
          width: double.infinity,
          onPressed: () {},
        ),
        SizedBox(height: 12.h),
        
        // Secondary Button
        ModernButton(
          text: 'Secondary Button',
          style: ModernButtonStyle.secondary,
          width: double.infinity,
          onPressed: () {},
        ),
        SizedBox(height: 12.h),
        
        // Glass Button
        ModernButton(
          text: 'Glass Button',
          style: ModernButtonStyle.glass,
          width: double.infinity,
          onPressed: () {},
        ),
        SizedBox(height: 12.h),
        
        // Gradient Button
        ModernButton(
          text: 'Gradient Button',
          style: ModernButtonStyle.gradient,
          width: double.infinity,
          onPressed: () {},
        ),
        SizedBox(height: 12.h),
        
        // Outline Button
        ModernButton(
          text: 'Outline Button',
          style: ModernButtonStyle.outline,
          width: double.infinity,
          onPressed: () {},
        ),
        SizedBox(height: 12.h),
        
        // Button with Icon
        ModernButton(
          text: 'Button with Icon',
          icon: Icons.star,
          style: ModernButtonStyle.primary,
          width: double.infinity,
          onPressed: () {},
        ),
      ],
    );
  }

  Widget _buildTextFieldsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Text Fields', style: FontPalette.headlineMedium),
        SizedBox(height: 16.h),
        
        // Filled Text Field
        ModernTextField(
          labelText: 'Filled Text Field',
          hintText: 'Enter some text',
          style: ModernTextFieldStyle.filled,
          controller: _textController,
        ),
        SizedBox(height: 16.h),
        
        // Outlined Text Field
        ModernTextField(
          labelText: 'Outlined Text Field',
          hintText: 'Enter some text',
          style: ModernTextFieldStyle.outlined,
        ),
        SizedBox(height: 16.h),
        
        // Glass Text Field
        ModernTextField(
          labelText: 'Glass Text Field',
          hintText: 'Enter some text',
          style: ModernTextFieldStyle.glass,
        ),
        SizedBox(height: 16.h),
        
        // Text Field with Icons
        ModernTextField(
          labelText: 'Text Field with Icons',
          hintText: 'Search...',
          style: ModernTextFieldStyle.filled,
          prefixIcon: const Icon(Icons.search),
          suffixIcon: const Icon(Icons.clear),
        ),
      ],
    );
  }

  Widget _buildColorPaletteSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Color Palette', style: FontPalette.headlineMedium),
        SizedBox(height: 16.h),
        
        // Primary Colors
        _buildColorRow('Primary', [
          ColorPalette.primaryColor,
          ColorPalette.primaryColorDark,
          ColorPalette.accentPrimary,
          ColorPalette.accentPrimaryDark,
        ]),
        SizedBox(height: 12.h),
        
        // Status Colors
        _buildColorRow('Status', [
          ColorPalette.successColor,
          ColorPalette.warningColor,
          ColorPalette.errorColor,
          ColorPalette.infoColor,
        ]),
        SizedBox(height: 12.h),
        
        // Grey Scale
        _buildColorRow('Grey Scale', [
          ColorPalette.greyColor1,
          ColorPalette.greyColor2,
          ColorPalette.greyColor3,
          ColorPalette.greyColor4,
        ]),
      ],
    );
  }

  Widget _buildColorRow(String title, List<Color> colors) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: FontPalette.titleMedium),
        SizedBox(height: 8.h),
        Row(
          children: colors.map((color) => Expanded(
            child: Container(
              height: 60.h,
              margin: EdgeInsets.only(right: 8.w),
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
            ),
          )).toList(),
        ),
      ],
    );
  }
}
