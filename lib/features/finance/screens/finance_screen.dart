import 'dart:async';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/utils/convert_helper.dart';

import '../../../core/constants/enums.dart';
import '../../../core/extention.dart';
import '../../../core/theme/my_color_scheme.dart';
import '../../../core/utils/mixin/animation.dart';
import '../../../core/widgets/custom_button.dart';
import '../../home/<USER>/home/<USER>';
import '../logic/finance/finance_cubit.dart';
import '../widgets/finance_shimmer.dart';
import '../widgets/buy_bottom_sheet.dart';

class FinanceScreen extends StatefulWidget {
  const FinanceScreen({super.key});

  @override
  State<FinanceScreen> createState() => _FinanceScreenState();
}

class _FinanceScreenState extends State<FinanceScreen> with StaggeredAnimation {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    context.read<FinanceCubit>()
      ..getAccountInfo()
      ..getProductRule()
      ..getProductBuyList();
    context.read<HomeCubit>().getBalance();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        title: Text(
          StringConstants.finance.tr(),
          style: FontPalette.semiBold20,
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () async => _loadData(),
        color: myColorScheme(context).primaryColor,
        child: BlocBuilder<FinanceCubit, FinanceState>(
          builder: (context, state) {
            final isLoading = state.accountInfoStatus == DataStatus.loading ||
                state.productRuleStatus == DataStatus.loading ||
                state.productBuyListStatus == DataStatus.loading;

            if (isLoading) {
              return const FinanceShimmer();
            }

            if (state.productRuleStatus == DataStatus.failed ||
                state.productBuyListStatus == DataStatus.failed) {
              return _buildErrorState(state.error);
            }

            return _buildSuccessState(state);
          },
        ),
      ),
    );
  }

  Widget _buildErrorState(String? error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            error ?? StringConstants.somethingWentWrong.tr(),
            style: FontPalette.normal16,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: Text(StringConstants.tryAgain.tr()),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessState(FinanceState state) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 600),
              curve: Curves.easeOutCubic,
              tween: Tween(begin: 0.0, end: 1.0),
              builder: (context, value, child) {
                return Transform.translate(
                  offset: Offset(0, 30 * (1 - value)),
                  child: Opacity(
                    opacity: value,
                    child: Transform.scale(
                      scale: 0.95 + (0.05 * value),
                      child: _buildBalanceCard(state),
                    ),
                  ),
                );
              },
            ),
            // Animated product cards with staggered slide up and fade
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 800),
              curve: Curves.easeOutCubic,
              tween: Tween(begin: 0.0, end: 1.0),
              builder: (context, value, child) {
                return Transform.translate(
                  offset: Offset(0, 40 * (1 - value)),
                  child: Opacity(
                    opacity: value,
                    child: Transform.scale(
                      scale: 0.9 + (0.1 * value),
                      child: _buildProductCards(state)
                    ),
                  ),
                );
              },
            ),

            // Animated purchase list with staggered slide up and fade
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 1000),
              curve: Curves.easeOutCubic,
              tween: Tween(begin: 0.0, end: 1.0),
              builder: (context, value, child) {
                return Transform.translate(
                  offset: Offset(0, 50 * (1 - value)),
                  child: Opacity(
                    opacity: value,
                    child: Transform.scale(
                      scale: 0.85 + (0.15 * value),
                      child: _buildPurchaseListSection(state),
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceCard(FinanceState state) {
    final accountData = state.accountInfo?.data;
    final balance = ConvertHelper.formatNumberWithTwoDecimals(accountData?.balance ?? 0.0);
    final expectedProfit = ConvertHelper.formatNumberWithTwoDecimals(accountData?.expectedProfit ?? 0.0);
    final actualProfit = ConvertHelper.formatNumberWithTwoDecimals(accountData?.actualProfit ?? 0.0);

    return Container(
      margin: const EdgeInsets.only(top: 8, bottom: 16),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            balance.toCurrencyWithSymbol(),
            style: FontPalette.bold20.copyWith(letterSpacing: 1.2),
          ),
          Divider(
            color: myColorScheme(context).titleColor?.withValues(alpha: 0.2),
            thickness: 1,
          ),
          const SizedBox(height: 5),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    StringConstants.expectedProfit.tr(),
                    style: FontPalette.normal16
                        .copyWith(color: myColorScheme(context).subTitleColor),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    expectedProfit.toCurrencyWithSymbol(),
                    style: FontPalette.bold18,
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    StringConstants.actualProfit.tr(),
                    style: FontPalette.normal16
                        .copyWith(color: myColorScheme(context).subTitleColor),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    actualProfit.toCurrencyWithSymbol(),
                    style: FontPalette.bold18,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductCards(FinanceState state) {
    final products = state.productRule?.data ?? [];

    if (products.isEmpty) {
      return const SizedBox.shrink();
    }

    return ListView.builder(
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: (products.length / 2).ceil(),
      itemBuilder: (context, index) {
        final firstIndex = index * 2;
        final secondIndex = firstIndex + 1;
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  child: _ProductCard(
                    product: products[firstIndex],
                    onBuy: () => _handleProductBuy(products[firstIndex]),
                    onRefresh: _loadData,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: secondIndex < products.length
                      ? _ProductCard(
                          product: products[secondIndex],
                          onBuy: () => _handleProductBuy(products[secondIndex]),
                          onRefresh: _loadData,
                        )
                      : const SizedBox.shrink(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }


  Widget _buildPurchaseListSection(FinanceState state) {
    final purchases = state.productBuyList?.data ?? [];
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          StringConstants.purchaseList.tr(),
          style: FontPalette.bold22,
        ),
        const SizedBox(height: 12),
        if (purchases.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(32.0),
              child: Text(
                StringConstants.noPurchasesYet.tr(),
                style: FontPalette.normal16
                    .copyWith(color: myColorScheme(context).subTitleColor),
              ),
            ),
          )
        else
          ...purchases.map((purchase) => Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: _PurchaseItem(
                  title: purchase.financeProductName ?? '',
                  amount: ConvertHelper.formatNumberWithTwoDecimals(purchase.amount ?? 0)
                      .toCurrencyWithSymbol(),
                  profitAmount: ConvertHelper.formatNumberWithTwoDecimals(purchase.profitAmount ?? 0)
                      .toCurrencyWithSymbol(),
                  interestRate: '${purchase.rate ?? 0}%',
                  lockPeriod:
                      '${purchase.day ?? 0} ${StringConstants.day.tr()}',
                  createTime: purchase.startTime == null
                      ? ''
                      : _formatDateTime(
                          DateTime.parse(purchase.startTime ?? '')),
                  status: _getStatusText(purchase.status ?? 0),
                  statusColor: _getStatusColor(purchase.status ?? 0),
                  endTime: purchase.endTime == null
                      ? ''
                      : _formatDateTime(DateTime.parse(purchase.endTime ?? '')),
                ),
              )),
      ],
    );
  }

  void _handleProductBuy(dynamic product) {
    _showBuyBottomSheet(product);
  }

  void _showBuyBottomSheet(dynamic product) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BuyBottomSheet(
        product: product,
        onClose: () => Navigator.pop(context),
      ),
    );
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '';
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  }

  String _getStatusText(int status) => switch (status) {
        0 => StringConstants.progress.tr(),
        1 => StringConstants.completed.tr(),
        2 => StringConstants.settled.tr(),
        _ => StringConstants.unknown.tr()
      };

  Color _getStatusColor(int status) => switch (status) {
        0 => const Color(0xFF1677FF), // Processing
        1 => const Color(0xFF52C41A), // Completed
        2 => const Color(0xFF52C41A), // Settled
        _ => Colors.grey
      };
}

enum ProductStatus { upcoming, available, ended }

class _ProductCard extends StatefulWidget {
  final dynamic product;
  final VoidCallback onBuy;
  final VoidCallback onRefresh;

  const _ProductCard({
    required this.product,
    required this.onBuy,
    required this.onRefresh,
  });

  @override
  State<_ProductCard> createState() => _ProductCardState();
}

class _ProductCardState extends State<_ProductCard> {
  Timer? _timer;
  Duration _sellTimeRemaining = Duration.zero;
  Duration _availabilityTimeRemaining = Duration.zero;
  ProductStatus _status = ProductStatus.available;

  @override
  void initState() {
    super.initState();
    _updateStatus();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  DateTime _parseTimestamp(int? timestamp, String type) {
    if (timestamp == null || timestamp == 0) {
      // Provide reasonable fallbacks for testing
      switch (type) {
        case 'start':
          return DateTime.now()
              .subtract(const Duration(hours: 1)); // Started 1 hour ago
        case 'end':
          return DateTime.now()
              .add(const Duration(hours: 24)); // Ends in 24 hours
        case 'sell':
          return DateTime.now().add(const Duration(days: 7)); // Sell in 7 days
        default:
          return DateTime.now();
      }
    }

    // Try parsing as seconds first, then milliseconds
    DateTime result;
    if (timestamp > 1000000000000) {
      // Looks like milliseconds (13+ digits)
      result = DateTime.fromMillisecondsSinceEpoch(timestamp);
    } else if (timestamp > 1000000000) {
      // Looks like seconds (10 digits)
      result = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    } else {
      // Very small number, might be invalid - use fallback
      switch (type) {
        case 'start':
          return DateTime.now().subtract(const Duration(hours: 1));
        case 'end':
          return DateTime.now().add(const Duration(hours: 24));
        case 'sell':
          return DateTime.now().add(const Duration(days: 7));
        default:
          return DateTime.now();
      }
    }

    return result;
  }

  void _updateStatus() {
    final now = DateTime.now();

    // Debug logging for timestamp values
    // Convert timestamps to DateTime with robust parsing
    final availableStartTime =
        _parseTimestamp(widget.product.availableStartTime, 'start');
    final availableEndTime =
        _parseTimestamp(widget.product.availableEndTime, 'end');
    final sellTime = _parseTimestamp(widget.product.sellTime, 'sell');

    // Status logic matching React reference exactly - NO setState here!
    if (now.isBefore(availableStartTime)) {
      _status = ProductStatus.upcoming;
      _availabilityTimeRemaining = availableStartTime.difference(now);
    } else if (now.isAfter(availableEndTime)) {
      _status = ProductStatus.ended;
      _availabilityTimeRemaining = Duration.zero;
    } else {
      _status = ProductStatus.available;
      _availabilityTimeRemaining = availableEndTime.difference(now);
    }

    // Always calculate sell time remaining
    _sellTimeRemaining = sellTime.difference(now);
    if (_sellTimeRemaining.isNegative) {
      _sellTimeRemaining = Duration.zero;
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _updateStatus();
        // if (_sellTimeRemaining.inSeconds <= 0 &&
        //     _availabilityTimeRemaining.inSeconds <= 0) {
        //   widget.onRefresh();
        // }
      });
    });
  }

  Color _getStatusColor() {
    switch (_status) {
      case ProductStatus.upcoming:
        return const Color(0xFF1890FF); // Blue
      case ProductStatus.available:
        return const Color(0xFF52C41A); // Green
      case ProductStatus.ended:
        return const Color(0xFFFF4D4F); // Red
    }
  }

  String _getStatusText() {
    switch (_status) {
      case ProductStatus.upcoming:
        return StringConstants.upcoming.tr();
      case ProductStatus.available:
        return StringConstants.available.tr();
      case ProductStatus.ended:
        return StringConstants.soldOut
            .tr(); // Keep "Sold Out" text for ended status
    }
  }

  Widget _buildCountdown(Duration duration) {
    // Handle negative durations
    if (duration.isNegative) {
      return Row(
        children: [
          _buildCountdownItem('00d'),
          const SizedBox(width: 4),
          _buildCountdownItem('00h'),
          const SizedBox(width: 4),
          _buildCountdownItem('00m'),
          const SizedBox(width: 4),
          _buildCountdownItem('00s'),
        ],
      );
    }

    final days = duration.inDays;
    final hours = duration.inHours % 24;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    return Row(
      children: [
        _buildCountdownItem('${days.toString().padLeft(2, '0')}d'),
        const SizedBox(width: 4),
        _buildCountdownItem('${hours.toString().padLeft(2, '0')}h'),
        const SizedBox(width: 4),
        _buildCountdownItem('${minutes.toString().padLeft(2, '0')}m'),
        const SizedBox(width: 4),
        _buildCountdownItem('${seconds.toString().padLeft(2, '0')}s'),
      ],
    );
  }

  Widget _buildCountdownItem(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: myColorScheme(context).primaryColor?.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: FontPalette.medium12.copyWith(
          color: const Color(0xFF1890FF),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and status
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  widget.product.name ?? '',
                  style: FontPalette.bold18,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  _getStatusText(),
                  style: FontPalette.bold12.copyWith(color: Colors.white),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Interest Rate
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${StringConstants.interestRate.tr()}:',
                style: FontPalette.semiBold14.copyWith(color: Colors.green),
              ),
              Text(
                '${widget.product.interestRate ?? 0}%',
                style: FontPalette.bold16.copyWith(color: Colors.green),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Availability time (for upcoming/available products)
          if (_status == ProductStatus.upcoming) ...[
            Text(
              '${StringConstants.availableFrom.tr()}:',
              style: FontPalette.normal14.copyWith(
                color: myColorScheme(context).subTitleColor,
              ),
            ),
            const SizedBox(height: 4),
            _buildCountdown(_availabilityTimeRemaining),
            const SizedBox(height: 8),
          ] else if (_status == ProductStatus.available) ...[
            Text(
              '${StringConstants.availableUntil.tr()}:',
              style: FontPalette.normal14.copyWith(
                color: myColorScheme(context).subTitleColor,
              ),
            ),
            const SizedBox(height: 4),
            _buildCountdown(_availabilityTimeRemaining),
            const SizedBox(height: 8),
          ],

          // Sell Time
          Text(
            '${StringConstants.sellTime.tr()}:',
            style: FontPalette.normal14.copyWith(
              color: myColorScheme(context).subTitleColor,
            ),
          ),
          const SizedBox(height: 4),
          _buildCountdown(_sellTimeRemaining),
          const SizedBox(height: 12),

          // Buy Button - only enabled for available status
          const Spacer(),
          CustomButton(
            height: 40.h,
            width: double.infinity,
            onPressed: _status == ProductStatus.available ? widget.onBuy : null,
            label: StringConstants.buy.tr(),
            backgroundColor: _status == ProductStatus.available
                ? myColorScheme(context).primaryColor
                : Colors.grey.withValues(alpha: 0.5),
            borderRadiusUser: 10,
            elevation: _status == ProductStatus.available ? 5 : 0,
          ),
        ],
      ),
    );
  }
}

class _PurchaseItem extends StatelessWidget {
  final String title;
  final String amount;
  final String profitAmount;
  final String interestRate;
  final String lockPeriod;
  final String createTime;
  final String endTime;
  final String status;
  final Color statusColor;

  const _PurchaseItem({
    required this.title,
    required this.amount,
    required this.profitAmount,
    required this.interestRate,
    required this.lockPeriod,
    required this.createTime,
    required this.endTime,
    required this.status,
    required this.statusColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Animated title and status
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: FontPalette.bold20,
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  status,
                  style: FontPalette.bold14.copyWith(color: statusColor),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${StringConstants.amount.tr()}: ',
                    style: FontPalette.normal14
                        .copyWith(color: myColorScheme(context).subTitleColor),
                  ),
                  Text(
                    amount,
                    style: FontPalette.bold16,
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '${StringConstants.profitAmount.tr()}: ',
                    style: FontPalette.normal14
                        .copyWith(color: myColorScheme(context).subTitleColor),
                  ),
                  Text(
                    profitAmount,
                    style: FontPalette.bold16,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    StringConstants.interestRate.tr(),
                    style: FontPalette.normal14
                        .copyWith(color: myColorScheme(context).subTitleColor),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    interestRate,
                    style: FontPalette.bold14,
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${StringConstants.lockPeriod.tr()}: ',
                    style: FontPalette.normal14
                        .copyWith(color: myColorScheme(context).subTitleColor),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    lockPeriod,
                    style: FontPalette.bold14,
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${StringConstants.startTime.tr()}: ',
                    style: FontPalette.normal14
                        .copyWith(color: myColorScheme(context).subTitleColor),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    createTime,
                    style: FontPalette.bold14,
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${StringConstants.endTime.tr()}: ',
                    style: FontPalette.normal14
                        .copyWith(color: myColorScheme(context).subTitleColor),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    endTime,
                    style: FontPalette.bold14,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

}
