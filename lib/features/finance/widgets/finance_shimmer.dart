import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/theme/my_color_scheme.dart';
import '../../../core/widgets/common_shimmer.dart';

class FinanceShimmer extends StatelessWidget {
  const FinanceShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Balance and profit summary shimmer
            Container(
              margin: const EdgeInsets.only(top: 8, bottom: 16),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: myColorScheme(context).cardColor,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.03),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Main balance shimmer
                  CommonShimmer(
                    width: 200.w,
                    height: 32.h,
                    br: 8.r,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CommonShimmer(
                            width: 120.w,
                            height: 16.h,
                            br: 4.r,
                          ),
                          const SizedBox(height: 4),
                          CommonShimmer(
                            width: 140.w,
                            height: 18.h,
                            br: 4.r,
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          CommonShimmer(
                            width: 100.w,
                            height: 16.h,
                            br: 4.r,
                          ),
                          const SizedBox(height: 4),
                          CommonShimmer(
                            width: 80.w,
                            height: 18.h,
                            br: 4.r,
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Product cards shimmer
            Row(
              children: [
                Expanded(
                  child: _ProductCardShimmer(),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _ProductCardShimmer(),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Purchase list title shimmer
            CommonShimmer(
              width: 150.w,
              height: 22.h,
              br: 4.r,
            ),
            
            const SizedBox(height: 12),
            
            // Purchase items shimmer
            ...List.generate(3, (index) => Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: _PurchaseItemShimmer(),
            )),
            
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}

class _ProductCardShimmer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CommonShimmer(
            width: 80.w,
            height: 20.h,
            br: 4.r,
          ),
          const SizedBox(height: 8),
          CommonShimmer(
            width: 120.w,
            height: 18.h,
            br: 4.r,
          ),
          const SizedBox(height: 8),
          CommonShimmer(
            width: 100.w,
            height: 16.h,
            br: 4.r,
          ),
          const SizedBox(height: 16),
          CommonShimmer(
            width: double.infinity,
            height: 40.h,
            br: 10.r,
          ),
        ],
      ),
    );
  }
}

class _PurchaseItemShimmer extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CommonShimmer(
                width: 100.w,
                height: 20.h,
                br: 4.r,
              ),
              CommonShimmer(
                width: 80.w,
                height: 24.h,
                br: 6.r,
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonShimmer(
                    width: 60.w,
                    height: 14.h,
                    br: 4.r,
                  ),
                  const SizedBox(height: 4),
                  CommonShimmer(
                    width: 100.w,
                    height: 16.h,
                    br: 4.r,
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  CommonShimmer(
                    width: 80.w,
                    height: 14.h,
                    br: 4.r,
                  ),
                  const SizedBox(height: 4),
                  CommonShimmer(
                    width: 90.w,
                    height: 16.h,
                    br: 4.r,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          CommonShimmer(
            width: 150.w,
            height: 14.h,
            br: 4.r,
          ),
          const SizedBox(height: 4),
          CommonShimmer(
            width: 120.w,
            height: 14.h,
            br: 4.r,
          ),
          const SizedBox(height: 4),
          CommonShimmer(
            width: 180.w,
            height: 14.h,
            br: 4.r,
          ),
        ],
      ),
    );
  }
}
