import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/api/network/network_helper.dart';
import 'package:sf_app_v2/core/constants/assets.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/services/wallet_balance/wallet_balance_service.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/common_pin_field.dart';
import 'package:sf_app_v2/core/widgets/common_text_field.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';
import 'package:sf_app_v2/features/finance/logic/finance/finance_cubit.dart';

class BuyBottomSheet extends StatefulWidget {
  final dynamic product;
  final VoidCallback onClose;

  const BuyBottomSheet({
    super.key,
    required this.product,
    required this.onClose,
  });

  @override
  State<BuyBottomSheet> createState() => _BuyBottomSheetState();
}

class _BuyBottomSheetState extends State<BuyBottomSheet> {
  final PageController _pageController = PageController();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final GlobalKey<FormState> _amountFormKey = GlobalKey<FormState>();
  int _currentStep = 0;

  @override
  void initState() {
    super.initState();
    context.read<FinanceCubit>().resetBuyProductStatus();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _amountController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep == 0) {
      if (_amountFormKey.currentState?.validate() ?? false) {
        setState(() => _currentStep = 1);
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    } else {
      _confirmPurchase();
    }
  }

  void _navigateToStep(int targetStep) {
    if (targetStep < _currentStep) {
      setState(() => _currentStep = targetStep);
      _pageController.animateToPage(
        targetStep,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _confirmPurchase() {
    if (_formKey.currentState?.validate() ?? false) {
      context.read<FinanceCubit>().buyProduct(
            ruleId: widget.product.id,
            amount: int.parse(_amountController.text),
            password: _passwordController.text,
            accountType: 5,
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<FinanceCubit, FinanceState>(
      listenWhen: (previous, current) =>
          previous.buyProductStatus != current.buyProductStatus,
      listener: (context, state) {
        if (state.buyProductStatus == DataStatus.success) {
          context.read<FinanceCubit>()
            ..getProductBuyList()
            ..getAccountInfo();
        }
        if (state.buyProductStatus == DataStatus.failed) {
          NetworkHelper.handleMessage(
            state.error,
            context,
            type: HandleTypes.customDialog,
            snackBarType: SnackBarType.error,
          );
        }
      },
      builder: (context, state) {
        return Container(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom > 0
                ? MediaQuery.of(context).viewInsets.bottom
                : 20.h,
          ),
          decoration: BoxDecoration(
            color: myColorScheme(context).cardColor,
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(20.r),
            ),
          ),
          child: ListView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _buildHeader(),
              state.buyProductStatus == DataStatus.success
                  ? _buildSuccessView(widget.onClose)
                  : _buildMainContent(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return SizedBox(
      height: 40.h,
      child: Stack(
        children: [
          Center(
            child: Container(
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: myColorScheme(context).borderColor,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: IconButton(
              icon: const Icon(Icons.close),
              onPressed: widget.onClose,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return SingleChildScrollView(
      child: Container(
        color: myColorScheme(context).cardColor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBreadcrumb(),
            _currentStep == 0 ? _buildAmountStep() : _buildPasswordStep(),
          ],
        ),
      ),
    );
  }

  Widget _buildBreadcrumb() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
      child: Row(
        children: [
          _buildBreadcrumbItem(
            title: StringConstants.amount.tr(),
            isActive: _currentStep >= 0,
            onTap: () => _navigateToStep(0),
          ),
          Icon(
            Icons.chevron_right,
            color: ColorPalette.greyColor3,
            size: 20.w,
          ),
          _buildBreadcrumbItem(
            title: StringConstants.walletPass.tr(),
            isActive: _currentStep >= 1,
            onTap: () => _navigateToStep(1),
          ),
        ],
      ),
    );
  }

  Widget _buildBreadcrumbItem({
    required String title,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Text(
        title,
        style: FontPalette.medium16.copyWith(
          color: isActive
              ? myColorScheme(context).primaryColor
              : ColorPalette.greyColor3,
        ),
      ),
    );
  }

  Widget _buildAmountStep() {
    final balance = WalletBalanceService().cashBalance;
    return Form(
      key: _amountFormKey,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 20.h),
            CommonTextField(
              controller: _amountController,
              textInputType: TextInputType.number,
              labelText: StringConstants.amount.tr(),
              hintText: StringConstants.enterAmount.tr(),
              textInputAction: TextInputAction.done,
              autovalidateMode: AutovalidateMode.onUserInteraction,
              onFieldSubmitted: (_) => _nextStep(),
              validator: (_) => _validateAmount(balance),
            ),
            SizedBox(height: 16.h),
            Text(
              '${StringConstants.availableBalance.tr()}: ${NumberFormat('#,##0.00').format(balance).toCurrency()}',
              style: FontPalette.normal14,
            ),
            SizedBox(height: 40.h),
            _buildStepIndicators(isFirstStep: true),
            SizedBox(height: 24.h),
            _buildActionButton(
              label: StringConstants.next.tr(),
              onPressed: _nextStep,
            ),
            SizedBox(height: 50.h),
          ],
        ),
      ),
    );
  }

  String? _validateAmount(double balance) {
    final value = _amountController.text;
    if (value.isEmpty) {
      return StringConstants.pleaseEnterAValidAmount.tr();
    }
    final amount = int.tryParse(value);
    if (amount == null || amount <= 0) {
      return StringConstants.pleaseEnterAValidAmount.tr();
    }
    if (amount > balance) {
      return StringConstants.insufficientBalance.tr();
    }
    return null;
  }

  Widget _buildPasswordStep() {
    return Form(
      key: _formKey,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 20.h),
            Text(
              StringConstants.enterWalletPassword.tr(),
              style: FontPalette.semiBold18.copyWith(
                color: myColorScheme(context).primaryColor,
              ),
            ),
            SizedBox(height: 32.h),
            Center(
              child: CommonPinFiledText(
                controller: _passwordController,
                obscureText: true,
                type: 'wallet',
              ),
            ),
            SizedBox(height: 40.h),
            _buildStepIndicators(isFirstStep: false),
            SizedBox(height: 24.h),
            _buildActionButton(
              label: StringConstants.confirm.tr(),
              onPressed: _nextStep,
            ),
            SizedBox(height: 50.h),
          ],
        ),
      ),
    );
  }

  Widget _buildStepIndicators({required bool isFirstStep}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildStepDot(isActive: isFirstStep),
        SizedBox(width: 8.w),
        _buildStepDot(isActive: !isFirstStep),
      ],
    );
  }

  Widget _buildStepDot({required bool isActive}) {
    return Container(
      width: 8.w,
      height: 8.w,
      decoration: BoxDecoration(
        color: isActive
            ? myColorScheme(context).primaryColor
            : ColorPalette.greyColor3,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildActionButton({
    required String label,
    required VoidCallback onPressed,
  }) {
    return CustomButton(
      width: double.infinity,
      height: 56.h,
      label: label,
      onPressed: onPressed,
      backgroundColor: myColorScheme(context).primaryColor,
    );
  }

  Widget _buildSuccessView(VoidCallback onClose) {
    return Container(
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
      ),
      child: Column(
        children: [
          87.verticalSpace,
          CircleAvatar(
            backgroundColor: Colors.transparent,
            radius: 60.r,
            child: SvgPicture.asset(Assets.alertSuccess),
          ),
          Text(
            StringConstants.purchase.tr(),
            style: FontPalette.medium18,
          ),
          Text(
            StringConstants.successful.tr(),
            style: FontPalette.semiBold40,
          ),
          54.verticalSpace,
          BlocSelector<FinanceCubit, FinanceState, DataStatus>(
            selector: (state) => state.buyProductStatus,
            builder: (context, state) {
              return CustomButton(
                width: 384.w,
                height: 56.h,
                label: StringConstants.done.tr(),
                onPressed: () {
                  _currentStep = 0;
                  context.read<FinanceCubit>().resetBuyProductStatus();
                  onClose();
                },
                child: Text(StringConstants.done.tr()),
              );
            },
          ),
        ],
      ),
    );
  }
}
