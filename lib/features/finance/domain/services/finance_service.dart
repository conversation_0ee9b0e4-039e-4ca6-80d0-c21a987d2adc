import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/finance/domain/models/account_info/account_info.dart';
import 'package:sf_app_v2/features/finance/domain/models/product_buy_list/product_buy_list.dart';
import 'package:sf_app_v2/features/finance/domain/models/product_rule/product_rule.dart';

import '../../../../core/api/endpoint/api_endpoints.dart';
import '../../../../core/api/network/network.dart';
import '../repository/finance_repository.dart';

/// Service class that implements the FinanceRepository interface to handle finance-related API calls
@Injectable(as: FinanceRepository)
class FinanceService implements FinanceRepository {
  /// Fetches user's account information
  /// Returns a [ResponseResult] containing [AccountInfo] on success
  @override
  Future<ResponseResult<AccountInfo>> getAccountInfo() async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.financeAccountInfo,
         isSigninRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: AccountInfo.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get account info');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches list of purchased financial products
  /// Returns a [ResponseResult] containing [ProductBuyList] on success
  @override
  Future<ResponseResult<ProductBuyList>> getProductBuyList() async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.financeProductBuyList,
         isSigninRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: ProductBuyList.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get product buy list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches financial product rules and terms
  /// Returns a [ResponseResult] containing [ProductRule] on success
  @override
  Future<ResponseResult<ProductRule>> getProductRule() async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.financeProductRule,
         isSigninRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: ProductRule.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get product rule');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Purchases a financial product
  /// [ruleId] - ID of the product rule
  /// [amount] - Amount to purchase
  /// [password] - User's password for verification
  /// [accountType] - Type of account to use for purchase
  /// Returns a [ResponseResult] containing bool on success
  @override
  Future<ResponseResult<bool>> buyProduct(
      {required int ruleId,
      required int amount,
      required String password,
      required int accountType}) async {
    try {
      final response = await NetworkProvider().post(
        ApiEndpoints.financeProductBuy,
        data: {
          'ruleId': ruleId,
          'amount': amount,
          'password': password,
          'accountType': accountType
        },
         isSigninRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to buy product');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
