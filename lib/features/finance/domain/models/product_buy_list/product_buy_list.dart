// To parse this JSON data, do
//
//     final productBuyList = productBuyListFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'product_buy_list.freezed.dart';
part 'product_buy_list.g.dart';

ProductBuyList productBuyListFromJson( str) => ProductBuyList.fromJson((str));

String productBuyListToJson(ProductBuyList data) => json.encode(data.toJson());

@freezed
class ProductBuyList with _$ProductBuyList {
    const factory ProductBuyList({
        int? code,
        List<ProductBuyListData>? data,
        String? msg,
    }) = _ProductBuyList;

    factory ProductBuyList.fromJson(Map<String, dynamic> json) => _$ProductBuyListFromJson(json);
}

@freezed
class ProductBuyListData with _$ProductBuyListData {
    const factory ProductBuyListData({
        num? amount,
        String? createTime,
        int? day,
        String? description,
        String? endTime,
        String? financeProductName,
        num? profitAmount,
        num? rate,
        String? startTime,
        int? status,
        int? userId,
    }) = _ProductBuyListData;

    factory ProductBuyListData.fromJson(Map<String, dynamic> json) => _$ProductBuyListDataFromJson(json);
}
