// To parse this JSON data, do
//
//     final accountInfo = accountInfoFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'account_info.freezed.dart';
part 'account_info.g.dart';

AccountInfo accountInfoFromJson(str) => AccountInfo.fromJson((str));

String accountInfoToJson(AccountInfo data) => json.encode(data.toJson());

@freezed
class AccountInfo with _$AccountInfo {
    const factory AccountInfo({
        int? code,
        Data? data,
        String? msg,
    }) = _AccountInfo;

    factory AccountInfo.fromJson(Map<String, dynamic> json) => _$AccountInfoFromJson(json);
}

@freezed
class Data with _$Data {
    const factory Data({
        num? actualProfit,
        num? balance,
        num? expectedProfit,
    }) = _Data;

    factory Data.fromJson(Map<String, dynamic> json) => _$Data<PERSON>(json);
}
