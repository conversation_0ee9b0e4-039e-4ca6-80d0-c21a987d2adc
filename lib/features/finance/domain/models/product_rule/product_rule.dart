// To parse this JSON data, do
//
//     final productRule = productRuleFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'product_rule.freezed.dart';
part 'product_rule.g.dart';

ProductRule productRuleFromJson( str) => ProductRule.fromJson((str));

String productRuleToJson(ProductRule data) => json.encode(data.toJson());

@freezed
class ProductRule with _$ProductRule {
    const factory ProductRule({
        int? code,
        List<ProductRuleData>? data,
        String? msg,
    }) = _ProductRule;

    factory ProductRule.fromJson(Map<String, dynamic> json) => _$ProductRuleFromJson(json);
}

@freezed
class ProductRuleData with _$ProductRuleData {
    const factory ProductRuleData({
        int? availableEndTime,
        int? availableStartTime,
        String? desc,
        int? holdingDuration,
        int? id,
        num? interestRate,
        int? minDay,
        String? name,
        int? sellTime,
    }) = _ProductRuleData;

    factory ProductRuleData.fromJson(Map<String, dynamic> json) => _$ProductRuleDataFromJson(json);
}
