import '../../../../core/models/result.dart';
import '../models/account_info/account_info.dart';
import '../models/product_buy_list/product_buy_list.dart';
import '../models/product_rule/product_rule.dart';

abstract class FinanceRepository {
  const FinanceRepository();

  Future<ResponseResult<AccountInfo>> getAccountInfo();
  Future<ResponseResult<ProductRule>> getProductRule();
  Future<ResponseResult<ProductBuyList>> getProductBuyList();
  Future<ResponseResult<bool>> buyProduct(
      {required int ruleId,
      required int amount,
      required String password,
      required int accountType});
}
