import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';

import '../../domain/models/account_info/account_info.dart';
import '../../domain/models/product_buy_list/product_buy_list.dart';
import '../../domain/models/product_rule/product_rule.dart';
import '../../domain/repository/finance_repository.dart';

part 'finance_state.dart';

@injectable
class FinanceCubit extends Cubit<FinanceState> {
  final FinanceRepository financeRepository;

  FinanceCubit(this.financeRepository) : super(const FinanceState());

  Future<void> getAccountInfo() async {
    emit(
      state.copyWith(
        accountInfoStatus: DataStatus.loading,
      ),
    );
    try {
      final result = await financeRepository.getAccountInfo();
      if (result.data != null) {
        emit(state.copyWith(
            accountInfoStatus: DataStatus.success, accountInfo: result.data));
      } else {
        emit(
          state.copyWith(
            accountInfoStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          accountInfoStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> getProductRule() async {
    emit(state.copyWith(productRuleStatus: DataStatus.loading));
    try {
      final result = await financeRepository.getProductRule();
      if (result.data != null) {
        emit(state.copyWith(
            productRuleStatus: DataStatus.success, productRule: result.data));
      } else {
        emit(state.copyWith(
            productRuleStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(
          productRuleStatus: DataStatus.failed, error: e.toString()));
    }
  }

  Future<void> getProductBuyList() async {
    emit(state.copyWith(productBuyListStatus: DataStatus.loading));
    try {
      final result = await financeRepository.getProductBuyList();
      if (result.data != null) {
        emit(state.copyWith(
            productBuyListStatus: DataStatus.success,
            productBuyList: result.data));
      } else {
        emit(state.copyWith(
            productBuyListStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(
          productBuyListStatus: DataStatus.failed, error: e.toString()));
    }
  }

  Future<void> buyProduct(
      {required int ruleId,
      required int amount,
      required String password,
      required int accountType}) async {
    emit(state.copyWith(buyProductStatus: DataStatus.loading));
    try {
      final result = await financeRepository.buyProduct(
        ruleId: ruleId,
        amount: amount,
        password: password,
        accountType: accountType,
      );
      if (result.data ?? false) {
        emit(state.copyWith(buyProductStatus: DataStatus.success));
      } else {
        emit(state.copyWith(buyProductStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(buyProductStatus: DataStatus.failed, error: e.toString()));
    }
  }

  resetBuyProductStatus() {
    emit(state.copyWith(buyProductStatus: DataStatus.idle));
  }
}
