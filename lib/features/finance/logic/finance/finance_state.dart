part of 'finance_cubit.dart';

class FinanceState extends Equatable {
  final AccountInfo? accountInfo;
  final ProductRule? productRule;
  final ProductBuyList? productBuyList;
  final DataStatus accountInfoStatus;
  final DataStatus productRuleStatus;
  final DataStatus productBuyListStatus;
  final DataStatus buyProductStatus;
  final String? error;

  const FinanceState({
    this.accountInfo,
    this.productRule,
    this.productBuyList,
    this.accountInfoStatus = DataStatus.idle,
    this.productRuleStatus = DataStatus.idle,
    this.productBuyListStatus = DataStatus.idle,
    this.buyProductStatus = DataStatus.idle,
    this.error,
  });

  @override
  List<Object?> get props => [
        accountInfo,
        productRule,
        productBuyList,
        accountInfoStatus,
        productRuleStatus,
        productBuyListStatus,
        buyProductStatus,
        error
      ];

  FinanceState copyWith({
    AccountInfo? accountInfo,
    ProductRule? productRule,
    ProductBuyList? productBuyList,
    DataStatus? accountInfoStatus,
    DataStatus? productRuleStatus,
    DataStatus? productBuyListStatus,
    DataStatus? buyProductStatus,
    String? error,
  }) =>
      FinanceState(
        accountInfo: accountInfo ?? this.accountInfo,
        productRule: productRule ?? this.productRule,
        productBuyList: productBuyList ?? this.productBuyList,
        accountInfoStatus: accountInfoStatus ?? this.accountInfoStatus,
        productRuleStatus: productRuleStatus ?? this.productRuleStatus,
        productBuyListStatus: productBuyListStatus ?? this.productBuyListStatus,
        buyProductStatus: buyProductStatus ?? this.buyProductStatus,
        error: error ?? this.error,
      );
}
