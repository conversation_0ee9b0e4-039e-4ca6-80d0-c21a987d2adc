import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/api/endpoint/api_endpoints.dart';
import 'package:sf_app_v2/core/api/network/network.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/profile/domain/models/add_wallet_model/add_wallet_model.dart';
import '../repository/profile_repository.dart';
import '../models/profile/profile_info.dart';
import '../models/email_check/email_code_check.dart';

/// Service class that implements the ProfileRepository interface to handle profile-related API calls
@Injectable(as: ProfileRepository)
class ProfileService implements ProfileRepository {
  /// Fetches user profile information
  /// Returns a [ResponseResult] containing [ProfileInfo] on success
  @override
  Future<ResponseResult<ProfileInfo>> requestProfileInfo() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.profileInfo,
         isSigninRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: ProfileInfo.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch profile info');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Adds a new wallet address for the user
  /// [address] - Wallet address to add
  /// [name] - Name/label for the wallet
  /// [type] - Type of wallet
  /// Returns a [ResponseResult] containing [AddWalletModel] on success
  @override
  Future<ResponseResult<AddWalletModel>> addWallet({
    required String address,
    required String name,
    required String type,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.expendAddress,
        data: {
          "address": address,
          "name": name,
          "type": type,
        },
         isSigninRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: AddWalletModel.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to add wallet');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Requests an email verification code to be sent
  /// Returns a [ResponseResult] containing [EmailCodeCheck] on success
  @override
  Future<ResponseResult<EmailCodeCheck>> requestEmailSend() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.emailCaptchaProfile,
         isSigninRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: EmailCodeCheck.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to send email code');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Verifies an email verification code
  /// [code] - Verification code to check
  /// [nonce] - Nonce value for verification
  /// Returns a [ResponseResult] containing [EmailCodeCheck] on success
  @override
  Future<ResponseResult<EmailCodeCheck>> checkEmailCode({
    required String code,
    required String nonce,
  }) async {
    try {
      final response = await NetworkProvider().post(
        ApiEndpoints.emailCodeCheck,
        data: {
          "code": code,
          "nonce": nonce,
        },
         isSigninRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: EmailCodeCheck.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to check email code');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Changes the user's wallet password
  /// [nonce] - Nonce value for verification
  /// [payPassword] - New wallet password
  /// [confirmPayPassword] - Confirmation of new wallet password
  /// Returns a [ResponseResult] containing bool indicating success
  @override
  Future<ResponseResult<bool>> changeWalletPassword({
    required String nonce,
    required String payPassword,
    required String confirmPayPassword,
  }) async {
    try {
      final response = await NetworkProvider().post(
        ApiEndpoints.changeWalletPassword,
        data: {
          "nonce": nonce,
          "payPassword": payPassword,
          "confirmPayPassword": confirmPayPassword,
        },
         isSigninRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to change wallet password');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Changes the user's login password
  /// [nonce] - Nonce value for verification
  /// [payPassword] - New login password
  /// [confirmPayPassword] - Confirmation of new login password
  /// Returns a [ResponseResult] containing bool indicating success
  @override
  Future<ResponseResult<bool>> changeLoginPassword({
    required String nonce,
    required String payPassword,
    required String confirmPayPassword,
  }) async {
    try {
      final response = await NetworkProvider().post(
        ApiEndpoints.changeLoginPassword,
        data: {
          "nonce": nonce,
          "password": payPassword,
          "confirmPassword": confirmPayPassword,
        },
         isSigninRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to change wallet password');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Deletes a wallet address
  /// [addressId] - ID of the wallet address to delete
  /// Returns a [ResponseResult] containing bool indicating success
  @override
  Future<ResponseResult<bool>> deleteWalletAddress({
    required int addressId,
  }) async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.deleteExpendAddress(addressId),
         isSigninRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to delete wallet address');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
