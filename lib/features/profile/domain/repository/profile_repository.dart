import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/profile/domain/models/add_wallet_model/add_wallet_model.dart';
import 'package:sf_app_v2/features/profile/domain/models/profile/profile_info.dart';
import 'package:sf_app_v2/features/profile/domain/models/email_check/email_code_check.dart';

abstract class ProfileRepository {
  const ProfileRepository();

  Future<ResponseResult<ProfileInfo>> requestProfileInfo();

  Future<ResponseResult<AddWalletModel>> addWallet({
    required String address,
    required String name,
    required String type,
  });

  Future<ResponseResult<EmailCodeCheck>> requestEmailSend();

  Future<ResponseResult<EmailCodeCheck>> checkEmailCode({
    required String code,
    required String nonce,
  });

  Future<ResponseResult<bool>> changeWalletPassword({
    required String nonce,
    required String payPassword,
    required String confirmPayPassword,
  });

  Future<ResponseResult<bool>> changeLoginPassword({
    required String nonce,
    required String payPassword,
    required String confirmPayPassword,
  });

  Future<ResponseResult<bool>> deleteWalletAddress({
    required int addressId,
  });
}
