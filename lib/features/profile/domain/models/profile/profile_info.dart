// To parse this JSON data, do
//
//     final profileInfo = profileInfoFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'profile_info.freezed.dart';
part 'profile_info.g.dart';

ProfileInfo profileInfoFromJson(String str) =>
    ProfileInfo.fromJson(json.decode(str));

String profileInfoToJson(ProfileInfo data) => json.encode(data.toJson());

@freezed
class ProfileInfo with _$ProfileInfo {
  const factory ProfileInfo({
    @JsonKey(name: "code") int? code,
    @J<PERSON><PERSON><PERSON>(name: "data") InfoData? infoData,
    @JsonKey(name: "msg") String? msg,
  }) = _ProfileInfo;

  factory ProfileInfo.fromJson(Map<String, dynamic> json) =>
      _$ProfileInfoFromJson(json);
}

@freezed
class InfoData with _$InfoData {
  const factory InfoData({
    @Json<PERSON>ey(name: "expendAddresses") List<ExpendAddress>? expendAddresses,
    @Json<PERSON>ey(name: "invitationCode") String? invitationCode,
    @JsonKey(name: "userId") int? userId,
    @JsonKey(name: "userLevel") int? userLevel,

  }) = _InfoData;

  factory InfoData.fromJson(Map<String, dynamic> json) =>
      _$InfoDataFromJson(json);
}

@freezed
class ExpendAddress with _$ExpendAddress {
  const factory ExpendAddress({
    @JsonKey(name: "address") String? address,
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "name") String? name,
    @JsonKey(name: "type") String? type,
  }) = _ExpendAddress;

  factory ExpendAddress.fromJson(Map<String, dynamic> json) =>
      _$ExpendAddressFromJson(json);
}
