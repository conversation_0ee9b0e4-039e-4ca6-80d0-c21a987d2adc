// To parse this JSON data, do
//
//     final addWalletModel = addWalletModelFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'add_wallet_model.freezed.dart';
part 'add_wallet_model.g.dart';

AddWalletModel addWalletModelFromJson(String str) =>
    AddWalletModel.fromJson(json.decode(str));

String addWalletModelToJson(AddWalletModel data) => json.encode(data.toJson());

@freezed
class AddWalletModel with _$AddWalletModel {
  const factory AddWalletModel({
    @JsonKey(name: "code") int? code,
    @JsonKey(name: "data") List<Datum>? data,
    @JsonKey(name: "msg") String? msg,
  }) = _AddWalletModel;

  factory AddWalletModel.fromJson(Map<String, dynamic> json) =>
      _$AddWalletModelFromJson(json);
}

@freezed
class Datum with _$Datum {
  const factory Datum({
    @JsonKey(name: "address") String? address,
    @JsonKey(name: "id") int? id,
    @JsonKey(name: "name") String? name,
    @JsonKey(name: "type") String? type,
  }) = _Datum;

  factory Datum.fromJson(Map<String, dynamic> json) => _$DatumFromJson(json);
}
