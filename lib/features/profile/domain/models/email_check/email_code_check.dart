import 'dart:convert';

class EmailCodeCheck {
  int code;
  EmailCheckData data;
  String msg;

  EmailCodeCheck({
    required this.code,
    required this.data,
    required this.msg,
  });

  factory EmailCodeCheck.fromRawJson(String str) =>
      EmailCodeCheck.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory EmailCodeCheck.fromJson(Map<String, dynamic> json) => EmailCodeCheck(
        code: json["code"],
        data: EmailCheckData.fromJson(json["data"]),
        msg: json["msg"],
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "data": data.toJson(),
        "msg": msg,
      };
}

class EmailCheckData {
  String nonce;

  EmailCheckData({
    required this.nonce,
  });

  factory EmailCheckData.fromRawJson(String str) =>
      EmailCheckData.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory EmailCheckData.fromJson(Map<String, dynamic> json) => EmailCheckData(
        nonce: json["nonce"],
      );

  Map<String, dynamic> toJson() => {
        "nonce": nonce,
      };
}
