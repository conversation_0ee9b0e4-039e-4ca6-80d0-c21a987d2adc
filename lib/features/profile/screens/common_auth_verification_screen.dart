import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/api/network/network_helper.dart';
import 'package:sf_app_v2/core/api/network/route_arguments/change_wallet_password_arguments.dart';
import 'package:sf_app_v2/core/common_function.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/common_appbar.dart';
import 'package:sf_app_v2/core/widgets/common_pin_field.dart';
import 'package:sf_app_v2/core/widgets/custom_alert_dialog.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';
import 'package:sf_app_v2/features/profile/domain/models/email_check/email_code_check.dart';
import 'package:sf_app_v2/features/profile/logic/profile/profile_cubit.dart';
import '../../../core/constants/assets.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/routes/routes.dart';
import '../../../core/shared/logic/app_data/app_data_cubit.dart';
import '../../../core/widgets/timer_widget.dart';
import '../../auth/forgot/logic/forgot/forgot_cubit.dart';

class CommonAuthVerificationScreen extends StatefulWidget {
  final String routeName;

  const CommonAuthVerificationScreen({super.key, required this.routeName});

  @override
  State<CommonAuthVerificationScreen> createState() =>
      _CommonAuthVerificationScreenState();
}

class _CommonAuthVerificationScreenState
    extends State<CommonAuthVerificationScreen> {
  final pinController = TextEditingController();
  final _formGlobalKey = GlobalKey<FormState>();

  checkCode(BuildContext context, String code) {
    FocusScope.of(context).unfocus();
    if (_formGlobalKey.currentState!.validate()) {
      context.read<ProfileCubit>().checkEmailCode(code: code);
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) =>
          context.read<ProfileCubit>().resetTimer(),
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 45.0.h),
                child: CommonAppBarWidget(
                  buildContext: context,
                  enableNavBack: true,
                  onBackPressed: () {
                    context.read<ProfileCubit>().resetTimer();
                    Navigator.pop(context);
                  },
                ),
              ),
              67.verticalSpace,
              Center(
                child: SvgPicture.asset(
                  Assets.logoSvg,
                  width: 108.w,
                  height: 112.h,
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 43.0.w),
                child: Form(
                  key: _formGlobalKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      92.verticalSpace,
                      Center(
                        child: Text(
                          widget.routeName == routeChangePasswordScreen
                              ? StringConstants.changeWalletPassword.tr()
                              : StringConstants.changeLoginPassword.tr(),
                          style: FontPalette.semiBold20,
                        ),
                      ),
                      39.verticalSpace,
                      BlocSelector<ProfileCubit, ProfileState,
                          (DataStatus, EmailCodeCheck?, bool?, int?)>(
                        selector: (state) => (
                          state.emailSendStatus,
                          state.emailData,
                          state.showTimer,
                          state.timer,
                        ),
                        builder: (context, state) {
                          return state.$3 ?? false
                              ? CustomButton(
                                  isEnabled: state.$1 != DataStatus.loading,
                                  isLoading: state.$1 == DataStatus.loading,
                                  label: '',
                                  btnTextStyle: FontPalette.medium14.copyWith(
                                    color: ColorPalette.primaryVar1,
                                  ),
                                  width: 356.w,
                                  height: 52.h,
                                  borderRadiusUser: 26,
                                  isOutlined: true,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      TimerWidget(
                                        seconds: state.$4,
                                        onEnd: () {
                                          context
                                              .read<ProfileCubit>()
                                              .manageTimer();
                                        },
                                      ),
                                      4.horizontalSpace,
                                      Text(
                                        StringConstants.secondsUpper.tr(),
                                        style: FontPalette.medium14.copyWith(
                                          color: myColorScheme(context)
                                                  .primaryColor ??
                                              ColorPalette.primaryVar1,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : CustomButton(
                                  isEnabled: state.$1 != DataStatus.loading,
                                  isLoading: state.$1 == DataStatus.loading,
                                  onPressed: () {
                                    if (state.$1 == DataStatus.loading) {
                                      return;
                                    } else {
                                      context
                                          .read<ProfileCubit>()
                                          .sendEmailCode();
                                    }
                                    context
                                        .read<ProfileCubit>()
                                        .setIsSentOnce(isSentOnce: true);
                                  },
                                  label: StringConstants.sendCode.tr(),
                                  btnTextStyle: FontPalette.medium14,
                                  width: 356.w,
                                  height: 52.h,
                                  borderRadiusUser: 26,
                                );
                        },
                      ),
                      25.verticalSpace,
                      Text(
                        StringConstants.enterCode.tr(),
                        style: FontPalette.normal14,
                      ),
                      10.verticalSpace,
                      CommonPinFiledText(
                        obscureText: false,
                        type: 'code',
                        controller: pinController,
                      ),
                      45.verticalSpace,
                      MultiBlocListener(
                        listeners: [
                          BlocListener<ProfileCubit, ProfileState>(
                            listenWhen: (previous, current) =>
                                previous.emailCheckStatus !=
                                current.emailCheckStatus,
                            listener: (context, state) {
                              String email = context
                                      .read<AppDataCubit>()
                                      .state
                                      .userData
                                      ?.email ??
                                  '';
                              if (state.emailCheckStatus ==
                                  DataStatus.success) {
                                if (widget.routeName == routeResetPassword) {
                                  context.read<ForgotCubit>().setAccount(email);
                                  context
                                      .read<ForgotCubit>()
                                      .setNonceConfirmReset(
                                        state.checkData?.nonce ?? '',
                                      );
                                  Navigator.pushNamed(
                                    context,
                                    routeResetPassword,
                                  );
                                } else {
                                  Navigator.pushNamed(
                                    context,
                                    widget.routeName,
                                    arguments: ChangeSettingsArguments(
                                      nonce: state.checkData?.nonce ?? '',
                                      emailCaptcha: pinController.text,
                                    ),
                                  );
                                }
                              }
                              if (state.emailCheckStatus == DataStatus.failed) {
                                pinController.clear();
                                NetworkHelper.handleMessage(
                                  state.error,
                                  context,
                                  type: HandleTypes.customDialog,
                                  snackBarType: SnackBarType.error,
                                );
                              }
                            },
                          ),
                          BlocListener<ProfileCubit, ProfileState>(
                            listenWhen: (previous, current) =>
                                previous.emailSendStatus !=
                                current.emailSendStatus,
                            listener: (context, state) {
                              if (state.emailSendStatus == DataStatus.success) {
                                CommonFunctions.showDialogPopUp(
                                  context,
                                  CustomAlertDialog(
                                    message: StringConstants.sendCodeAlert.tr(),
                                    actionButtonText: StringConstants.ok.tr(),
                                    buttonBackGroundColor:
                                        myColorScheme(context).primaryColor ??
                                            ColorPalette.primaryVar1,
                                    onActionButtonPressed: () async {
                                      Navigator.pop(context);
                                    },
                                    headerImage: Assets.alertSuccess,
                                    isLoading: false,
                                    messageTextStyle:
                                        FontPalette.semiBold20.copyWith(
                                      color:
                                          myColorScheme(context).primaryColor ??
                                              ColorPalette.primaryColor,
                                    ),
                                  ),
                                  barrierDismissible: false,
                                );
                              }
                              if (state.emailCheckStatus == DataStatus.failed) {
                                pinController.clear();
                                NetworkHelper.handleMessage(
                                  state.error,
                                  context,
                                  type: HandleTypes.snackbar,
                                  snackBarType: SnackBarType.error,
                                );
                              }
                            },
                          ),
                        ],
                        child: BlocSelector<ProfileCubit, ProfileState,
                            (bool, DataStatus)>(
                          selector: (state) =>
                              (state.isSentOnce, state.emailCheckStatus),
                          builder: (context, value) {
                            return CustomButton(
                              isEnabled: value.$1,
                              isLoading: value.$2 == DataStatus.loading,
                              onPressed: () =>
                                  checkCode(context, pinController.text),
                              label: StringConstants.submit.tr(),
                              btnTextStyle: FontPalette.medium18,
                              width: 356.w,
                              height: 52.h,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
