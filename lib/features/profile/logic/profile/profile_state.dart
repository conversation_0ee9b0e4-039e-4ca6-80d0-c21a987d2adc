part of 'profile_cubit.dart';

class ProfileState extends Equatable {
  final InfoData? infoData;
  final EmailCodeCheck? emailData;
  final EmailCheckData? checkData;
  final AddWalletModel? addWalletData;
  final String? firebaseShortUrl;
  final bool onCopiedCode;
  final bool onCopiedInvitation;
  final bool isSentOnce;
  final bool showTimer;
  final int timer;

  final DataStatus infoFetchStatus;
  final DataStatus addWalletFetchStatus;
  final DataStatus deleteWalletFetchStatus;
  final DataStatus emailSendStatus;
  final DataStatus emailCheckStatus;
  final DataStatus passChangeStatus;
  final DataStatus loginPasswordChangeStatus;
  final String? error;

  const ProfileState({
    this.infoData,
    this.emailData,
    this.checkData,
    this.addWalletData,
    this.firebaseShortUrl,
    this.onCopiedCode = false,
    this.onCopiedInvitation = false,
    this.isSentOnce = false,
    this.showTimer = false,
    this.timer = 0,
    this.infoFetchStatus = DataStatus.idle,
    this.addWalletFetchStatus = DataStatus.idle,
    this.deleteWalletFetchStatus = DataStatus.idle,
    this.emailSendStatus = DataStatus.idle,
    this.emailCheckStatus = DataStatus.idle,
    this.passChangeStatus = DataStatus.idle,
    this.loginPasswordChangeStatus = DataStatus.idle,
    this.error,
  });

  @override
  List<Object?> get props => [
        infoData,
        emailData,
        checkData,
        addWalletData,
        firebaseShortUrl,
        onCopiedCode,
        onCopiedInvitation,
        isSentOnce,
        showTimer,
        timer,
        infoFetchStatus,
        addWalletFetchStatus,
        deleteWalletFetchStatus,
        emailSendStatus,
        emailCheckStatus,
        passChangeStatus,
        loginPasswordChangeStatus,
        error,
      ];

  ProfileState copyWith({
    InfoData? infoData,
    EmailCodeCheck? emailData,
    EmailCheckData? checkData,
    AddWalletModel? addWalletData,
    String? firebaseShortUrl,
    bool? onCopiedCode,
    bool? onCopiedInvitation,
    bool? isSentOnce,
    bool? showTimer,
    int? timer,
    DataStatus? infoFetchStatus,
    DataStatus? addWalletFetchStatus,
    DataStatus? deleteWalletFetchStatus,
    DataStatus? emailSendStatus,
    DataStatus? emailCheckStatus,
    DataStatus? passChangeStatus,
    DataStatus? loginPasswordChangeStatus,
    String? error,
  }) {
    return ProfileState(
      infoData: infoData ?? this.infoData,
      emailData: emailData ?? this.emailData,
      checkData: checkData ?? this.checkData,
      addWalletData: addWalletData ?? this.addWalletData,
      firebaseShortUrl: firebaseShortUrl ?? this.firebaseShortUrl,
      onCopiedCode: onCopiedCode ?? this.onCopiedCode,
      onCopiedInvitation: onCopiedInvitation ?? this.onCopiedInvitation,
      isSentOnce: isSentOnce ?? this.isSentOnce,
      showTimer: showTimer ?? this.showTimer,
      timer: timer ?? this.timer,
      infoFetchStatus: infoFetchStatus ?? this.infoFetchStatus,
      addWalletFetchStatus: addWalletFetchStatus ?? this.addWalletFetchStatus,
      deleteWalletFetchStatus: deleteWalletFetchStatus ?? this.deleteWalletFetchStatus,
      emailSendStatus: emailSendStatus ?? this.emailSendStatus,
      emailCheckStatus: emailCheckStatus ?? this.emailCheckStatus,
      passChangeStatus: passChangeStatus ?? this.passChangeStatus,
      loginPasswordChangeStatus:
          loginPasswordChangeStatus ?? this.loginPasswordChangeStatus,
      error: error ?? this.error,
    );
  }
}
