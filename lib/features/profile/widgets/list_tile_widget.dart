import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

import '../../../core/constants/assets.dart';
import '../../../core/theme/font_pallette.dart';

class ListTileWidget extends StatelessWidget {
  final String title;
  final TextStyle? titleStyle;
  final Widget? suffix;
  final Widget? preffix;
  final Function() onPressed;

  const ListTileWidget({
    super.key,
    required this.title,
    required this.onPressed,
    this.suffix,
    this.preffix,
    this.titleStyle,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        height: 45.h,
        width: double.infinity,
        color: myColorScheme(context).greyColor1,
        child: Padding(
          padding: EdgeInsets.only(top: 14.0.h, bottom: 14.h, left: 32.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  preffix ?? const SizedBox.shrink(),
                  10.horizontalSpace,
                  Text(
                    title,
                    style: titleStyle ??
                        FontPalette.normal12.copyWith(color: myColorScheme(context).greyColor4),
                  ),
                ],
              ),
              suffix ??
                  Padding(
                    padding: const EdgeInsets.only(right: 36.0),
                    child: SvgPicture.asset(Assets.arrowForward),
                  ),
            ],
          ),
        ),
      ),
    );
  }
}