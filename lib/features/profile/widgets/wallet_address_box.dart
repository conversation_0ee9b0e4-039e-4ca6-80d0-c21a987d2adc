import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/common_empty_data.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';
import 'package:sf_app_v2/features/profile/domain/models/profile/profile_info.dart';

import '../../../core/constants/enums.dart';
import '../../../core/theme/font_pallette.dart';
import '../logic/profile/profile_cubit.dart';

class WalletAddressBox extends StatelessWidget {
  final void Function()? onPressed;
  final void Function(int addressId)? onDeletePressed;
  final bool? isLoading;
  final List<ExpendAddress>? items;

  const WalletAddressBox({
    super.key,
    this.onPressed,
    this.onDeletePressed,
    required this.items,
    this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    List<TableRow> getRows(List<ExpendAddress> l) {
      List<TableRow> rows = [];
      for (int i = 0; i < l.length; ++i) {
        rows.add(
          TableRow(
            children: <Widget>[
              SizedBox(
                height: 50.h,
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(10, 0, 0, 0).r,
                    child: Text(
                      l[i].name ?? '',
                      style: FontPalette.normal10.copyWith(
                        height: 1,
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 50.h,
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(10, 0, 0, 0).r,
                    child: Text(
                      l[i].type ?? '',
                      style: FontPalette.normal10,
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 50.h,
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(10, 0, 0, 0).r,
                    child: Text(
                      l[i].address ?? '',
                      style: FontPalette.normal10,
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 50.h,
                child: Align(
                  alignment: Alignment.center,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(10, 0, 10, 0).r,
                    child: BlocListener<ProfileCubit, ProfileState>(
                      listenWhen: (previous, current) =>
                          previous.deleteWalletFetchStatus !=
                          current.deleteWalletFetchStatus,
                      listener: (context, state) {
                        if (state.deleteWalletFetchStatus ==
                            DataStatus.success) {
                          context.read<ProfileCubit>().getProfileInfo();
                        }
                      },
                      child: Bounceable(
                        onTap: l[i].id != null
                            ? () => _showDeleteConfirmation(context, l[i].id!)
                            : null,
                        child: Container(
                          padding: EdgeInsets.all(8.r),
                          decoration: BoxDecoration(
                            color: myColorScheme(context).primaryColor?.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(5.r),
                          ),
                          child: Icon(
                            Icons.delete_outline,
                            color: Colors.red,
                            size: 20.w,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }
      return rows;
    }

    List<TableRow> getShimmerRows() {
      List<TableRow> shimmerRows = [];
      for (int i = 0; i < 3; ++i) {
        shimmerRows.add(
          TableRow(
            children: <Widget>[
              SizedBox(
                height: 50.h,
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(10, 0, 0, 0).r,
                    child: CommonShimmer(
                      width: 60.w,
                      height: 15.h,
                      br: 4.r,
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 50.h,
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(10, 0, 0, 0).r,
                    child: CommonShimmer(
                      width: 40.w,
                      height: 15.h,
                      br: 4.r,
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 50.h,
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(10, 0, 0, 0).r,
                    child: CommonShimmer(
                      width: 120.w,
                      height: 15.h,
                      br: 4.r,
                    ),
                  ),
                ),
              ),
              SizedBox(
                height: 50.h,
                child: Align(
                  alignment: Alignment.center,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(10, 0, 10, 0).r,
                    child: CommonShimmer(
                      width: 20.w,
                      height: 20.h,
                      br: 10.r,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }
      return shimmerRows;
    }

    return Stack(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 10.0.h),
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                width: 1.w,
                color: myColorScheme(context).textFieldBorderColor ??
                    ColorPalette.primaryColor,
              ),
              borderRadius: BorderRadius.circular(5.r),
            ),
            child: Column(
              children: [
                Table(
                  border: const TableBorder(
                    horizontalInside: BorderSide(
                      color: Colors.black12,
                      width: 1,
                    ),
                  ),
                  columnWidths: <int, TableColumnWidth>{
                    0: FixedColumnWidth(1.sw / 6),
                    1: FixedColumnWidth(1.sw / 8),
                    2: FixedColumnWidth(1.sw / 2.5),
                    3: FixedColumnWidth(1.sw / 8),
                  },
                  defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                  children: <TableRow>[
                    TableRow(
                      children: <Widget>[
                        Padding(
                          padding: const EdgeInsets.only(top: 8).h,
                          child: SizedBox(
                            height: 50.h,
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  10,
                                  0,
                                  0,
                                  0,
                                ).r,
                                child: Text(
                                  StringConstants.name.tr(),
                                  style: FontPalette.medium11,
                                ),
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 8).h,
                          child: SizedBox(
                            height: 50.h,
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  10,
                                  0,
                                  0,
                                  0,
                                ).r,
                                child: Text(
                                  StringConstants.type.tr(),
                                  style: FontPalette.medium11,
                                ),
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 8).h,
                          child: SizedBox(
                            height: 50.h,
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  10,
                                  0,
                                  0,
                                  0,
                                ).r,
                                child: Text(
                                  StringConstants.address.tr(),
                                  style: FontPalette.medium11,
                                ),
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 8).h,
                          child: SizedBox(
                            height: 50.h,
                            child: Align(
                              alignment: Alignment.center,
                              child: Padding(
                                padding: const EdgeInsets.fromLTRB(
                                  10,
                                  0,
                                  10,
                                  0,
                                ).r,
                                child: const SizedBox.shrink(),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    ...(isLoading ?? false)
                        ? getShimmerRows()
                        : getRows(items ?? []),
                  ],
                ),
                Visibility(
                  visible: !(isLoading ?? false) && (items?.isEmpty ?? true),
                  child: const CommonEmpty(
                    topPadding: 0,
                  ),
                ),
                (items?.length ?? 0) >= 6
                    ? const SizedBox(
                        width: 400,
                      )
                    : Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: WithdrawAddressButton(
                          width: 400,
                          borderRadiusUser: 6.w,
                          height: 40.h,
                          label: StringConstants.addWithdrawal.tr(),
                          onPressed: onPressed ?? () {},
                          btnTextStyle: FontPalette.semiBold14
                              .copyWith(color: Colors.white),
                        ),
                      ),
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(10, 0, 0, 0).r,
          child: Container(
            color: myColorScheme(context).backgroundColor,
            child: Padding(
              padding: const EdgeInsets.fromLTRB(5, 0, 5, 0).r,
              child: Text(
                StringConstants.withdrawAddresses.tr(),
                style: FontPalette.normal14.copyWith(
                  color: myColorScheme(context).textFieldBorderColor ??
                      ColorPalette.primaryColor,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showDeleteConfirmation(BuildContext context, int addressId) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(StringConstants.confirmDelete.tr()),
          content: Text(StringConstants.deleteAddressConfirmation.tr()),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(StringConstants.cancel.tr()),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                if (onDeletePressed != null) {
                  onDeletePressed!(addressId);
                }
              },
              child: Text(
                StringConstants.deleteAddress.tr(),
                style: const TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}

class WithdrawAddressButton extends StatelessWidget {
  final void Function()? onPressed;
  final double width;
  final double height;
  final String label;
  final bool? isLoading;
  final double? borderRadiusUser;
  final TextStyle? fontStyle;
  final ButtonStyle? buttonStyle;
  final TextStyle? textStyle;
  final TextStyle? btnTextStyle;
  final bool? isOutlined;
  final String? icon;
  final Color? backgroundColor;

  const WithdrawAddressButton({
    super.key,
    this.onPressed,
    required this.width,
    required this.height,
    required this.label,
    this.isLoading,
    this.borderRadiusUser,
    this.fontStyle,
    this.buttonStyle,
    this.btnTextStyle,
    this.isOutlined,
    this.backgroundColor,
    this.icon,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width,
      child: ElevatedButton(
        onPressed: onPressed ?? () {},
        style: ElevatedButton.styleFrom(
          side: (isOutlined ?? false)
              ? BorderSide(
                  width: 1.r,
                  color: ColorPalette.primaryColor,
                )
              : null,
          backgroundColor: (isOutlined ?? false)
              ? Colors.transparent
              : (backgroundColor ?? myColorScheme(context).primaryColor),
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
              (borderRadiusUser?.r ?? 30.r),
            ), // <-- Radius
          ),
          shadowColor: Colors.transparent,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: 10.h),
              child: (isOutlined ?? false)
                  ? Text(
                      label,
                      style: btnTextStyle ??
                          FontPalette.bold14.copyWith(color: Colors.black),
                    )
                  : Text(
                      label,
                      style: btnTextStyle ?? FontPalette.bold14,
                    ),
            ),
            if (icon != null)
              Padding(
                padding: const EdgeInsets.fromLTRB(8, 0, 0, 0).r,
                child: SizedBox(width: 14.w, child: SvgPicture.asset(icon!)),
              ),
          ],
        ),
      ),
    );
  }
}
