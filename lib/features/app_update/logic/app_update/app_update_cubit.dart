import 'dart:async';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/constants/string_constants.dart';
import '../../../../core/constants/enums.dart';
import '../../../../core/utils/log.dart';
import '../../domain/models/app_update_model.dart';
import '../../domain/repository/app_update_repository.dart';
import '../../../../core/services/app_update/app_update_dialog_service.dart';
import '../../../../core/services/app_update/app_download_service.dart';

part 'app_update_state.dart';

@injectable
class AppUpdateCubit extends Cubit<AppUpdateState> {
  final AppUpdateRepository _appUpdateRepository;
  final AppDownloadService _downloadService;
  StreamSubscription? _downloadSubscription;

  AppUpdateCubit(this._appUpdateRepository, this._downloadService)
      : super(const AppUpdateState());

  Future<void> checkAppUpdate() async {
    if (state.isCheckAppUpdateDone) return;

    try {
      final platform = Platform.isAndroid ? 'android' : 'ios';
      final result = await _appUpdateRepository.appUpdate(platform: platform);

      emit(state.copyWith(isCheckAppUpdateDone: true));

      if (result?.data != null) {
        final versionName = result!.data!.data?.versionName;
        if (versionName != null) {
          bool shouldShowUpdateDialog =
              await AppUpdateDialogService().shouldUpdate(versionName);

          if (shouldShowUpdateDialog) {
            emit(state.copyWith(
              updateInfo: result.data,
              appUpdateFetchStatus: DataStatus.success,
              shouldShowUpdateDialog: true,
            ));
          }
        }
      }
    } catch (e) {
      logDev("AppUpdateCubit.checkAppUpdate.Error: $e", 'VERSION_CHECK',
          error: true);
    }
  }

  /// Start download and installation process
  Future<void> startUpdate() async {
    if (Platform.isIOS) {
      await _iosUpdate();
    } else {
      final downloadUrl = state.updateInfo?.data?.fileUrl;
      if (downloadUrl == null || downloadUrl.isEmpty) {
        emit(state.copyWith(
          updatePhase: UpdatePhase.downloadFailed,
          downloadError: StringConstants.downloadUrlNotAvailable,
        ));
        return;
      }

      emit(state.copyWith(
        isUpdating: true,
        updatePhase: UpdatePhase.downloading,
        downloadProgress: 0.0,
        downloadError: null,
      ));

      _downloadSubscription?.cancel();
      _downloadSubscription =
          _downloadService.downloadAndInstallUpdate(downloadUrl).listen(
        (progress) {
          switch (progress.status) {
            case DownloadStatus.downloading:
              emit(state.copyWith(
                updatePhase: UpdatePhase.downloading,
                downloadProgress: progress.progress,
              ));
              break;
            case DownloadStatus.downloadComplete:
              emit(state.copyWith(
                updatePhase: UpdatePhase.downloadComplete,
                downloadProgress: 1.0,
              ));
              break;
            case DownloadStatus.installing:
              emit(state.copyWith(
                updatePhase: UpdatePhase.installing,
                downloadProgress: 1.0,
              ));
              break;
            case DownloadStatus.installationComplete:
              emit(state.copyWith(
                updatePhase: UpdatePhase.installationComplete,
                downloadProgress: 1.0,
                isUpdating: false,
              ));
              break;
            case DownloadStatus.downloadFailed:
              emit(state.copyWith(
                updatePhase: UpdatePhase.downloadFailed,
                downloadError: progress.error,
                isUpdating: false,
              ));
              break;
            case DownloadStatus.installationFailed:
              emit(state.copyWith(
                updatePhase: UpdatePhase.installationFailed,
                downloadError: progress.error,
                isUpdating: false,
              ));
              break;
            case DownloadStatus.permissionDenied:
              emit(state.copyWith(
                updatePhase: UpdatePhase.permissionDenied,
                downloadError: progress.error,
                isUpdating: false,
              ));
              break;
            default:
              break;
          }
        },
        onError: (error) {
          logDev("AppUpdateCubit.startUpdate.Error: $error", 'APP_UPDATE',
              error: true);
          emit(state.copyWith(
            updatePhase: UpdatePhase.downloadFailed,
            downloadError: error.toString(),
            isUpdating: false,
          ));
        },
      );
    }
  }

  Future<void> _iosUpdate() async {
    final appStoreUrl = state.updateInfo?.data?.fileUrl;
    if (appStoreUrl == null || appStoreUrl.isEmpty) {
      emit(state.copyWith(
        updatePhase: UpdatePhase.downloadFailed,
        downloadError: StringConstants.downloadUrlNotAvailable,
      ));
      return;
    }

    try {
      await launchUrl(Uri.parse(appStoreUrl),
          mode: LaunchMode.externalApplication);
    } catch (e) {
      emit(state.copyWith(
        updatePhase: UpdatePhase.downloadFailed,
        downloadError: 'Failed to open App Store',
      ));
    }
    return;
  }

  /// Retry the update process
  void retryUpdate() {
    emit(state.copyWith(
      updatePhase: UpdatePhase.idle,
      downloadProgress: 0.0,
      downloadError: '',
      isUpdating: false,
    ));
  }

  /// Cancel the update process
  void cancelUpdate() {
    _downloadSubscription?.cancel();
    _downloadService.cancelDownload();
    emit(state.copyWith(
      updatePhase: UpdatePhase.idle,
      downloadProgress: 0.0,
      downloadError: '',
      isUpdating: false,
    ));
  }

  /// Reset update dialog state
  void resetUpdateDialog() {
    emit(state.copyWith(
      shouldShowUpdateDialog: false,
      updatePhase: UpdatePhase.idle,
      downloadProgress: 0.0,
      downloadError: '',
      isUpdating: false,
    ));
  }

  @override
  Future<void> close() {
    _downloadSubscription?.cancel();
    return super.close();
  }
}
