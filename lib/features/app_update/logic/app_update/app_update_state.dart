part of 'app_update_cubit.dart';

enum UpdatePhase {
  idle,
  downloading,
  downloadComplete,
  installing,
  installationComplete,
  downloadFailed,
  installationFailed,
  permissionDenied,
}

class AppUpdateState extends Equatable {
  final AppUpdateModel? updateInfo;
  final DataStatus appUpdateFetchStatus;
  final String? error;
  final bool isCheckAppUpdateDone;
  final bool shouldShowUpdateDialog;
  final UpdatePhase updatePhase;
  final double downloadProgress;
  final String? downloadError;
  final bool isUpdating;

  const AppUpdateState({
    this.updateInfo,
    this.appUpdateFetchStatus = DataStatus.idle,
    this.error,
    this.isCheckAppUpdateDone = false,
    this.shouldShowUpdateDialog = false,
    this.updatePhase = UpdatePhase.idle,
    this.downloadProgress = 0.0,
    this.downloadError,
    this.isUpdating = false,
  });

  @override
  List<Object?> get props => [
    updateInfo,
    appUpdateFetchStatus,
    error,
    isCheckAppUpdateDone,
    shouldShowUpdateDialog,
    updatePhase,
    downloadProgress,
    downloadError,
    isUpdating,
  ];

  AppUpdateState copyWith({
    AppUpdateModel? updateInfo,
    DataStatus? appUpdateFetchStatus,
    String? error,
    bool? isCheckAppUpdateDone,
    bool? shouldShowUpdateDialog,
    UpdatePhase? updatePhase,
    double? downloadProgress,
    String? downloadError,
    bool? isUpdating,
  }) => AppUpdateState(
    updateInfo: updateInfo ?? this.updateInfo,
    appUpdateFetchStatus: appUpdateFetchStatus ?? this.appUpdateFetchStatus,
    error: error ?? this.error,
    isCheckAppUpdateDone: isCheckAppUpdateDone ?? this.isCheckAppUpdateDone,
    shouldShowUpdateDialog: shouldShowUpdateDialog ?? this.shouldShowUpdateDialog,
    updatePhase: updatePhase ?? this.updatePhase,
    downloadProgress: downloadProgress ?? this.downloadProgress,
    downloadError: downloadError ?? this.downloadError,
    isUpdating: isUpdating ?? this.isUpdating,
  );
}
