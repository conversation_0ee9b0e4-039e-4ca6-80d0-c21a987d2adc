import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:sf_app_v2/features/app_update/domain/models/app_update_model.dart';
import 'package:sf_app_v2/features/app_update/logic/app_update/app_update_cubit.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';

class AppUpdateOverlay {
  static OverlayEntry? _overlayEntry;
  static bool _isShowing = false;

  static void show(
    BuildContext context, {
    required AppUpdateModel updateInfo,
    required AppUpdateCubit cubit,
    VoidCallback? onUpdatePressed,
    VoidCallback? onCancelPressed,
  }) {
    if (_isShowing) return;

    _isShowing = true;
    _overlayEntry = OverlayEntry(
      builder: (context) => UpdateOverlayWidget(
        updateInfo: updateInfo,
        cubit: cubit,
        onUpdatePressed: onUpdatePressed,
        onCancelPressed: onCancelPressed,
        onDismiss: () => hide(),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  static void hide() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _isShowing = false;
    }
  }
}

class UpdateOverlayWidget extends StatefulWidget {
  const UpdateOverlayWidget({
    super.key,
    required this.updateInfo,
    required this.cubit,
    this.onUpdatePressed,
    this.onCancelPressed,
    this.onDismiss,
  });

  final AppUpdateModel updateInfo;
  final AppUpdateCubit cubit;
  final VoidCallback? onUpdatePressed;
  final VoidCallback? onCancelPressed;
  final VoidCallback? onDismiss;

  String get title => StringConstants.appUpdateAvailable.tr();
  String get version =>
      updateInfo.data?.versionName ?? StringConstants.unknownVersion.tr();
  String get changelogItems => updateInfo.data?.updateContent ?? '';

  bool get isForceUpdate => updateInfo.data?.forceUpdate ?? false;

  @override
  State<UpdateOverlayWidget> createState() => _UpdateOverlayWidgetState();
}

class _UpdateOverlayWidgetState extends State<UpdateOverlayWidget> {
  String _currentVersion = '1.0.0';
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _getCurrentVersion();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _getCurrentVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      if (mounted) {
        setState(() {
          _currentVersion = packageInfo.version;
        });
      }
    } catch (e) {
      // Keep default version if error
    }
  }

  void _handleDismiss() {
    widget.onDismiss?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: BlocBuilder<AppUpdateCubit, AppUpdateState>(
        bloc: widget.cubit,
        builder: (context, state) {
          return Container(
            color: Colors.black.withOpacity(0.3),
            child: Center(
              child: Container(
                margin: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top + 20.h,
                  left: 20.w,
                  right: 20.w,
                ),
                child: _buildOverlayContent(state),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildOverlayContent(AppUpdateState state) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24.r),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(state),
            _buildContent(),
            _buildProgress(state),
            _buildFooter(state),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(AppUpdateState state) {
    return Container(
      height: 130.h,
      width: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF002E6E),
            Color(0xFF1C6FE6),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Main content
          Positioned(
            left: 24.w,
            top: 24.h,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.title,
                  style: FontPalette.bold24.copyWith(
                    color: ColorPalette.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                SizedBox(height: 10.h),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    widget.version,
                    style: FontPalette.bold16.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Close button (hidden for force updates)
          if (!widget.isForceUpdate)
            Positioned(
              top: 20.h,
              right: 20.w,
              child: GestureDetector(
                onTap: _handleDismiss,
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.close,
                    size: 20.w,
                    color: myColorScheme(context).primaryColor,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Container(
      color: myColorScheme(context).cardColor,
      padding: EdgeInsets.fromLTRB(24.w, 10.h, 24.w, 20.h),
      child: Theme(
        data: ThemeData(
          scrollbarTheme: ScrollbarThemeData(
            thumbVisibility: MaterialStateProperty.all(true),
            thumbColor:
                MaterialStateProperty.all(myColorScheme(context).primaryColor),
            
            thickness: MaterialStateProperty.all(4.w),
            crossAxisMargin: 0,
            mainAxisMargin: 0,
          ),
        ),
        child: SizedBox(
          height: 160.h,
          width: double.infinity,
          child: Scrollbar(
            controller: _scrollController,
            thumbVisibility: true,
            scrollbarOrientation: ScrollbarOrientation.right,
            trackVisibility: true,
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Container(
                width: double.infinity,
                alignment: Alignment.centerLeft,
                child: Text(
                  widget.changelogItems,
                  style: FontPalette.medium14.copyWith(
                    color: myColorScheme(context).titleColor,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.left,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProgress(AppUpdateState state) {
    if (!state.isUpdating) return const SizedBox.shrink();
    return Container(
      color: myColorScheme(context).cardColor,
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${(state.downloadProgress * 100).toInt()}%',
                style: FontPalette.bold16.copyWith(
                  color: myColorScheme(context).primaryColor,
                ),
              ),
              Text(
                _getProgressText(state),
                style: FontPalette.medium14.copyWith(
                  color: myColorScheme(context).labelColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          LinearProgressIndicator(
            value: state.downloadProgress,
            backgroundColor: myColorScheme(context).greyColor1,
            valueColor: AlwaysStoppedAnimation<Color>(
              myColorScheme(context).primaryColor ?? ColorPalette.primaryColor,
            ),
            minHeight: 6.h,
          ),
          if (state.downloadError != null)
            Padding(
              padding: EdgeInsets.only(top: 8.h),
              child: Text(
                state.downloadError!,
                style: FontPalette.medium12.copyWith(
                  color: Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          SizedBox(height: 24.h),
        ],
      ),
    );
  }

  Widget _buildFooter(AppUpdateState state) {
    final isError = state.updatePhase == UpdatePhase.downloadFailed ||
        state.updatePhase == UpdatePhase.installationFailed ||
        state.updatePhase == UpdatePhase.permissionDenied;
    return Container(
      color: myColorScheme(context).cardColor,
      padding: EdgeInsets.all(24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(child: SizedBox()),
              _buildActionButtons(state),
            ],
          ),
          if (isError && state.downloadError != null) ...[
            SizedBox(height: 10.h),
            Text(
              state.downloadError!,
              style: FontPalette.medium12.copyWith(
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          SizedBox(height: 10.h),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              '${StringConstants.currentVersion.tr()}：$_currentVersion',
              style: FontPalette.medium12.copyWith(
                color: myColorScheme(context).labelColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getProgressText(AppUpdateState state) {
    switch (state.updatePhase) {
      case UpdatePhase.downloading:
        return StringConstants.downloading.tr();
      case UpdatePhase.downloadComplete:
        return StringConstants.downloadComplete.tr();
      case UpdatePhase.installing:
        return StringConstants.installing.tr();
      case UpdatePhase.installationComplete:
        return StringConstants.installationComplete.tr();
      case UpdatePhase.downloadFailed:
        return StringConstants.downloadFailed.tr();
      case UpdatePhase.installationFailed:
        return StringConstants.installationFailed.tr();
      case UpdatePhase.permissionDenied:
        return StringConstants.storagePermissionRequired.tr();
      default:
        return StringConstants.readyToUpdate.tr();
    }
  }

  Widget _buildActionButtons(AppUpdateState state) {
    final isUpdating = state.isUpdating;
    final isError = state.updatePhase == UpdatePhase.downloadFailed ||
        state.updatePhase == UpdatePhase.installationFailed ||
        state.updatePhase == UpdatePhase.permissionDenied;

    if (isUpdating) {
      return Row(
        children: [
          ElevatedButton(
            onPressed: () {
              widget.cubit.cancelUpdate();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                horizontal: 24.w,
                vertical: 12.h,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.r),
              ),
            ),
            child: Text(
              StringConstants.cancel.tr(),
              style: FontPalette.medium14.copyWith(
                color: Colors.white,
              ),
            ),
          ),
        ],
      );
    }

    if (isError) {
      return Row(
        children: [
          if (!widget.isForceUpdate)
            TextButton(
              onPressed: () {
                widget.onCancelPressed?.call();
                _handleDismiss();
              },
              child: Text(
                StringConstants.skipUpdate.tr(),
                style: FontPalette.medium14.copyWith(
                  color: myColorScheme(context).labelColor,
                ),
              ),
            ),
          if (!widget.isForceUpdate) SizedBox(width: 8.w),
          ElevatedButton(
            onPressed: () {
              widget.cubit.retryUpdate();
              widget.onUpdatePressed?.call();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: myColorScheme(context).primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                horizontal: 24.w,
                vertical: 12.h,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.r),
              ),
            ),
            child: Text(
              StringConstants.retryUpdate.tr(),
              style: FontPalette.medium14.copyWith(
                color: Colors.white,
              ),
            ),
          ),
        ],
      );
    }

    // Default state - show update button
    return Row(
      children: [
        if (!widget.isForceUpdate && widget.onCancelPressed != null)
          TextButton(
            onPressed: () {
              widget.onCancelPressed?.call();
              _handleDismiss();
            },
            child: Text(
              StringConstants.skipUpdate.tr(),
              style: FontPalette.medium14.copyWith(
                color: myColorScheme(context).labelColor,
              ),
            ),
          ),
        if (!widget.isForceUpdate && widget.onCancelPressed != null)
          SizedBox(width: 8.w),
        ElevatedButton(
          onPressed: () {
            widget.onUpdatePressed?.call();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: myColorScheme(context).primaryColor,
            foregroundColor: Colors.white,
            padding: EdgeInsets.symmetric(
              horizontal: 24.w,
              vertical: 12.h,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.r),
            ),
          ),
          child: Text(
            StringConstants.updateNow.tr(),
            style: FontPalette.medium14.copyWith(
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }
}
