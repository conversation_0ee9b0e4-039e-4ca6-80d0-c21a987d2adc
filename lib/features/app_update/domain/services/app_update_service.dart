import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/endpoint/api_endpoints.dart';
import '../../../../core/api/network/network.dart';
import '../../../../core/models/result.dart';
import '../models/app_update_model.dart';
import '../repository/app_update_repository.dart';

/// Service class that implements the AppUpdateRepository interface to handle app update related API calls
@Injectable(as: AppUpdateRepository)
class AppUpdateService implements AppUpdateRepository {
  /// Checks if an app update is available
  ///
  /// [platform] - The platform to check updates for (e.g. 'ios', 'android')
  /// Returns a [ResponseResult] containing [AppUpdateModel] with update info on success
  @override
  Future<ResponseResult<AppUpdateModel>?> appUpdate(
      {required String platform}) async {
    try {
      final Response response = await <PERSON><PERSON>rovider().get(
        ApiEndpoints.update(platform),
        isSigninRequired: false,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(
              data: AppUpdateModel.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get balance');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
