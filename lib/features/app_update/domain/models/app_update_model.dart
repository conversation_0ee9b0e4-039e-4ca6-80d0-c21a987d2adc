
// To parse this JSON data, do
//
//     final appUpdateModel = appUpdateModelFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'app_update_model.freezed.dart';
part 'app_update_model.g.dart';

AppUpdateModel appUpdateModelFromJson( str) => AppUpdateModel.fromJson((str));

String appUpdateModelToJson(AppUpdateModel data) => json.encode(data.toJson());

@freezed
class AppUpdateModel with _$AppUpdateModel {
    const factory AppUpdateModel({
        int? code,
        AppUpdateData? data,
        String? msg,
    }) = _AppUpdateModel;

    factory AppUpdateModel.fromJson(Map<String, dynamic> json) => _$AppUpdateModelFromJson(json);
}

@freezed
class AppUpdateData with _$AppUpdateData {
    const factory AppUpdateData({
        String? fileUrl,
        bool? forceUpdate,
        String? platform,
        int? status,
        String? updateContent,
        int? versionCode,
        String? versionName,
    }) = _AppUpdateData;

    factory AppUpdateData.fromJson(Map<String, dynamic> json) => _$AppUpdateDataFromJson(json);
}
