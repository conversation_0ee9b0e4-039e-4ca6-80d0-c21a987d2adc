import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/constants/string_constants.dart';
import '../../../core/theme/color_pallette.dart';
import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';

class NotificationActiveItem extends StatelessWidget {
  final double width;
  final double height;
  final String dateDay;
  final String dateMonth;
  final String notTitle;
  final String notDescription;

  const NotificationActiveItem({
    super.key,
    required this.width,
    required this.height,
    required this.dateDay,
    required this.dateMonth,
    required this.notTitle,
    required this.notDescription,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width.w,
      height: height.w,
      color: ColorPalette.greyColor1,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(8, 0, 0, 0),
            child: Container(
              decoration: BoxDecoration(
                boxShadow: const [
                  BoxShadow(
                    color: Colors.black38,
                  ),
                  BoxShadow(
                    color: Colors.white,
                    blurRadius: 12.0,
                  ),
                ],
                borderRadius: BorderRadius.only(
                  topRight: const Radius.circular(6).r,
                  topLeft: const Radius.circular(30).r,
                  bottomLeft: const Radius.circular(2).r,
                  bottomRight: const Radius.circular(6).r,
                ),
              ),
              width: 65.w,
              height: 65.w,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    height: 6.r,
                  ),
                  Text(
                    dateDay,
                    style: FontPalette.semiBold25
                        .copyWith(color: ColorPalette.greyColor4),
                  ),
                  Transform.translate(
                    offset: const Offset(0, -10),
                    child: Text(
                      dateMonth,
                      style: FontPalette.normal14
                          .copyWith(color: ColorPalette.greyColor4),
                    ),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            width: 16.w,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  notTitle,
                  style: FontPalette.medium14,
                ),
                SizedBox(
                  height: 6.w,
                ),
                Text(
                  notDescription,
                  style: FontPalette.normal8,
                ),
              ],
            ),
          ),
          SizedBox(
            width: 10.w,
          ),
          Column(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(10, 20, 10, 0).r,
                child: Container(
                  width: 10.w,
                  height: 10.w,
                  decoration: BoxDecoration(
                    color: ColorPalette.primaryColor,
                    borderRadius: BorderRadius.all(const Radius.circular(10).r),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class NotificationItem extends StatelessWidget {
  final double height;
  final String dateDay;
  final String dateMonth;
  final String notTitle;
  final int type;
  final bool isActive;
  final void Function()? onPressed;

  final String notDescription;

  const NotificationItem({
    super.key,
    required this.height,
    required this.dateDay,
    required this.dateMonth,
    required this.notTitle,
    required this.type,
    required this.notDescription,
    required this.onPressed,
    this.isActive = true,
  });

  Color tagColor(int type) {
    switch (type) {
      case 0:
        return ColorPalette.tagBlue;
      case 1:
        return ColorPalette.tagRed;
      case 2:
        return ColorPalette.tagGreen;
      default:
        return Colors.transparent;
    }
  }

  String tagTitle(int type) {
    switch (type) {
      case 0:
        return StringConstants.systemNotifications.tr();
      case 1:
        return StringConstants.maintenanceNotice.tr();
      case 2:
        return StringConstants.contractAnnouncement.tr();
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed ?? () {},
      child: Container(
        padding: EdgeInsets.only(bottom: 2.0.w),
        height: height.h,
        color: isActive
            ? myColorScheme(context).greyColor1
            : myColorScheme(context).backgroundColor,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.fromLTRB(8, 0, 0, 0),
              child: Container(
                height: 70.h,
                width: 62.w,
                decoration: BoxDecoration(
                  boxShadow: [
                    BoxShadow(
                      color: myColorScheme(context).cardColor ?? Colors.black12,
                    ),
                    BoxShadow(
                      color: myColorScheme(context).cardColor ?? Colors.white,
                      blurRadius: 12.0,
                    ),
                  ],
                  borderRadius: BorderRadius.only(
                    topRight: const Radius.circular(6).r,
                    topLeft: const Radius.circular(30).r,
                    bottomLeft: const Radius.circular(2).r,
                    bottomRight: const Radius.circular(6).r,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      dateDay,
                      style: FontPalette.semiBold25
                          .copyWith(color: ColorPalette.greyColor4),
                    ),
                    SizedBox(
                      height: 2.h,
                    ),
                    Transform.translate(
                      offset: const Offset(0, -10),
                      child: Text(
                        dateMonth,
                        style: FontPalette.normal14
                            .copyWith(color: ColorPalette.greyColor4),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              width: 16.w,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          notTitle,
                          style: FontPalette.medium14.copyWith(
                            color: isActive
                                ? myColorScheme(context).primaryColor
                                : myColorScheme(context).titleColor,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                      if (isActive)
                        CircleAvatar(
                          backgroundColor: myColorScheme(context).primaryColor,
                          radius: 4.5,
                        ),
                    ],
                  ),
                  SizedBox(
                    height: 6.h,
                  ),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5).r,
                      color: tagColor(type),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(10, 5, 10, 5).r,
                      child: Text(
                        tagTitle(type),
                        style: FontPalette.semiBold10
                            .copyWith(color: Colors.white),
                      ),
                    ),
                  ),
                  // Text(
                  //   ConvertHelper.parseHtmlString(notDescription),
                  //   style: FontPalette.normal9,
                  //   overflow: TextOverflow.ellipsis,
                  //   maxLines: 2,
                  // ),
                ],
              ),
            ),
            SizedBox(
              width: 10.w,
            ),
            Column(
              children: [
                Padding(
                  padding: const EdgeInsets.fromLTRB(10, 20, 10, 0).r,
                  child: Container(
                    width: 10.w,
                    height: 10.w,
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius:
                          BorderRadius.all(const Radius.circular(10).r),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
