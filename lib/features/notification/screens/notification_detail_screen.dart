import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';
import '../../../core/widgets/common_appbar.dart';
import '../logic/notification/notification_cubit.dart';

class NotificationDetailScreen extends StatefulWidget {
  final String appBarTitle;
  final String appBarContent;
  final int id;
  final bool haveRead;

  const NotificationDetailScreen({
    super.key,
    required this.appBarTitle,
    required this.appBarContent,
    required this.id,
    required this.haveRead,
  });

  @override
  State<NotificationDetailScreen> createState() =>
      _NotificationDetailScreenState();
}

class _NotificationDetailScreenState extends State<NotificationDetailScreen> {
  @override
  void initState() {
    super.initState();
    if (!widget.haveRead) {
      context.read<NotificationCubit>().markNotificationRead(widget.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        context.read<NotificationCubit>().getNotifications();
        return true;
      },
      child: Scaffold(
        body: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            CommonSliverAppBar(
              onBackPressed: () {
                Navigator.pop(context);
                context.read<NotificationCubit>().getNotifications();
              },
              buildContext: context,
              enableNavBack: true,
              titleWidget: Text(
                widget.appBarTitle,
                style: FontPalette.semiBold16
                    .copyWith(color: myColorScheme(context).titleColor),
              ),
              centerTitleText: true,
            ),
            SliverList(
              delegate: SliverChildListDelegate([
                20.verticalSpace,
                Container(
                  decoration: BoxDecoration(
                    color: myColorScheme(context).cardColor,
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(
                      top: 22.0.h,
                      left: 33.w,
                      bottom: 29,
                      right: 33.w,
                    ),
                    child: Html(
                      data: widget.appBarContent,
                      style: {
                        "body": Style(
                          fontSize: FontSize(14.r),
                        ),
                      },
                    ),
                  ),
                ),
              ]),
            ),
          ],
        ),
      ),
    );
  }
}
