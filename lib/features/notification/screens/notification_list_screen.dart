import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../core/api/network/route_arguments/notification_details_arguments.dart';
import '../../../core/common_function.dart';
import '../../../core/constants/enums.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/routes/routes.dart';
import '../../../core/theme/font_pallette.dart';
import '../../../core/theme/my_color_scheme.dart';
import '../../../core/utils/convert_helper.dart';
import '../../../core/widgets/common_appbar.dart';
import '../../../core/widgets/common_empty_data.dart';
import '../domain/models/notification_list/notification_model.dart';
import '../logic/notification/notification_cubit.dart';
import '../widgets/notification_list_widgets.dart';

class NotificationListScreen extends StatefulWidget {
  const NotificationListScreen({super.key});

  @override
  State<NotificationListScreen> createState() => _NotificationListScreenState();
}

class _NotificationListScreenState extends State<NotificationListScreen> {
  @override
  void initState() {
    super.initState();
    CommonFunctions.afterInit(_initialFunction);
  }

  _initialFunction() {
    context.read<NotificationCubit>().getNotifications();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: AnimationLimiter(
          child: RefreshIndicator(
            onRefresh: () async =>
                context.read<NotificationCubit>().getNotifications(),
            child: CustomScrollView(
              slivers: [
                CommonSliverAppBar(
                  buildContext: context,
                  enableNavBack: true,
                  titleWidget: Text(
                    StringConstants.notification.tr(),
                    style: FontPalette.semiBold25
                        .copyWith(color: myColorScheme(context).titleColor),
                  ),
                  centerTitleText: true,
                ),
                SliverList(
                  delegate: SliverChildListDelegate([
                    BlocSelector<NotificationCubit, NotificationState,
                        (DataStatus, Notifications?)>(
                      selector: (state) => (
                        state.notificationsFetchStatus,
                        state.notifications,
                      ),
                      builder: (context, data) {
                        switch (data.$1) {
                          case DataStatus.success:
                            if (data.$2 == null || data.$2!.data!.isEmpty) {
                              return const CommonEmpty();
                            }

                            List items = data.$2!.data!;

                            return ListView.builder(
                              physics: const BouncingScrollPhysics(),
                              itemCount: items.length,
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                NotificationData item = items[index];
                                return AnimationConfiguration.staggeredList(
                                  position: index,
                                  child: SlideAnimation(
                                    verticalOffset: index * 50.0,
                                    child: FadeInAnimation(
                                      child: NotificationItem(
                                        onPressed: () {
                                                
                                          Navigator.pushNamed(
                                            context,
                                            routeNotificationDetailScreen,
                                            arguments:
                                                NotificationDetailsArguments(
                                              id: item.id,
                                              title: item.title,
                                              content: item.content,
                                              haveRead: item.haveRead,
                                            ),
                                          );

                                          if (item.haveRead ?? false) {
                                          } else {
                                            null;
                                          }
                                        },
                                        isActive: !(item.haveRead ?? false),
                                        height: 96,
                                        dateDay: ConvertHelper.formatDay(
                                          item.startTime.toString(),
                                        ).toString(),
                                        dateMonth: ConvertHelper.formatMonth(
                                          item.startTime.toString(),
                                        ).toString(),
                                        notTitle: item.title ?? '',
                                        notDescription: item.content ?? '',
                                        type: item.type ?? 0,
                                      ),
                                    ),
                                  ),
                                );
                              },
                            );
                          case DataStatus.loading:
                            return Padding(
                              padding: const EdgeInsets.fromLTRB(0, 10, 0, 0).r,
                              child: SizedBox(
                                height: 1.sw,
                                child: const Center(
                                  child: CircularProgressIndicator.adaptive(),
                                ),
                              ),
                            );
                          default:
                            return SizedBox(
                              width: 0.2.sw,
                              height: 45.h,
                            );
                        }
                      },
                    ),
                  ]),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
