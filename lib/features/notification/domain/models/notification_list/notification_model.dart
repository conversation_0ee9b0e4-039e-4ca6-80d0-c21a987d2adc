import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'notification_model.freezed.dart';
part 'notification_model.g.dart';

@freezed
class Notifications with _$Notifications {
  const factory Notifications({
    int? code,
    List<NotificationData>? data,
    String? msg,
  }) = _Notifications;

  factory Notifications.fromJson(Map<String, dynamic> json) =>
      _$NotificationsFromJson(json);
}

@freezed
class NotificationData with _$NotificationData {
  const factory NotificationData({
    String? content,
    DateTime? endTime,
    bool? haveRead,
    int? id,
    DateTime? startTime,
    String? title,
    int? type,
  }) = _NotificationData;

  factory NotificationData.fromJson(Map<String, dynamic> json) =>
      _$NotificationDataFromJson(json);
}

// Optional: Keep these for backwards compatibility
Notifications notificationsFromJson(String str) =>
    Notifications.fromJson(json.decode(str));

String notificationsToJson(Notifications data) => json.encode(data.toJson());
