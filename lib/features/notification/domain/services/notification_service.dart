import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/endpoint/api_endpoints.dart';
import '../../../../core/api/network/network.dart';
import '../../../../core/models/result.dart';
import '../models/notification_list/notification_model.dart';
import '../repository/notification_repository.dart';

/// Service class that implements the NotificationRepository interface to handle notification-related API calls
@Injectable(as: NotificationRepository)
class NotificationService implements NotificationRepository {
  /// Fetches user notifications
  /// Returns a [ResponseResult] containing [Notifications] on success
  @override
  Future<ResponseResult<Notifications>> getNotifications() async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.notifications,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: Notifications.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get notifications');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Marks a notification as read
  /// [id] - ID of the notification to mark as read
  /// Returns a [ResponseResult] containing bool indicating success
  @override
  Future<ResponseResult<bool>> markNotificationRead(int id) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.haveRead,
        data: {'id': id},
         isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to mark notification as read');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
