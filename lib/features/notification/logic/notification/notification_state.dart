part of 'notification_cubit.dart';

class NotificationState extends Equatable {
  final bool? isUnreadAvailable;
  final Notifications? notifications;
  final DataStatus notificationsFetchStatus;
  final String? error;
  const NotificationState({
    this.isUnreadAvailable,
    this.notifications,
    this.notificationsFetchStatus = DataStatus.idle,
    this.error,
  });

  @override
  List<Object?> get props => [
        isUnreadAvailable,
        notifications,
        notificationsFetchStatus,
        error,
      ];
  NotificationState copyWith({
    bool? isUnreadAvailable,
    Notifications? notifications,
    DataStatus? notificationsFetchStatus,
    String? error,
  }) {
    return NotificationState(
      isUnreadAvailable: isUnreadAvailable ?? this.isUnreadAvailable,
      notifications: notifications ?? this.notifications,
      notificationsFetchStatus:
          notificationsFetchStatus ?? this.notificationsFetchStatus,
      error: error ?? this.error,
    );
  }
}
