import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';

import '../../domain/models/notification_list/notification_model.dart';
import '../../domain/repository/notification_repository.dart';

part 'notification_state.dart';

@injectable
class NotificationCubit extends Cubit<NotificationState> {
  final NotificationRepository _notificationService;

  NotificationCubit(this._notificationService)
      : super(const NotificationState());

  Future<void> getNotifications() async {
    emit(state.copyWith(notificationsFetchStatus: DataStatus.loading));
    try {
      final result = await _notificationService.getNotifications();
      if (result.data != null) {
        final sortedData = List<NotificationData>.from(result.data!.data!)
          ..sort(
            (a, b) => b.startTime.toString().compareTo(a.startTime.toString()),
          );

        emit(
          state.copyWith(
            notifications: Notifications(
              code: result.data!.code,
              data: sortedData,
              msg: result.data!.msg,
            ),
            notificationsFetchStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            notificationsFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          notificationsFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> markNotificationRead(int id) async {
    emit(state.copyWith(notificationsFetchStatus: DataStatus.loading));
    try {
      final result = await _notificationService.markNotificationRead(id);
      if (result.data != null) {
        emit(state.copyWith(notificationsFetchStatus: DataStatus.success));
      } else {
        emit(
          state.copyWith(
            notificationsFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          notificationsFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }
}
