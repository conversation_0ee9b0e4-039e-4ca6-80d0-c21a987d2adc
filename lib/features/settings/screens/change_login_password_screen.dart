import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';
import 'package:sf_app_v2/features/profile/logic/profile/profile_cubit.dart';

import '../../../core/common_function.dart';
import '../../../core/constants/assets.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/validator.dart';
import '../../../core/widgets/common_text_field.dart';
import '../../../core/widgets/custom_alert_dialog.dart';

class ChangeLoginPasswordScreen extends StatefulWidget {
  final String? nonce;

  const ChangeLoginPasswordScreen({super.key, this.nonce});

  @override
  State<ChangeLoginPasswordScreen> createState() =>
      _ChangeLoginPasswordScreenState();
}

class _ChangeLoginPasswordScreenState extends State<ChangeLoginPasswordScreen>
    with Validator {
  final passwordController = TextEditingController();

  final confirmPasswordController = TextEditingController();
  final formGlobalKey = GlobalKey<FormState>();
  bool isClosingPopup = false;

  void delayedPop({Function? function}) {
    if (isClosingPopup) return;
    isClosingPopup = true;
    if (function != null) function();
    Future.delayed(const Duration(milliseconds: 200), () {
      isClosingPopup = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onPanDown: (_) => FocusScope.of(context).requestFocus(FocusNode()),
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.arrow_back_ios),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 37.0.w),
                  child: Form(
                    key: formGlobalKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        70.verticalSpace,
                        Center(
                          child: SvgPicture.asset(
                            Assets.logoSvg,
                            width: 108.w,
                            height: 112.h,
                          ),
                        ),
                        60.verticalSpace,
                        Center(
                          child: Text(
                            StringConstants.changeLoginPassword.tr(),
                            style: FontPalette.semiBold20,
                          ),
                        ),
                        39.verticalSpace,
                        Text(
                          StringConstants.passwordHintText.tr(),
                          style: FontPalette.normal14.copyWith(
                            color: myColorScheme(context).titleColor,
                          ),
                        ),
                        16.verticalSpace,
                        CommonTextField(
                          enableObscure: true,
                          textInputType: TextInputType.text,
                          textInputAction: TextInputAction.next,
                          labelText: StringConstants.password.tr(),
                          hintText: StringConstants.passwordHintText.tr(),
                          controller: passwordController,
                          validator: (_) => validatePassword(
                            passwordController.text,
                            msg: StringConstants.atLeast8character.tr(),
                          ),
                          onChanged: (_) {},
                        ),
                        35.verticalSpace,
                        Text(
                          StringConstants.confirmHintText.tr(),
                          style: FontPalette.normal14.copyWith(
                            color: myColorScheme(context).titleColor,
                          ),
                        ),
                        16.verticalSpace,
                        CommonTextField(
                          enableObscure: true,
                          textInputType: TextInputType.text,
                          textInputAction: TextInputAction.next,
                          labelText: StringConstants.confirmHintText.tr(),
                          hintText: StringConstants.confirmHintText.tr(),
                          controller: confirmPasswordController,
                          validator: (value) => validateConfirmPassword(
                            passwordController.text,
                            confirmPasswordController.text,
                          ),
                          onChanged: (_) {},
                        ),
                        50.verticalSpace,
                        BlocListener<ProfileCubit, ProfileState>(
                          listener: (context, state) {
                            if (state.loginPasswordChangeStatus ==
                                DataStatus.success) {
                              CommonFunctions.showDialogPopUp(
                                context,
                                CustomAlertDialog(
                                  title: StringConstants
                                      .loginPasswordUpdatedToast
                                      .tr(),
                                  message: StringConstants.successfully.tr(),
                                  actionButtonText: StringConstants.ok.tr(),
                                  buttonBackGroundColor:
                                      myColorScheme(context).primaryColor,
                                  onActionButtonPressed: () {
                                    delayedPop(
                                      function: () {
                                        Navigator.pop(context);
                                        Navigator.pop(context);
                                        Navigator.pop(context);
                                      },
                                    );
                                  },
                                  headerImage: Assets.alertSuccess,
                                  isLoading: false,
                                  messageTextStyle: FontPalette.semiBold30
                                      .copyWith(fontSize: 29.sp),
                                ),
                                barrierDismissible: false,
                              );
                            }
                          },
                          child: BlocSelector<ProfileCubit, ProfileState,
                              DataStatus>(
                            selector: (state) =>
                                state.loginPasswordChangeStatus,
                            builder: (context, state) {
                              return CustomButton(
                                width: 356,
                                height: 52,
                                label: StringConstants.submit.tr(),
                                isOutlined: false,
                                isLoading: state == DataStatus.loading,
                                onPressed: () {
                                  if (formGlobalKey.currentState!.validate()) {
                                    context
                                        .read<ProfileCubit>()
                                        .changeLoginPassword(
                                          nonce: widget.nonce!,
                                          password: passwordController.text,
                                          confirmPassword:
                                              confirmPasswordController.text,
                                        );
                                  }
                                },
                              );
                            },
                          ),
                        ),
                        20.verticalSpace,
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
