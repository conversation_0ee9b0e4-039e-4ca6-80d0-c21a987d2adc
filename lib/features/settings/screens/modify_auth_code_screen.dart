import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/account_info/account_info_cubit.dart';
import 'package:sf_app_v2/features/auth/account_info/logic/google_authentication/google_authentication_cubit.dart';

import '../../../core/common_function.dart';
import '../../../core/constants/assets.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/utils/mixin/animation.dart';
import '../../../core/widgets/common_pin_field.dart';
import '../../../core/widgets/common_text_field.dart';
import '../../../core/widgets/custom_alert_dialog.dart';

class ModifyAuthCodeScreen extends StatefulWidget {
  final String? nonce;
  final String? emailCaptcha;

  const ModifyAuthCodeScreen({super.key, this.nonce, this.emailCaptcha});

  @override
  State<ModifyAuthCodeScreen> createState() => _ModifyAuthCodeScreenState();
}

class _ModifyAuthCodeScreenState extends State<ModifyAuthCodeScreen> with StaggeredAnimation {
  final googleCodeController = TextEditingController();
  final textController = TextEditingController();
  final _formGlobalKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    CommonFunctions.afterInit(_initialFunction);
  }

  _initialFunction() {
    context.read<GoogleAuthenticationCubit>().setBytesReset(null);
    context.read<GoogleAuthenticationCubit>().googleAuthReset(
          emailCaptcha: widget.emailCaptcha ?? '',
          nonce: widget.nonce ?? '',
        );
  }

  bool isClosingPopup = false;
  void delayedPop({Function? function}) {
    if (isClosingPopup) return;
    isClosingPopup = true;
    if (function != null) function();
    Future.delayed(const Duration(milliseconds: 200), () {
      isClosingPopup = false;
    });
  }

  _onSubmit() {
    context.read<GoogleAuthenticationCubit>().googleAuthResetCheck(code: textController.text);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onPanDown: (_) => FocusScope.of(context).requestFocus(FocusNode()),
        child: SingleChildScrollView(
          child: AnimationLimiter(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 57.0.w),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: staggeredAnimation(
                    children: [
                      120.verticalSpace,
                      Text(
                        StringConstants.qrCodeScan.tr(),
                        style: FontPalette.normal14.copyWith(
                          color: myColorScheme(context).titleColor,
                        ),
                      ),
                      40.verticalSpace,
                      BlocSelector<GoogleAuthenticationCubit, GoogleAuthenticationState, Uint8List?>(
                        selector: (state) => state.bytesReset,
                        builder: (context, value) {
                          return Container(
                            height: 157.h,
                            width: 157.w,
                            decoration: value != null
                                ? BoxDecoration(
                                    color: myColorScheme(context).cardColor,
                                    image: DecorationImage(
                                      image: MemoryImage(value),
                                    ),
                                  )
                                : null,
                            child: value != null
                                ? null
                                : Column(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        height: 30.h,
                                        width: 30.w,
                                        child: const CircularProgressIndicator.adaptive(),
                                      ),
                                    ],
                                  ),
                          );
                        },
                      ),
                      40.verticalSpace,
                      Text(
                        StringConstants.OR.tr(),
                        style: FontPalette.bold26.copyWith(
                          fontSize: 26.sp,
                          color: myColorScheme(context).titleColor,
                        ),
                      ),
                      54.verticalSpace,
                      Text(
                        StringConstants.copyCode.tr(),
                        style: FontPalette.normal13.copyWith(
                          color: myColorScheme(context).titleColor,
                        ),
                      ),
                      18.verticalSpace,
                      Form(
                        key: _formGlobalKey,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            MultiBlocListener(
                              listeners: [
                                BlocListener<GoogleAuthenticationCubit, GoogleAuthenticationState>(
                                  listener: (context, state) {
                                    if (state.googleAuthResetStatus == DataStatus.success) {
                                      googleCodeController.text = state.googleAuthResetData?.qrcode ?? '';
                                    }
                                  },
                                ),
                                BlocListener<GoogleAuthenticationCubit, GoogleAuthenticationState>(
                                  listener: (context, state) {
                                    if (state.googleAuthResetCheckStatus == DataStatus.success) {
                                      context.read<AccountInfoCubit>().getStatus();
                                      CommonFunctions.showDialogPopUp(
                                        context,
                                        CustomAlertDialog(
                                          title: StringConstants.googleCodeUpdatedToast.tr(),
                                          message: StringConstants.successfully.tr(),
                                          actionButtonText: StringConstants.ok.tr(),
                                          buttonBackGroundColor: myColorScheme(context).pendingColor,
                                          onActionButtonPressed: () {
                                            delayedPop(
                                              function: () {
                                                Navigator.pop(context);
                                                Navigator.pop(context);
                                                Navigator.pop(context);
                                              },
                                            );
                                          },
                                          headerImage: Assets.alertSuccess,
                                          isLoading: false,
                                          messageTextStyle: FontPalette.semiBold30.copyWith(fontSize: 29.sp),
                                        ),
                                        barrierDismissible: false,
                                      );
                                    }
                                  },
                                ),
                              ],
                              child: CommonTextField(
                                isEditable: false,
                                suffixIcon: InkWell(
                                  onTap: () {
                                    Clipboard.setData(
                                      ClipboardData(
                                        text: googleCodeController.text,
                                      ),
                                    );
                                    CommonFunctions().showFlutterToast(
                                      StringConstants.copiedClipboard.tr(),
                                    );
                                  },
                                  child: Padding(
                                    padding: const EdgeInsets.only(right: 10.0),
                                    child: SvgPicture.asset(
                                      Assets.copy,
                                      height: 10.h,
                                      width: 10.w,
                                    ),
                                  ),
                                ),
                                textInputType: TextInputType.phone,
                                textInputAction: TextInputAction.next,
                                labelText: StringConstants.code.tr(),
                                hintText: StringConstants.hintWallet.tr(),
                                controller: googleCodeController,
                                validator: (_) {
                                  return null;
                                },
                                onChanged: (_) {},
                              ),
                            ),
                            60.verticalSpace,
                            Text(
                              StringConstants.googleAuthCode.tr(),
                              style: FontPalette.normal13.copyWith(
                                color: myColorScheme(context).titleColor,
                              ),
                            ),
                            16.verticalSpace,
                            CommonPinFiledText(
                              controller: textController,
                              type: StringConstants.typeGoogle.tr(),
                            ),
                            45.verticalSpace,
                            Center(
                              child: BlocSelector<GoogleAuthenticationCubit, GoogleAuthenticationState, DataStatus>(
                                selector: (state) => state.googleAuthResetCheckStatus,
                                builder: (context, state) {
                                  return CustomButton(
                                    width: 356.w,
                                    height: 52.h,
                                    label: StringConstants.submit.tr(),
                                    isOutlined: false,
                                    isLoading: state == DataStatus.loading,
                                    isEnabled: state == DataStatus.idle,
                                    onPressed: () {
                                      if (_formGlobalKey.currentState!.validate()) {
                                        _onSubmit();
                                      }
                                    },
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
