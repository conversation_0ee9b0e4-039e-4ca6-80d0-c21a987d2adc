import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';
import 'package:sf_app_v2/features/profile/logic/profile/profile_cubit.dart';

import '../../../core/common_function.dart';
import '../../../core/constants/assets.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/widgets/common_pin_field.dart';
import '../../../core/widgets/custom_alert_dialog.dart';

class ChangeWalletPasswordScreen extends StatefulWidget {
  final String? nonce;

  const ChangeWalletPasswordScreen({super.key, this.nonce});

  @override
  State<ChangeWalletPasswordScreen> createState() =>
      _ChangeWalletPasswordScreenState();
}

class _ChangeWalletPasswordScreenState
    extends State<ChangeWalletPasswordScreen> {
  final passwordController = TextEditingController();

  final confirmPasswordController = TextEditingController();

  final pinController = TextEditingController();

  final formGlobalKey = GlobalKey<FormState>();

  bool isClosingPopup = false;

  void delayedPop({Function? function}) {
    if (isClosingPopup) return;
    isClosingPopup = true;
    if (function != null) function();
    Future.delayed(const Duration(milliseconds: 200), () {
      isClosingPopup = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onPanDown: (_) => FocusScope.of(context).requestFocus(FocusNode()),
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.arrow_back_ios),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 37.0.w),
                  child: Form(
                    key: formGlobalKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        70.verticalSpace,
                        Center(
                          child: SvgPicture.asset(
                            Assets.logoSvg,
                            width: 108.w,
                            height: 112.h,
                          ),
                        ),
                        60.verticalSpace,
                        Center(
                          child: Text(
                            StringConstants.changeWalletPassword.tr(),
                            style: FontPalette.semiBold20,
                          ),
                        ),
                        39.verticalSpace,
                        Text(
                          StringConstants.newPassword.tr(),
                          style: FontPalette.normal14.copyWith(
                            color: myColorScheme(context).titleColor,
                          ),
                        ),
                        16.verticalSpace,
                        CommonPinFiledText(
                          obscureText: true,
                          controller: passwordController,
                        ),
                        35.verticalSpace,
                        Text(
                          StringConstants.confirmWalletPasswordLabel.tr(),
                          style: FontPalette.normal14.copyWith(
                            color: myColorScheme(context).titleColor,
                          ),
                        ),
                        16.verticalSpace,
                        CommonPinFiledText(
                          obscureText: true,
                          type: 'confirmWallet',
                          controller: confirmPasswordController,
                          passwordController: passwordController,
                        ),
                        50.verticalSpace,
                        BlocListener<ProfileCubit, ProfileState>(
                          listener: (context, state) {
                            if (state.passChangeStatus == DataStatus.success) {
                              CommonFunctions.showDialogPopUp(
                                context,
                                CustomAlertDialog(
                                  title:
                                      StringConstants.walletUpdatedToast.tr(),
                                  actionButtonText: StringConstants.ok.tr(),
                                  buttonBackGroundColor:
                                      myColorScheme(context).primaryColor,
                                  onActionButtonPressed: () {
                                    delayedPop(
                                      function: () {
                                        Navigator.pop(context);
                                        Navigator.pop(context);
                                        Navigator.pop(context);
                                      },
                                    );
                                  },
                                  headerImage: Assets.alertSuccess,
                                  isLoading: false,
                                  messageTextStyle: FontPalette.semiBold30
                                      .copyWith(fontSize: 29.sp),
                                ),
                                barrierDismissible: false,
                              );
                            }
                          },
                          child: BlocSelector<ProfileCubit, ProfileState,
                              DataStatus>(
                            selector: (state) => state.passChangeStatus,
                            builder: (context, state) {
                              return CustomButton(
                                width: 356,
                                height: 52,
                                label: StringConstants.submit.tr(),
                                isOutlined: false,
                                isLoading: state == DataStatus.loading,
                                onPressed: () {
                                  if (formGlobalKey.currentState!.validate()) {
                                    context
                                        .read<ProfileCubit>()
                                        .changeWalletPassword(
                                          nonce: widget.nonce!,
                                          payPassword: passwordController.text,
                                          confirmPayPassword:
                                              confirmPasswordController.text,
                                        );
                                  }
                                },
                              );
                            },
                          ),
                        ),
                        20.verticalSpace,
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
