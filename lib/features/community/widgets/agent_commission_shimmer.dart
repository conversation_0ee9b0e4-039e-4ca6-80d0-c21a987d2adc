import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';

class AgentCommissionShimmer extends StatelessWidget {
  const AgentCommissionShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Stats Grid Shimmer (2x2)
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          mainAxisSpacing: 10.r,
          crossAxisSpacing: 10.r,
          childAspectRatio: 179 / 69,
          children: List.generate(
            4,
            (index) => _buildStatCardShimmer(context),
          ),
        ),
        24.verticalSpace,
        // Generation Cards Shimmer
        ...List.generate(
            3,
            (index) => Padding(
                  padding: EdgeInsets.only(bottom: 12.h),
                  child: _buildGenerationCardShimmer(context),
                )),
      ],
    );
  }

  Widget _buildStatCardShimmer(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
            color:
                myColorScheme(context).borderColor ?? const Color(0xFFE5EAF2)),
      ),
      padding: EdgeInsets.all(12.r),
      child: Row(
        children: [
          CommonShimmer(
            br: 7.r,
            width: 28.r,
            height: 28.r,
          ),
          8.horizontalSpace,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CommonShimmer(
                  br: 4.r,
                  width: 60.w,
                  height: 12.h,
                ),
                4.verticalSpace,
                CommonShimmer(
                  br: 4.r,
                  width: 80.w,
                  height: 16.h,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenerationCardShimmer(BuildContext context) {
    return Container(
      height: 70.h,
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
            color:
                myColorScheme(context).borderColor ?? const Color(0xFFE5EAF2)),
        boxShadow: const [
          BoxShadow(
            offset: Offset(0, 4),
            blurRadius: 12,
            color: Color(0x141890FF),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 16.r, top: 8.r),
            child: CommonShimmer(
              br: 4.r,
              width: 120.w,
              height: 13.h,
            ),
          ),
          2.verticalSpace,
          Container(
            height: 30.h,
            margin: EdgeInsets.symmetric(horizontal: 12.r),
            padding: EdgeInsets.symmetric(vertical: 4.r, horizontal: 16.r),
            decoration: BoxDecoration(
              gradient: Theme.of(context).brightness == Brightness.dark
                  ? null
                  : const LinearGradient(
                      colors: [
                        Color(0xFFF0F7FF),
                        Color(0xFFE6F4FF),
                      ],
                    ),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonShimmer(
                  br: 4.r,
                  width: 80.w,
                  height: 13.h,
                ),
                CommonShimmer(
                  br: 4.r,
                  width: 100.w,
                  height: 13.h,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ProfileHeaderShimmer extends StatelessWidget {
  const ProfileHeaderShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
            color:
                myColorScheme(context).borderColor ?? const Color(0xFFE5EAF2)),
      ),
      child: Row(
        children: [
          CommonShimmer(
            br: 25.r,
            width: 50.r,
            height: 50.r,
          ),
          12.horizontalSpace,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CommonShimmer(
                  br: 4.r,
                  width: 100.w,
                  height: 14.h,
                ),
                3.verticalSpace,
                CommonShimmer(
                  br: 4.r,
                  width: 150.w,
                  height: 12.h,
                ),
              ],
            ),
          ),
          CommonShimmer(
            br: 12.r,
            width: 60.w,
            height: 24.h,
          ),
        ],
      ),
    );
  }
}

class InvitationLinksShimmer extends StatelessWidget {
  const InvitationLinksShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildTextFieldShimmer(context),
        20.verticalSpace,
        _buildTextFieldShimmer(context),
      ],
    );
  }

  Widget _buildTextFieldShimmer(BuildContext context) {
    return Container(
      height: 56.h,
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
            color:
                myColorScheme(context).borderColor ?? const Color(0xFFE5EAF2)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonShimmer(
                    br: 4.r,
                    width: 80.w,
                    height: 10.h,
                  ),
                  4.verticalSpace,
                  CommonShimmer(
                    br: 4.r,
                    width: 200.w,
                    height: 14.h,
                  ),
                ],
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.all(8.r),
            child: CommonShimmer(
              br: 8.r,
              width: 60.w,
              height: 40.h,
            ),
          ),
        ],
      ),
    );
  }
}
