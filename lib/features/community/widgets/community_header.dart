import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import '../../../core/widgets/common_user_name_header.dart';

class CommunityHeaderBackground extends StatelessWidget {
  final double height;

  const CommunityHeaderBackground({super.key, required this.height});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height.h,
      width: 430.w,
      decoration: BoxDecoration(
        color: myColorScheme(context).primaryColor,
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(30.r)),
      ),
    );
  }
}

class CommunityHeader extends StatelessWidget {
  final double height;
  final String title;
  final String userId;
  final String icon1;
  final String icon2;
  final String icon3;
  final void Function()? onPressedIcon1;
  final void Function()? onPressedIcon2;
  final void Function()? onPressedIcon3;

  const CommunityHeader({
    super.key,
    required this.height,
    required this.title,
    required this.userId,
    required this.icon1,
    required this.icon2,
    required this.icon3,
    required this.onPressedIcon1,
    required this.onPressedIcon2,
    required this.onPressedIcon3,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20).r),
      child: Column(
        children: [
          CommonUserNameHeader(
            userId: userId,
            userName: title,
            icon1: icon1,
            onPressedIcon1: null,
            onSupport: onPressedIcon2,
            onNotifications: onPressedIcon3,
          ),
        ],
      ),
    );
  }
}

class CommunityItem extends StatelessWidget {
  final double? height;
  final String title;
  final String iconPath;

  const CommunityItem({
    super.key,
    this.height = 90,
    required this.title,
    required this.iconPath,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height?.h,
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5).r),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          children: [
            Padding(
              padding: EdgeInsets.all(20.r),
              child: Text(
                title,
                style: FontPalette.medium16,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Image.asset(
                iconPath,
                width: 50.w,
                height: 50.w,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
