import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';

import '../../../core/constants/assets.dart';
import '../../../core/theme/my_color_scheme.dart';

class CommunityHeaderTile extends StatelessWidget {
  final String id;
  final String email;
  final String vipLevel;
  final String? avatarUrl;
  final VoidCallback? onTap;

  const CommunityHeaderTile({
    super.key,
    required this.id,
    required this.email,
    required this.vipLevel,
    this.avatarUrl,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: myColorScheme(context).cardColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            _buildAvatar(),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'ID: $id',
                        style: FontPalette.medium14.copyWith(
                          color: myColorScheme(context).titleColor,
                        ),
                      ),
                    ],
                  ),
                  3.verticalSpace,
                  Text(
                    email,
                    style: FontPalette.normal12.copyWith(
                      color: myColorScheme(context).titleColor?.withAlpha(150),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            _buildVipBadge(),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    return SvgPicture.asset(
      Assets.logoSvg,
      height: 32.h,
      width: 32.w,
    );
  }

  Widget _buildVipBadge() {
    return TweenAnimationBuilder(
      tween: Tween<double>(begin: 0.8, end: 1.0),
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Text(
              'VIP $vipLevel',
              style: FontPalette.semiBold14.copyWith(
                color: Colors.white,
              ),
            ),
          ),
        );
      },
    );
  }
}
