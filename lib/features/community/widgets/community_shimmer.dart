import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';

class CommunityShimmer extends StatelessWidget {
  const CommunityShimmer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 5,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.fromLTRB(5, 6, 5, 0).r,
          child: _buildCommunityItemShimmer(),
        );
      },
    );
  }

  Widget _buildCommunityItemShimmer() {
    return Container(
      height: 90.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(5.r),
        border: Border.all(color: const Color(0xFFE5EAF2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: EdgeInsets.all(20.r),
            child: CommonShimmer(
              br: 4.r,
              width: 120.w,
              height: 16.h,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: CommonShimmer(
              br: 25.r,
              width: 50.w,
              height: 50.w,
            ),
          ),
        ],
      ),
    );
  }
}
