import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app_v2/core/constants/assets.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/routes/routes.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/widgets/custom_button.dart';

class CommunityScreenAuth extends StatelessWidget {
  const CommunityScreenAuth({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 8.0.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
              Assets.logoSvg,
              width: 80.w,
              height: 79.h,
            ),
            40.verticalSpace,
            Text(
              'Oops! It looks like you need to sign in.',
              textAlign: TextAlign.center,
              style: FontPalette.normal14,
            ),
            20.verticalSpace,
            Padding(
              padding: EdgeInsets.only(left: 30.w, right: 30.w, bottom: 20.h),
              child: CustomButton(
                onPressed: () => Navigator.pushNamedAndRemoveUntil(
                  context,
                  routeLoginScreen,
                  (route) => false,
                ),
                label: StringConstants.logIn.tr(),
                width: double.infinity,
                height: 52.h,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
