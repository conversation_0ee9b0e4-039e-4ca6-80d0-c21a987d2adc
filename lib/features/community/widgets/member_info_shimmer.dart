import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';

class MemberInfoShimmer extends StatelessWidget {
  const MemberInfoShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 6,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.only(bottom: 16.h),
          child: _buildMemberItemShimmer(context),
        );
      },
    );
  }

  Widget _buildMemberItemShimmer(BuildContext context) {
    return Container(
      height: 82.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: myColorScheme(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Username shimmer
                CommonShimmer(
                  width: 120.w,
                  height: 16.h,
                  br: 4.r,
                ),
                // Balance shimmer
                CommonShimmer(
                  width: 80.w,
                  height: 16.h,
                  br: 4.r,
                ),
              ],
            ),
            SizedBox(height: 8.h),
            // User ID shimmer
            CommonShimmer(
              width: 60.w,
              height: 12.h,
              br: 4.r,
            ),
          ],
        ),
      ),
    );
  }
}
