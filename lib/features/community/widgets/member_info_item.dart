import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/extention.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/features/community/domain/models/agent_sub_users/agent_sub_user.dart';

class MemberInfoItem extends StatelessWidget {
  final ListElement member;

  const MemberInfoItem({
    super.key,
    required this.member,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: myColorScheme(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    member.userName ?? '',
                    style: FontPalette.semiBold14,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 8.w),
                Text(
                  (member.balance?.toStringAsFixed(2) ?? '').toCurrency(),
                  style: FontPalette.semiBold16.copyWith(
                    color: myColorScheme(context).primaryColor,
                  ),
                ),
              ],
            ),
            SizedBox(height: 4.h),
            Text(
              '#${member.userId?.toString() ?? ''}',
              style: FontPalette.normal12.copyWith(
                color: myColorScheme(context).subTitleColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
