import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/constants/enums.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/widgets/common_empty_data.dart';
import '../../../core/widgets/pagination_widget.dart';
import '../logic/community/community_cubit.dart';
import '../widgets/member_info_item.dart';
import '../widgets/member_info_shimmer.dart';

class MemberInfoScreen extends StatefulWidget {
  final int level;
  const MemberInfoScreen({super.key, required this.level});

  @override
  State<MemberInfoScreen> createState() => _MemberInfoScreenState();
}

class _MemberInfoScreenState extends State<MemberInfoScreen> {
  @override
  void initState() {
    super.initState();
    context.read<CommunityCubit>().getAgentSubUsers(level: widget.level);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(StringConstants.memberInfo.tr()),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.0.w, vertical: 16.0.h),
        child: BlocBuilder<CommunityCubit, CommunityState>(
          builder: (context, state) {
            return _buildContent(context, state);
          },
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, CommunityState state) {
    switch (state.agentSubUsersFetchStatus) {
      case DataStatus.loading:
        return _buildLoadingState();
      case DataStatus.success:
        return _buildSuccessState(context, state);
      case DataStatus.failed:
        return _buildErrorState(context, state);
      case DataStatus.idle:
        return _buildLoadingState();
    }
  }

  Widget _buildLoadingState() {
    return const MemberInfoShimmer();
  }

  Widget _buildSuccessState(BuildContext context, CommunityState state) {
    final members = state.agentSubUsers?.list ?? [];

    if (members.isEmpty) {
      return const Center(child: CommonEmpty());
    }

    return PaginationWidget(
      isPaginating: state.agentSubUsersFetchStatus == DataStatus.loading &&
                   state.hasMoreData,
      next: _hasMoreData(state),
      onPagination: (notification) => _handlePagination(context, state),
      child: ListView.separated(
        itemCount: members.length,
        separatorBuilder: (context, index) => SizedBox(height: 16.h),
        itemBuilder: (context, index) {
          final member = members[index];
          return MemberInfoItem(member: member);
        },
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, CommunityState state) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.w,
            color: Colors.grey,
          ),
          SizedBox(height: 16.h),
          Text(
            state.error ?? 'An error occurred',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          ElevatedButton(
            onPressed: () => _retryLoading(context),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  bool _hasMoreData(CommunityState state) {
    final currentCount = state.agentSubUsers?.list?.length ?? 0;
    final totalCount = state.agentSubUsers?.total ?? 0;
    return currentCount < totalCount;
  }

  bool _handlePagination(BuildContext context, CommunityState state) {
    if (!_hasMoreData(state)) {
      return false;
    }

    context.read<CommunityCubit>().getAgentSubUsers(
      page: (state.agentSubUsers?.pageNum ?? 0) + 1,
      level: widget.level,
      isLoadMore: true,
    );
    return true;
  }

  void _retryLoading(BuildContext context) {
    context.read<CommunityCubit>().getAgentSubUsers(level: widget.level);
  }
}
