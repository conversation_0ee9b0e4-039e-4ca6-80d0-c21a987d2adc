import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/api/endpoint/api_endpoints.dart';
import 'package:sf_app_v2/core/api/network/network.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/community/domain/models/community_list.dart';
import 'package:sf_app_v2/features/community/domain/models/agent_commission_stats/agent_commission_stats.dart';
import 'package:sf_app_v2/features/community/domain/models/agent_sub_users/agent_sub_user.dart';
import '../../../home/<USER>/models/carousel/carousel_list.dart';
import '../repository/community_repository.dart';

/// Service class that implements the CommunityRepository interface to handle community-related API calls
@Injectable(as: CommunityRepository)
class CommunityService implements CommunityRepository {
  /// Fetches paginated list of community data
  /// [pageNum] - Page number for pagination
  /// Returns a [ResponseResult] containing [CommunityList] on success
  @override
  Future<ResponseResult<CommunityList>> communityList({
    required int pageNum,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.community(pageNum),
         isSigninRequired: true,
        force: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: CommunityList.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get community list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches list of carousel data for community
  /// Returns a [ResponseResult] containing List of [CarouselData] on success
  @override
  Future<ResponseResult<List<CarouselData>>> carouselList() async {
    try {
      final Response response = await NetworkProvider().get(ApiEndpoints.carouselCommunityList);
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: CarouselList.fromJson(response.data).data);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get carousel list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches agent commission statistics
  /// Returns a [ResponseResult] containing [AgentCommissionStatsData] on success
  @override
  Future<ResponseResult<AgentCommissionStatsData>> agentCommissionStats() async {
    try {
      final Response response = await NetworkProvider().get(ApiEndpoints.agentCommissionStats,
         isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: AgentCommissionStatsData.fromJson(response.data['data']));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get agent commission stats');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// Fetches paginated list of agent sub users
  /// [pageNum] - Page number for pagination
  /// [level] - Level of sub users to fetch
  /// Returns a [ResponseResult] containing [AgentSubUsersData] on success
  @override
  Future<ResponseResult<AgentSubUsersData>> agentSubUsers({
    required int pageNum,
    required int level,
  }) async {
    try {
      final Response response = await NetworkProvider().get(ApiEndpoints.agentSubUsers, 
        queryParameters: {
          'pageNum': pageNum,
          'level': level,
        },
         isSigninRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: AgentSubUsersData.fromJson(response.data['data']));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get agent sub users');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
