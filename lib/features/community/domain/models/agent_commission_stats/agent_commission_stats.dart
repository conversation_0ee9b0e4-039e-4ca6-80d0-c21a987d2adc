import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'agent_commission_stats.freezed.dart';
part 'agent_commission_stats.g.dart';

AgentCommissionStats agentCommissionStatsFromJson(str) =>
    AgentCommissionStats.fromJson((str));

String agentCommissionStatsToJson(AgentCommissionStats data) =>
    json.encode(data.toJson());

@freezed
class AgentCommissionStats with _$AgentCommissionStats {
  const factory AgentCommissionStats({
    int? code,
    AgentCommissionStatsData? data,
    String? msg,
  }) = _AgentCommissionStats;

  factory AgentCommissionStats.fromJson(Map<String, dynamic> json) =>
      _$AgentCommissionStatsFromJson(json);
}

@freezed
class AgentCommissionStatsData with _$AgentCommissionStatsData {
  const factory AgentCommissionStatsData({
    int? directMembers,
    AgentCommissionStatsGeneration? firstGeneration,
    int? otherMembers,
    AgentCommissionStatsGeneration? secondGeneration,
    AgentCommissionStatsGeneration? thirdGeneration,
    int? totalMembers,
    double? totalRevenue,
  }) = _AgentCommissionStatsData;

  factory AgentCommissionStatsData.fromJson(Map<String, dynamic> json) => _$AgentCommissionStatsDataFromJson(json);
}

@freezed
class AgentCommissionStatsGeneration with _$AgentCommissionStatsGeneration {
  const factory AgentCommissionStatsGeneration({
    int? memberCount,
    double? revenue,
  }) = _AgentCommissionStatsGeneration;

  factory AgentCommissionStatsGeneration.fromJson(Map<String, dynamic> json) =>
      _$AgentCommissionStatsGenerationFromJson(json);
}
