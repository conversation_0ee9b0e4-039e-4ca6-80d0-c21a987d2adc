// To parse this JSON data, do
//
//     final agentSubUsers = agentSubUsersFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'agent_sub_user.freezed.dart';
part 'agent_sub_user.g.dart';

AgentSubUsers agentSubUsersFromJson(str) => AgentSubUsers.fromJson((str));

String agentSubUsersToJson(AgentSubUsers data) => json.encode(data.toJson());

@freezed
class AgentSubUsers with _$AgentSubUsers {
  const factory AgentSubUsers({
    int? code,
    AgentSubUsersData? data,
    String? msg,
  }) = _AgentSubUsers;

  factory AgentSubUsers.fromJson(Map<String, dynamic> json) =>
      _$AgentSubUsersFromJson(json);
}

@freezed
class AgentSubUsersData with _$AgentSubUsersData {
  const factory AgentSubUsersData({
    List<ListElement>? list,
    int? pageNum,
    int? pageSize,
    int? total,
  }) = _AgentSubUsersData;

  factory AgentSubUsersData.fromJson(Map<String, dynamic> json) =>
      _$AgentSubUsersDataFromJson(json);
}

@freezed
class ListElement with _$ListElement {
  const factory ListElement({
    double? balance,
    int? userId,
    String? userName,
  }) = _ListElement;

  factory ListElement.fromJson(Map<String, dynamic> json) =>
      _$ListElementFromJson(json);
}
