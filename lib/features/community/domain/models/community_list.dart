import 'package:freezed_annotation/freezed_annotation.dart';

part 'community_list.freezed.dart';
part 'community_list.g.dart';

@freezed
class CommunityList with _$CommunityList {
  const factory CommunityList({
    required int code,
    required CommunityData data,
    required String msg,
  }) = _CommunityList;

  factory CommunityList.fromJson(Map<String, dynamic> json) =>
      _$CommunityListFromJson(json);
}

@freezed
class CommunityData with _$CommunityData {
  const factory CommunityData({
    required List<CommunityDataItem> list,
    required int pageNum,
    required int pageSize,
    required int total,
  }) = _CommunityData;

  factory CommunityData.fromJson(Map<String, dynamic> json) =>
      _$CommunityDataFromJson(json);
}

@freezed
class CommunityDataItem with _$CommunityDataItem {
  const factory CommunityDataItem({
    required int level,
    required int orderAmount,
    required String username,
  }) = _CommunityDataItem;

  factory CommunityDataItem.fromJson(Map<String, dynamic> json) =>
      _$CommunityDataItemFromJson(json);
}
