import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/community/domain/models/community_list.dart';

import '../../../home/<USER>/models/carousel/carousel_list.dart';
import '../models/agent_commission_stats/agent_commission_stats.dart';
import '../models/agent_sub_users/agent_sub_user.dart';

abstract class CommunityRepository {
  const CommunityRepository();

  Future<ResponseResult<CommunityList>> communityList({
    required int pageNum,
  });
  Future<ResponseResult<List<CarouselData>>> carouselList();
  Future<ResponseResult<AgentCommissionStatsData>> agentCommissionStats();
  Future<ResponseResult<AgentSubUsersData>> agentSubUsers({
    required int pageNum,
    required int level,
  });
}
