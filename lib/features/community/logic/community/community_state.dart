part of 'community_cubit.dart';

class CommunityState extends Equatable {
  final CommunityList? communityList;
  final DataStatus communityFetchStatus;
  final List<CarouselData>? carouselList;
  final DataStatus carouselFetchStatus;
  final AgentCommissionStatsData? agentCommissionStats;
  final DataStatus agentCommissionStatsFetchStatus;
  final AgentSubUsersData? agentSubUsers;
  final DataStatus agentSubUsersFetchStatus;
  final String? error;
  final bool hasMoreData;

  const CommunityState({
    this.communityList,
    this.communityFetchStatus = DataStatus.idle,
    this.carouselList,
    this.carouselFetchStatus = DataStatus.idle,
    this.agentCommissionStats,
    this.agentCommissionStatsFetchStatus = DataStatus.idle,
    this.agentSubUsers,
    this.agentSubUsersFetchStatus = DataStatus.idle,
    this.error,
    this.hasMoreData = false,
  });

  @override
  List<Object?> get props => [
        communityList,
        communityFetchStatus,
        carouselList,
        carouselFetchStatus,
        agentCommissionStats,
        agentCommissionStatsFetchStatus,
        agentSubUsers,
        agentSubUsersFetchStatus,
        error,
        hasMoreData,
      ];

  CommunityState copyWith({
    CommunityList? communityList,
    DataStatus? communityFetchStatus,
    List<CarouselData>? carouselList,
    DataStatus? carouselFetchStatus,
    AgentCommissionStatsData? agentCommissionStats,
    DataStatus? agentCommissionStatsFetchStatus,
    AgentSubUsersData? agentSubUsers,
    DataStatus? agentSubUsersFetchStatus,
    String? error,
    bool? hasMoreData,
  }) {
    return CommunityState(
      communityList: communityList ?? this.communityList,
      communityFetchStatus: communityFetchStatus ?? this.communityFetchStatus,
      carouselList: carouselList ?? this.carouselList,
      carouselFetchStatus: carouselFetchStatus ?? this.carouselFetchStatus,
      agentCommissionStats: agentCommissionStats ?? this.agentCommissionStats,
      agentCommissionStatsFetchStatus: agentCommissionStatsFetchStatus ?? this.agentCommissionStatsFetchStatus,
      agentSubUsers: agentSubUsers ?? this.agentSubUsers,
      agentSubUsersFetchStatus: agentSubUsersFetchStatus ?? this.agentSubUsersFetchStatus,
      error: error ?? this.error,
      hasMoreData: hasMoreData ?? this.hasMoreData,
    );
  }
}
