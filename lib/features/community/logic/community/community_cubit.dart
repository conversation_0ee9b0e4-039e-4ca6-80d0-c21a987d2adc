import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/community/domain/models/community_list.dart';
import 'package:sf_app_v2/features/community/domain/repository/community_repository.dart';

import '../../../home/<USER>/models/carousel/carousel_list.dart';
import '../../domain/models/agent_commission_stats/agent_commission_stats.dart';
import '../../domain/models/agent_sub_users/agent_sub_user.dart';

part 'community_state.dart';

@injectable
class CommunityCubit extends Cubit<CommunityState> {
  final CommunityRepository _communityService;

  CommunityCubit(this._communityService) : super(const CommunityState());

  Future<void> getCommunityList({int page = 1, bool isLoadMore = false}) async {
    emit(state.copyWith(communityFetchStatus: DataStatus.loading));
    try {
      final result = await _communityService.communityList(pageNum: page);
      if (result.data != null) {
        if (isLoadMore) {
          emit(
            state.copyWith(
              communityFetchStatus: DataStatus.success,
              communityList: CommunityList(
                code: result.data!.code,
                data: CommunityData(
                  list: [
                    ...?state.communityList?.data.list,
                    ...result.data!.data.list,
                  ],
                  pageNum: result.data!.data.pageNum,
                  pageSize: result.data!.data.pageSize,
                  total: result.data!.data.total,
                ),
                msg: result.data!.msg,
              ),
            ),
          );
        } else {
          emit(
            state.copyWith(
              communityFetchStatus: DataStatus.success,
              communityList: result.data,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            communityFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          communityFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> getCarouselList() async {
    emit(state.copyWith(carouselFetchStatus: DataStatus.loading));
    try {
      final result = await _communityService.carouselList();
      if (result.data != null) {
        emit(state.copyWith(
            carouselFetchStatus: DataStatus.success,
            carouselList: result.data));
      } else {
        emit(state.copyWith(
            carouselFetchStatus: DataStatus.failed, error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(
          carouselFetchStatus: DataStatus.failed, error: e.toString()));
    }
  }

  Future<void> getAgentCommissionStats() async {
    emit(state.copyWith(agentCommissionStatsFetchStatus: DataStatus.loading));
    try {
      final result = await _communityService.agentCommissionStats();
      if (result.data != null) {
        emit(state.copyWith(
            agentCommissionStatsFetchStatus: DataStatus.success,
            agentCommissionStats: result.data));
      } else {
        emit(state.copyWith(
            agentCommissionStatsFetchStatus: DataStatus.failed,
            error: result.error));
      }
    } on Exception catch (e) {
      emit(state.copyWith(
          agentCommissionStatsFetchStatus: DataStatus.failed,
          error: e.toString()));
    }
  }

  Future<void> getAgentSubUsers(
      {int page = 1, bool isLoadMore = false, required int level}) async {
    emit(state.copyWith(agentSubUsersFetchStatus: DataStatus.loading));
    try {
      final result =
          await _communityService.agentSubUsers(pageNum: page, level: level);
      if (result.data != null) {
        if (isLoadMore) {
          emit(
            state.copyWith(
              agentSubUsersFetchStatus: DataStatus.success,
              agentSubUsers: AgentSubUsersData(
                list: [
                  ...?state.agentSubUsers?.list,
                  ...?result.data?.list,
                ],
                pageNum: result.data?.pageNum ?? 0,
                pageSize: result.data?.pageSize ?? 0,
                total: result.data?.total ?? 0,
              ),
            ),
          );
        } else {
          emit(
            state.copyWith(
              agentSubUsersFetchStatus: DataStatus.success,
              agentSubUsers: result.data,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            agentSubUsersFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } on Exception catch (e) {
      emit(
        state.copyWith(
          agentSubUsersFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }
}
