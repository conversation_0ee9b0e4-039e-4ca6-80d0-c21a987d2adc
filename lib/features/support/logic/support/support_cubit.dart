import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/features/support/domain/models/customer_service_channel/customer_service_channel.dart';
import 'package:sf_app_v2/features/support/domain/repository/support_repository.dart';

part 'support_state.dart';

@injectable
class SupportCubit extends Cubit<SupportState> {
  final SupportRepository _supportRepository;

  SupportCubit(this._supportRepository) : super(const SupportState());

  Future<void> getCustomerServiceChannels() async {
    emit(state.copyWith(customerServiceChannelsStatus: DataStatus.loading));
    try {
      final result = await _supportRepository.getCustomerServiceChannels();
      if (result.data != null) {
        emit(
          state.copyWith(
            customerServiceChannels: result.data,
            customerServiceChannelsStatus: DataStatus.success,
          ),
        );
      } else {
        emit(
          state.copyWith(
            customerServiceChannelsStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          customerServiceChannelsStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
    }
  }
}
