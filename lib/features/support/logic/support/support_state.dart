part of 'support_cubit.dart';

class SupportState extends Equatable {
  final List<CustomerServiceChannelData> customerServiceChannels;
  final DataStatus customerServiceChannelsStatus;
  final String error;
  const SupportState({
    this.customerServiceChannels = const [],
    this.customerServiceChannelsStatus = DataStatus.idle,
    this.error = '',
  });

  SupportState copyWith({
    List<CustomerServiceChannelData>? customerServiceChannels,
    DataStatus? customerServiceChannelsStatus,
    String? error,
  }) {
    return SupportState(
      customerServiceChannels: customerServiceChannels ?? this.customerServiceChannels,
      customerServiceChannelsStatus: customerServiceChannelsStatus ?? this.customerServiceChannelsStatus,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [customerServiceChannels, customerServiceChannelsStatus, error];
}
