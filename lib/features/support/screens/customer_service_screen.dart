import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:sf_app_v2/core/constants/enums.dart';
import 'package:sf_app_v2/core/constants/string_constants.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';
import 'package:sf_app_v2/core/utils/mixin/animation.dart';
import 'package:sf_app_v2/core/widgets/common_shimmer.dart';
import 'package:sf_app_v2/features/support/domain/models/customer_service_channel/customer_service_channel.dart';
import 'package:sf_app_v2/features/support/widgets/support_option_item.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../core/utils/mixin/chat.dart';
import '../../../core/widgets/common_empty_data.dart';
import '../logic/support/support_cubit.dart';

class CustomerServiceScreen extends StatefulWidget {
  const CustomerServiceScreen({super.key});

  @override
  State<CustomerServiceScreen> createState() => _CustomerServiceScreenState();
}

class _CustomerServiceScreenState extends State<CustomerServiceScreen> with StaggeredAnimation, HideFloatButtonRouteAwareMixin {
  @override
  void initState() {
    super.initState();
    context.read<SupportCubit>().getCustomerServiceChannels();
  }

  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri)) {
      throw Exception('Could not launch \$uri');
    }
  }

  Widget _buildShimmerLoading() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      child: ListView.builder(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: 4,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: myColorScheme(context).cardColor,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Row(
                children: [
                  CommonShimmer(
                    width: 60.w,
                    height: 60.w,
                    br: 30.r,
                  ),
                  16.horizontalSpace,
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonShimmer(
                          width: 120.w,
                          height: 16.h,
                          br: 4.r,
                        ),
                        8.verticalSpace,
                        CommonShimmer(
                          width: 200.w,
                          height: 14.h,
                          br: 4.r,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: myColorScheme(context).cardColor,
        elevation: 0,
        centerTitle: true,
        title: Text(
          StringConstants.customerService.tr(),
          style: FontPalette.bold18.copyWith(
            color: myColorScheme(context).titleColor,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, size: 20),
          onPressed: () => Navigator.pop(context),
          color: myColorScheme(context).titleColor,
        ),
      ),
      body: BlocBuilder<SupportCubit, SupportState>(
        builder: (context, state) {
          if (state.customerServiceChannelsStatus == DataStatus.loading) {
            return _buildShimmerLoading();
          } else if (state.customerServiceChannelsStatus == DataStatus.success) {
            if (state.customerServiceChannels.isEmpty) {
              return const Center(
                child: CommonEmpty(),
              );
            }
            return RefreshIndicator(
              onRefresh: () async {
                context.read<SupportCubit>().getCustomerServiceChannels();
              },
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  child: AnimationLimiter(
                    child: ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.customerServiceChannels.length,
                      itemBuilder: (context, index) {
                        final channel = state.customerServiceChannels[index];
                        return staggeredAnimation(
                          children: [_buildServiceChannels(channel)],
                        )[0];
                      },
                    ),
                  ),
                ),
              ),
            );
          } else {
            return const SizedBox.shrink();
          }
        },
      ),
    );
  }

  Widget _buildServiceChannels(CustomerServiceChannelData channelItem) {
    return SupportOptionItem(
      icon: channelItem.icon,
      title: channelItem.name,
      subtitle: channelItem.content,
      onTap: () => _launchUrl(channelItem.content),
      fallbackIcon: Icons.message,
    );
  }
}
