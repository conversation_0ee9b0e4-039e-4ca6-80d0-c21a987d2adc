// To parse this JSON data, do
//
//     final customerServiceChannelResponse = customerServiceChannelResponseFromJson(jsonString);

import 'package:freezed_annotation/freezed_annotation.dart';
import 'dart:convert';

part 'customer_service_channel.freezed.dart';
part 'customer_service_channel.g.dart';

CustomerServiceChannel customerServiceChannelResponseFromJson( str) => CustomerServiceChannel.fromJson((str));

String customerServiceChannelResponseToJson(CustomerServiceChannel data) => json.encode(data.toJson());

@freezed
class CustomerServiceChannel with _$CustomerServiceChannel {
    const factory CustomerServiceChannel({
        @JsonKey(name: "code")
        required int code,
        @JsonKey(name: "data")
        required List<CustomerServiceChannelData> data,
        @JsonKey(name: "msg")
        required String msg,
    }) = _CustomerServiceChannel;

    factory CustomerServiceChannel.fromJson(Map<String, dynamic> json) => _$CustomerServiceChannelFromJson(json);
}

@freezed
class CustomerServiceChannelData with _$CustomerServiceChannelData {
    const factory CustomerServiceChannelData({
        @JsonKey(name: "id")
        required int id,
        @JsonKey(name: "name")
        required String name,
        @JsonKey(name: "content")
        required String content,
        @JsonKey(name: "icon")
        required String icon,
    }) = _CustomerServiceChannelData;

    factory CustomerServiceChannelData.fromJson(Map<String, dynamic> json) => _$CustomerServiceChannelDataFromJson(json);
}
