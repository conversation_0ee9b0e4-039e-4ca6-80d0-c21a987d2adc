import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app_v2/core/models/result.dart';
import 'package:sf_app_v2/features/support/domain/models/customer_service_channel/customer_service_channel.dart';
import 'package:sf_app_v2/features/support/domain/repository/support_repository.dart';

import '../../../../core/api/endpoint/api_endpoints.dart';
import '../../../../core/api/network/network.dart';

/// Service class that implements [SupportRepository] to handle customer service related operations
@Injectable(as: SupportRepository)
class SupportService implements SupportRepository {
  /// Fetches the list of available customer service channels
  ///
  /// Makes a GET request to retrieve customer service channels
  /// Returns [ResponseResult] containing either:
  /// - List of [CustomerServiceChannelData] on success
  /// - Error message string on failure
  @override
  Future<ResponseResult<List<CustomerServiceChannelData>>> getCustomerServiceChannels() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.customerServiceChannels,
        // options: Options(
        //   headers: {'auth': true},
        // ),
        // force: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 200) {
          return ResponseResult(data: CustomerServiceChannel.fromJson(response.data).data);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch customer service channels');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
