import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:sf_app_v2/my_app.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sf_app_v2/core/utils/shared_preference_helper.dart';
import 'core/config/app_config.dart';
import 'core/dependency_injection/injectable.dart';
import 'core/providers/app_providers.dart';
import 'package:timezone/data/latest.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependency injection
  initializeGetIt();
  // Initialize localization and date formatting
  await EasyLocalization.ensureInitialized();
  initializeDateFormatting();

  // Initialize shared preferences
  await SharedPreferenceHelper().getInit();

  // Initialize timezone data
  initializeTimeZones();

  // Set up HydratedBloc storage for persisting bloc states
  HydratedBloc.storage = await HydratedStorage.build(
    storageDirectory: await getApplicationDocumentsDirectory(),
  );

  runApp(const _Translation());
}

class _Translation extends StatelessWidget {
  const _Translation();

  @override
  Widget build(BuildContext context) {
    final config = getIt<AppConfig>();

    return EasyLocalization(
      supportedLocales: config.supportedLanguages.map((e) => e.locale).toList(),
      path: 'assets/translations',
      fallbackLocale: const Locale('en', 'US'),
      saveLocale: true,
      child: const _MultiBlocWrapper(),
    );
  }
}

class _MultiBlocWrapper extends StatelessWidget {
  const _MultiBlocWrapper();

  @override
  Widget build(BuildContext context) => MultiBlocProvider(
        providers: AppProviders.getProviders(),
        child: const MyApps(),
      );
}
