# Project metadata
name: sf_app_v2
description: "A new Flutter project."

publish_to: "none"

version: 1.0.0+1

# SDK constraints
environment:
  sdk: ^3.5.4

# Main dependencies
dependencies:
  # UI Components
  action_slider: ^0.7.0
  auto_size_text: ^3.0.0
  carousel_slider: ^5.0.0
  draggable_float_widget: ^0.1.0
  fl_chart: ^0.69.1
  flutter_avif: ^2.5.0
  flutter_bounceable: ^1.1.0
  flutter_html: ^3.0.0-beta.2
  flutter_lucide: ^1.5.0
  flutter_screenutil: ^5.9.3
  flutter_staggered_animations: ^1.1.1
  flutter_svg: ^2.0.14
  flutter_timer_countdown: ^1.0.7
  flutter_widget_from_html: ^0.16.0
  fluttertoast: ^8.2.8
  pinput: ^5.0.0
  qr_flutter: ^4.1.0
  readmore: ^3.0.0
  shimmer_animation: ^2.2.1
  slide_countdown: ^2.0.0
  video_player: ^2.9.2
  webview_flutter: any

  # State Management
  bloc: ^8.1.4
  flutter_bloc: ^8.1.6
  hydrated_bloc: ^9.1.5
  get_it: ^7.7.0
  injectable: ^2.4.4
  equatable: ^2.0.7

  # Network & API
  app_links: ^6.3.2
  cached_network_image: ^3.4.1
  dio: ^5.7.0
  dio_smart_retry: null
  web_socket_channel: ^2.4.0
  html: ^0.15.0

  # Storage & Preferences
  flutter_secure_storage: ^9.2.2
  path_provider: ^2.1.5
  shared_preferences: ^2.3.3

  # # Firebase & Notifications
  # firebase_core: ^3.4.0
  # flutter_local_notifications: ^18.0.1

  # Image & Media
  image_cropper: ^8.0.2
  image_picker: ^1.1.2

  # Utilities
  change_app_package_name: ^1.5.0
  easy_localization: ^3.0.7
  flutter_flavorizr: ^2.2.3
  flutter_launcher_icons: ^0.14.1
  freezed_annotation: ^2.4.4
  install_plugin: ^2.1.0
  intl: ^0.19.0
  package_info_plus: ^8.1.1
  permission_handler: ^11.3.1
  sf_cli: ^1.0.3
  timezone: ^0.9.2
  url_launcher: ^6.3.1

  # Custom packages
  flutter:
    sdk: flutter
  k_chart_plus:
    path: packages/k_chart_plus-1.0.2

# Dependency overrides  
dependency_overrides:
  uuid: ^4.4.2
  file: ^6.1.2
  json_annotation: ^4.9.0
  wakelock_plus: 1.2.11

# Development dependencies
dev_dependencies:
  build_runner: ^2.4.13
  flutter_lints: ^4.0.0
  flutter_test:
    sdk: flutter
  freezed: ^2.5.7
  injectable_generator: ^2.6.2
  json_serializable: ^6.9.0

# App icon configuration
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/logo/sf_app/logo.png"
  adaptive_icon_background: "assets/logo/app-logo-bg.png"
  adaptive_icon_foreground: "assets/logo/sf_app/logo.png"
  min_sdk_android: 21

# Flutter configuration
flutter:
  uses-material-design: true
  
  # Asset configuration
  assets:
    - assets/
    - assets/images/
    - assets/icons/
    - assets/svg/
    - assets/svg/crypto/
    - assets/html/
    - assets/translations/
    - assets/flags/
    - assets/logo/
    - assets/logo/ncm/
    - assets/logo/cfroex/
    - assets/logo/sf_app/
    - assets/logo/sis/
    - assets/splash/ncm/
    - assets/splash/cfroex/
    - assets/splash/sf_app/
    - assets/splash/sis/

  # Font configuration
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Bold.ttf
        - asset: assets/fonts/Poppins-ExtraLight.ttf
        - asset: assets/fonts/Poppins-Light.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Thin.ttf