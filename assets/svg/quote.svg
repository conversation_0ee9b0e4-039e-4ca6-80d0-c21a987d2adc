<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_380_3964)">
<path d="M13 2C6.92888 2 2 6.92888 2 13C2 14.5178 2.31294 15.9573 2.8606 17.2717L7.35135 12.7809C8.07112 12.0612 9.22902 12.0612 9.94879 12.7809L11.4353 14.2674C11.7952 14.6273 12.3741 14.6273 12.734 14.2674L17.2091 9.79232H14.377C13.8762 9.79232 13.4538 9.38549 13.4538 8.86913C13.4538 8.36842 13.8606 7.94595 14.377 7.94595H19.4154C19.9161 7.94595 20.3385 8.35277 20.3385 8.86913V13.9075C20.3385 14.4083 19.9317 14.8307 19.4154 14.8307C18.899 14.8307 18.4922 14.4239 18.4922 13.9075V11.091L13.3755 16.2077C12.6558 16.9275 11.4979 16.9275 10.7781 16.2077L9.29161 14.7212C8.93172 14.3613 8.35277 14.3613 7.99289 14.7212L3.75249 18.9616C5.70839 21.9972 9.11949 24 13 24C19.0711 24 24 19.0711 24 13C24 6.92888 19.0711 2 13 2Z" fill="url(#paint0_linear_380_3964)"/>
</g>
<defs>
<linearGradient id="paint0_linear_380_3964" x1="13" y1="2" x2="13" y2="24" gradientUnits="userSpaceOnUse">
<stop stop-color="#AFB8CB"/>
<stop offset="1" stop-color="#9BA8C2"/>
</linearGradient>
<clipPath id="clip0_380_3964">
<rect width="22" height="22" fill="white" transform="translate(2 2)"/>
</clipPath>
</defs>
</svg>
