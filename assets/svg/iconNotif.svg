<svg width="61" height="61" viewBox="0 0 61 61" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_iiiii_1_94)">
<circle cx="30.5" cy="30.5" r="30.5" fill="#E2E2E2"/>
<circle cx="30.5" cy="30.5" r="30.5" fill="url(#paint0_radial_1_94)"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.8748 17.5C31.8748 16.6716 31.2032 16 30.3748 16C29.5464 16 28.8748 16.6716 28.8748 17.5V18.625H28.0387C24.7011 18.625 21.9387 21.22 21.7303 24.5511L21.3988 29.8513C21.2724 31.8721 20.594 33.8195 19.4374 35.4814C18.3924 36.983 19.3227 39.0568 21.1391 39.2748L26.2498 39.8881V41.5C26.2498 43.7782 28.0966 45.625 30.3748 45.625C32.653 45.625 34.4998 43.7782 34.4998 41.5V39.8881L39.6105 39.2748C41.4269 39.0568 42.3572 36.9829 41.3122 35.4814C40.1556 33.8195 39.4771 31.8721 39.3507 29.8513L39.0192 24.5511C38.8109 21.22 36.0485 18.625 32.7109 18.625H31.8748V17.5ZM28.4998 41.5C28.4998 42.5355 29.3393 43.375 30.3748 43.375C31.4103 43.375 32.2498 42.5355 32.2498 41.5V40.375H28.4998V41.5Z" fill="url(#paint1_linear_1_94)"/>
<defs>
<filter id="filter0_iiiii_1_94" x="-10" y="-12" width="77" height="82" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_94"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-10" dy="-12"/>
<feGaussianBlur stdDeviation="9"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1_94" result="effect2_innerShadow_1_94"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_1_94" result="effect3_innerShadow_1_94"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="6" dy="9"/>
<feGaussianBlur stdDeviation="4.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_1_94" result="effect4_innerShadow_1_94"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_1_94" result="effect5_innerShadow_1_94"/>
</filter>
<radialGradient id="paint0_radial_1_94" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(19.5 12) rotate(55.9679) scale(46.457 46.457)">
<stop stop-color="white"/>
<stop offset="0.697917" stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint1_linear_1_94" x1="30.3748" y1="16" x2="30.3748" y2="45.625" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="#666666"/>
</linearGradient>
</defs>
</svg>
