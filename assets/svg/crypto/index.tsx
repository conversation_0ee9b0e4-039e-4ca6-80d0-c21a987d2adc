import { ReactComponent as atom } from './atom.svg';
import { ReactComponent as avax } from './avax.svg';
import { ReactComponent as bnb } from './bnb.svg';
import { ReactComponent as btc } from './btc.svg';
import { ReactComponent as eth } from './eth.svg';
import { ReactComponent as matic } from './matic.svg';
import { ReactComponent as near } from './near.svg';

export default {
  BTCUSDT: btc,
  ETHUSDT: eth,
  BNBUSDT: bnb,
  MATICUSDT: matic,
  ATOMUSDT: atom,
  AVAXUSDT: avax,
  NEARUSDT: near,
} as {
  BTCUSDT: any;
  ETHUSDT: any;
  BNBUSDT: any;
  MATICUSDT: any;
  ATOMUSDT: any;
  AVAXUSDT: any;
  NEARUSDT: any;
};
