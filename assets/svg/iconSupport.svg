<svg width="61" height="61" viewBox="0 0 61 61" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_iiiii_307_7)">
<circle cx="30.5" cy="30.5" r="30.5" fill="#E2E2E2"/>
<circle cx="30.5" cy="30.5" r="30.5" fill="url(#paint0_radial_307_7)"/>
</g>
<path d="M44.2569 43.5831L43.5966 41.6419C42.3569 37.9905 38.9997 35.502 35.175 35.3387C34.8383 35.6003 34.4835 35.825 34.1157 36.0216C33.5673 37.4614 31.725 37.8514 30.6456 36.7488C29.3686 36.636 28.1995 36.1315 27.1785 35.3388C23.3537 35.5016 19.9974 37.9901 18.7568 41.642L18.0977 43.5831C17.9071 44.1414 17.9989 44.7568 18.3426 45.2361C18.6852 45.7157 19.2396 46 19.8292 46H42.5244C43.1139 46 43.6684 45.7158 44.011 45.2361C44.3546 44.7568 44.4463 44.1414 44.2569 43.5831Z" fill="url(#paint1_linear_307_7)"/>
<path d="M33.7035 33.8614C35.9239 32.3007 37.4769 28.8542 37.4769 25.5222C37.4769 16.0528 24.8776 16.0629 24.8776 25.5222C24.8776 29.5718 27.1705 33.7843 30.1699 34.5795C30.4591 33.7601 31.2331 33.1675 32.1504 33.1675C32.7672 33.1674 33.3175 33.438 33.7035 33.8614Z" fill="url(#paint2_linear_307_7)"/>
<path d="M22.113 29.4755C22.4254 30.7025 24.1278 30.3649 24.1278 29.2286C24.1278 24.674 24.3454 24.2487 23.7357 23.7544C25.1315 15.1532 37.1554 15.2177 38.6189 23.7553C38.01 24.2512 38.2278 24.6745 38.2278 29.2286C38.2278 29.5848 38.4193 29.8837 38.6944 30.0692C38.6944 33.6123 37.2572 33.4052 32.9354 34.5507C32.2672 33.8255 31.0739 34.2976 31.0739 35.2758C31.0739 36.5009 32.8296 36.7875 33.1703 35.556C36.6594 34.6308 39.7264 34.339 39.7264 30.8518V30.1361C39.9854 30.0021 40.1688 29.7643 40.2424 29.4755C43.6057 29.407 43.6067 24.3666 40.2424 24.2981C40.1728 24.0229 40.0016 23.7982 39.7606 23.6611C39.8474 22.629 39.6347 21.2724 39.043 19.9627C34.928 11.4715 22.7741 14.7939 22.5837 23.0231C22.5575 23.3018 22.5787 23.4994 22.5947 23.6611C22.3538 23.7982 22.1826 24.0229 22.113 24.2981C20.704 24.3253 19.5681 25.4718 19.5681 26.8868C19.5681 28.3019 20.704 29.4483 22.113 29.4755Z" fill="url(#paint3_linear_307_7)"/>
<defs>
<filter id="filter0_iiiii_307_7" x="-10" y="-12" width="77" height="82" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_307_7"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-10" dy="-12"/>
<feGaussianBlur stdDeviation="9"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_307_7" result="effect2_innerShadow_307_7"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_307_7" result="effect3_innerShadow_307_7"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="6" dy="9"/>
<feGaussianBlur stdDeviation="4.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_307_7" result="effect4_innerShadow_307_7"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_307_7" result="effect5_innerShadow_307_7"/>
</filter>
<radialGradient id="paint0_radial_307_7" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(19.5 12) rotate(55.9679) scale(46.457 46.457)">
<stop stop-color="white"/>
<stop offset="0.697917" stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint1_linear_307_7" x1="31.1769" y1="35.3387" x2="31.1769" y2="46" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="#666666"/>
</linearGradient>
<linearGradient id="paint2_linear_307_7" x1="31.1772" y1="18.424" x2="31.1772" y2="34.5795" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="#666666"/>
</linearGradient>
<linearGradient id="paint3_linear_307_7" x1="31.1667" y1="15" x2="31.1667" y2="36.3493" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="#666666"/>
</linearGradient>
</defs>
</svg>
