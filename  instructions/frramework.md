# Flutter App Architecture Documentation

## 1. Overall Structure

The application follows a feature-first, clean architecture approach with clear separation of concerns:

```
lib/
├── core/                  # Core utilities and infrastructure
├── config/                # App configuration
├── features/              # Feature modules
├── shared/                # Shared components
```

## 2. Data Flow Architecture

### 2.1 Core Components

#### ResponseResult<T>
A generic wrapper class that encapsulates API responses:

```dart
class ResponseResult<T> {
  final T? data;           // Success data
  final String? error;     // Error message
  final String? token;     // Optional token (for auth responses)

  ResponseResult({this.data, this.error, this.token});

  bool get isSuccess => data != null;
}
```

#### NetworkProvider
Central class for making HTTP requests with consistent error handling:

```dart
class NetworkProvider {
  final Dio _dio;
  
  // Methods for different HTTP verbs
  Future<Response<T>> get<T>(String path, {...});
  Future<Response<T>> post<T>(String path, {...});
  Future<Response<T>> put<T>(String path, {...});
  Future<Response<T>> delete<T>(String path, {...});
  Future<Response<T>> formData<T>(String path, {...});
}
```

### 2.2 Feature Architecture

Each feature follows this structure:

```
feature_name/
├── domain/               # Domain layer (business logic)
│   ├── models/           # Data models
│   ├── repository/       # Repository interfaces
│   └── services/         # Service implementations
├── logic/                # State management (Cubits/BLoCs)
├── screens/              # UI screens
└── widgets/              # UI components
```

## 3. Key Patterns

### 3.1 Repository Pattern

#### Repository Interface
Defines the contract for data operations:

```dart
abstract class FeatureRepository {
  Future<ResponseResult<ModelType>> getData(parameters);
  Future<ResponseResult<bool>> performAction(parameters);
}
```

#### Service Implementation
Implements the repository interface using NetworkProvider:

```dart
@Injectable(as: FeatureRepository)
class FeatureService implements FeatureRepository {
  @override
  Future<ResponseResult<ModelType>> getData(parameters) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.endpoint,
        isAuthRequired: true,
        queryParameters: parameters,
      );
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: ModelType.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed operation');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}
```

### 3.2 Model Structure

Models use Freezed for immutability and code generation:

```dart
@freezed
class ModelName with _$ModelName {
  const factory ModelName({
    @JsonKey(name: 'server_field_name') String? fieldName,
    @Default(defaultValue) Type fieldWithDefault,
    required Type requiredField,
  }) = _ModelName;

  factory ModelName.fromJson(Map<String, dynamic> json) => 
      _$ModelNameFromJson(json);
}
```

### 3.3 State Management

Using Cubit pattern for state management:

```dart
// State
@freezed
class FeatureState with _$FeatureState {
  const factory FeatureState({
    @Default(false) bool isLoading,
    @Default(null) ModelType? data,
    @Default(null) String? error,
  }) = _FeatureState;
}

// Cubit
class FeatureCubit extends Cubit<FeatureState> {
  final FeatureRepository _repository;
  
  FeatureCubit(this._repository) : super(const FeatureState());
  
  Future<void> loadData(parameters) async {
    emit(state.copyWith(isLoading: true, error: null));
    
    final result = await _repository.getData(parameters);
    
    if (result.isSuccess) {
      emit(state.copyWith(
        isLoading: false,
        data: result.data,
      ));
    } else {
      emit(state.copyWith(
        isLoading: false,
        error: result.error,
      ));
    }
  }
}
```

## 4. Network Layer Details

### 4.1 NetworkProvider Configuration

```dart
NetworkProvider({String? baseUrl})
    : _dio = Dio(
        BaseOptions(
          baseUrl: baseUrl ?? '${HostUtil().currentHost ?? Urls.baseUrl}/api',
          headers: {
            "Content-Type": "application/json",
            // Other default headers
          },
          connectTimeout: const Duration(seconds: 20),
          receiveTimeout: const Duration(seconds: 20),
          sendTimeout: const Duration(seconds: 20),
        ),
      ) {
  _setupInterceptors();
}
```

### 4.2 Authentication Handling

```dart
// In NetworkProvider
Future<void> _handleRequest(
  RequestOptions options,
  RequestInterceptorHandler handler,
) async {
  if (options.headers.containsKey('auth')) {
    options.headers.remove('auth');
    bool isSignedIn = await AuthUtils.instance.isSignedIn;

    if (!isSignedIn) {
      return handler.reject(
        DioException(
          requestOptions: options,
          error: 'signInError'.tr(),
        ),
      );
    }

    final String? token = await SecureStorageHelper().readSecureData(LocalStorageKeys.token);
    if (token != null && token.isNotEmpty) {
      options.headers['Authorization'] = token;
    }
  }
  
  // Continue with request
  handler.next(options);
}
```

### 4.3 Error Handling

```dart
Future<void> _handleError(
  DioException error,
  ErrorInterceptorHandler handler,
) async {
  // Handle different error types (401, 500, etc.)
  if (error.response?.statusCode == 401) {
    await _handleUnauthorizedError();
  }
  
  // Continue with error
  handler.next(error);
}
```

## 5. WebSocket Integration

### 5.1 WebSocket Service

```dart
class WebSocketService implements WebSocketInterface {
  final WebSocketChannel _channel;
  
  void connect(String url) {
    // Connect to WebSocket
  }
  
  Stream<WebSocketMessage> onMessage(String type) {
    // Filter messages by type
  }
}
```

### 5.2 WebSocket Mixin

```dart
mixin WebSocketMixin {
  Stream<WebSocketMessage> onMessage(String type, {bool loginRequired = false}) {
    // Return filtered stream of messages
  }
}
```

## 6. Dependency Injection

Using injectable package for dependency injection:

```dart
@module
abstract class AppModule {
  @singleton
  WebSocketService get webSocketService => WebSocketService();
  
  @singleton
  NetworkProvider get networkProvider => NetworkProvider();
}

@Injectable(as: FeatureRepository)
class FeatureService implements FeatureRepository {
  // Implementation
}
```