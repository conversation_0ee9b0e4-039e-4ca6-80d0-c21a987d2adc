{"swagger": "2.0", "info": {"description": "API 描述", "version": "1.0", "title": "SuperFuture接口文档", "contact": {"name": "SuperFuture", "url": "https://www.google.com"}}, "host": "www.superfuture.world", "basePath": "/", "tags": [{"name": "article-controller", "description": "Article Controller"}, {"name": "stock-transaction-record-controller", "description": "Stock Transaction Record Controller"}, {"name": "tencent-IM相关接口", "description": "Trtc Controller"}, {"name": "test-controller", "description": "Test Controller"}, {"name": "公司配置接口", "description": "Company Config Controller"}, {"name": "合约产品接口", "description": "Product Controller"}, {"name": "客服渠道", "description": "Customer Service Channel Controller"}, {"name": "导师信息管理", "description": "Mentor Controller"}, {"name": "市场行情接口", "description": "Market Ticker Controller"}, {"name": "我的社区接口", "description": "Community Controller"}, {"name": "文件上传接口", "description": "File Controller"}, {"name": "注册接口", "description": "Register Controller"}, {"name": "用户信息接口", "description": "User Profile Controller"}, {"name": "用户端公告相关接口", "description": "Announcement Controller"}, {"name": "用户认证接口", "description": "Auth Controller"}, {"name": "登录接口", "description": "Login Controller"}, {"name": "轮播图接口", "description": "Carouse Controller"}, {"name": "钱包接口", "description": "Wallet Controller"}], "paths": {"/announcement/have_read": {"post": {"tags": ["用户端公告相关接口"], "summary": "获取当前需要展示的用户端公告列表", "operationId": "haveReadUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "announcementHaveReadVo", "description": "announcementHaveReadVo", "required": true, "schema": {"$ref": "#/definitions/AnnouncementHaveReadVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/announcement/info": {"post": {"tags": ["用户端公告相关接口"], "summary": "获取当前需要展示的用户端公告列表", "operationId": "infoUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«List«AnnouncementVo»»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/article/details": {"get": {"tags": ["article-controller"], "summary": "获取资讯详情", "operationId": "getDetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "id", "in": "query", "description": "id", "required": false, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«ArticleDetailBO»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/article/page": {"get": {"tags": ["article-controller"], "summary": "分页查询文章", "operationId": "pageUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "pageNum", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "status", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "title", "in": "query", "required": false, "type": "string"}, {"name": "type", "in": "query", "required": false, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Pager«ArticleVo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/auth/google_token": {"get": {"tags": ["用户认证接口"], "summary": "获取谷歌验证秘钥", "operationId": "googleTokenUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«谷歌验证返回信息»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/auth/google_token/check": {"post": {"tags": ["用户认证接口"], "summary": "校验谷歌验证码", "operationId": "checkGoogleTokenUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "checkReqVo", "description": "checkReqVo", "required": true, "schema": {"$ref": "#/definitions/谷歌验证模型"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/auth/identity": {"post": {"tags": ["用户认证接口"], "summary": "申请身份认证", "operationId": "identityUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "identityReqVo", "description": "identityReqVo", "required": true, "schema": {"$ref": "#/definitions/身份认证模型"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«string»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/auth/mobile_no": {"post": {"tags": ["用户认证接口"], "summary": "绑定手机号", "operationId": "checkMobileNoUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "mobileNoReqVo", "description": "mobileNoReqVo", "required": true, "schema": {"$ref": "#/definitions/手机号模型"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/auth/pay_password": {"post": {"tags": ["用户认证接口"], "summary": "设置支付密码", "operationId": "payPasswordUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "createReqVo", "description": "createReqVo", "required": true, "schema": {"$ref": "#/definitions/支付密码"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/auth/status": {"get": {"tags": ["用户认证接口"], "summary": "获取验证状态", "operationId": "getAuthStatusUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«AuthStatusVo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/carousel/{type}/list": {"get": {"tags": ["轮播图接口"], "summary": "listCarouselImages", "operationId": "listCarouselImagesUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "type", "in": "path", "description": "type", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«List«CarouselImageVO»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/change_password": {"post": {"tags": ["登录接口"], "summary": "修改登录密码", "operationId": "changePasswordUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "changePasswordReqVo", "description": "changePasswordReqVo", "required": true, "schema": {"$ref": "#/definitions/ChangePasswordReqVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/change_password/email_captcha/check": {"post": {"tags": ["登录接口"], "summary": "校验邮箱验证码", "operationId": "checkEmailCaptchaUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "checkReqVo", "description": "checkReqVo", "required": true, "schema": {"$ref": "#/definitions/EmailCaptchaCheckReqVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«随机数模型»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/change_password/email_captcha/send": {"get": {"tags": ["登录接口"], "summary": "发送邮箱验证码", "operationId": "emailCaptchaUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "email", "description": "邮箱地址", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«随机数模型»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/community/friend/page": {"get": {"tags": ["我的社区接口"], "summary": "获取朋友列表", "operationId": "listAllUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "pageNum", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "required": false, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Pager«FriendListVo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/config/about_us": {"get": {"tags": ["公司配置接口"], "summary": "关于我们", "operationId": "aboutUsUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«string»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/config/online_service": {"post": {"tags": ["公司配置接口"], "summary": "获取在线客服链接", "operationId": "infoUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«string»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/config/privacy": {"get": {"tags": ["公司配置接口"], "summary": "隐私政策", "operationId": "privacyUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«string»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/config/protocol": {"get": {"tags": ["公司配置接口"], "summary": "协议声明", "operationId": "protocolUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«string»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/config/serviceAgreement": {"get": {"tags": ["公司配置接口"], "summary": "服务条款", "operationId": "serviceAgreementUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«string»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/customer-service-channel/list": {"get": {"tags": ["客服渠道"], "summary": "获取客服渠道列表", "operationId": "listUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«List«CustomerServiceChannelVO»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/login": {"post": {"tags": ["登录接口"], "summary": "用户登录", "operationId": "loginUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "loginReqVO", "description": "loginReqVO", "required": true, "schema": {"$ref": "#/definitions/登录信息"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«UserInfoVo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/login/captcha": {"get": {"tags": ["登录接口"], "summary": "获取登录验证码", "operationId": "captchaUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«CaptchaVo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/login/getIpDetail": {"get": {"tags": ["登录接口"], "summary": "getIpDetail", "operationId": "getIpDetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«GeoVo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/logout": {"get": {"tags": ["登录接口"], "summary": "用户登出", "operationId": "logoutUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/market/depth/l2": {"get": {"tags": ["市场行情接口"], "summary": "获取股票深度数据", "operationId": "getDepthQuoteL2UsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "depth", "in": "query", "description": "depth", "required": false, "type": "integer", "format": "int32"}, {"name": "instrument", "in": "query", "description": "instrument", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«JSONObject»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/market/getComponentStock": {"get": {"tags": ["市场行情接口"], "summary": "获取股票指数（即将删除）", "operationId": "getComponentStockUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«List«ComponentStockVO»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/market/getGainDistribution": {"get": {"tags": ["市场行情接口"], "summary": "涨跌分布", "operationId": "getGainDistributionUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«GainDistributionVO»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/market/getMarketPlate": {"get": {"tags": ["市场行情接口"], "summary": "获取市场板块数据", "operationId": "getMarketPlateUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "field", "in": "query", "description": "field", "required": false, "type": "string", "default": "gain"}, {"name": "marketType", "in": "query", "description": "marketType", "required": false, "type": "string"}, {"name": "order", "in": "query", "description": "order", "required": false, "type": "string", "default": "DESC"}, {"name": "page", "in": "query", "description": "page", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "type": "integer", "format": "int32"}, {"name": "plate", "in": "query", "description": "plate", "required": false, "type": "string"}, {"name": "securityType", "in": "query", "description": "securityType", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«object»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/market/getPlateList": {"get": {"tags": ["市场行情接口"], "summary": "板块列表", "operationId": "getIndustryPlateUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "type": "integer", "format": "int32"}, {"name": "type", "in": "query", "description": "类型 1-行业板块 2-概念板块", "required": false, "type": "string", "allowEmptyValue": false}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«object»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/market/getStockKline": {"get": {"tags": ["市场行情接口"], "summary": "获取股票K线图", "operationId": "getStockKlineUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "period", "in": "query", "description": "day/week/month/year", "required": true, "type": "string"}, {"name": "symbol", "in": "query", "description": "symbol", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«StockKline»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/market/getStockList": {"get": {"tags": ["市场行情接口"], "summary": "股票列表", "operationId": "getStockListUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "order", "in": "query", "description": "排序 DESC ASC", "required": false, "type": "string"}, {"name": "pageNum", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "sortType", "in": "query", "description": "排序类型 1:价格 2:涨跌幅", "required": false, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Pager«JSONObject»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/market/getStockListByKeyword": {"get": {"tags": ["市场行情接口"], "summary": "Get stock List data by search keyword", "operationId": "getStockListByKeywordUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "keyword", "in": "query", "description": "keyword", "required": false, "type": "string"}, {"name": "pageNum", "in": "query", "description": "pageNum", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": false, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«object»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/market/kline": {"get": {"tags": ["市场行情接口"], "summary": "获取K线数据", "operationId": "klineUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "instrument", "in": "query", "description": "instrument", "required": true, "type": "string"}, {"name": "period", "in": "query", "description": "周期", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«JSONObject»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/market/security/list": {"get": {"tags": ["市场行情接口"], "summary": "批量获取证券详情数据(指数数据等)", "operationId": "securityListUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "format", "in": "query", "description": "格式 0:列表格式 1:字典格式", "required": true, "type": "string"}, {"name": "instrument", "in": "query", "description": "证券标识 HKEX|1|00700|R,HKEX|1|00001|R 多个证券标识请使用英文,隔开\n入参描述：\n证券市场|证券类型|证券代码|D 延迟行情\n证券市场|证券类型|证券代码|R 实时行情", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«object»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/market/securityList": {"get": {"tags": ["市场行情接口"], "summary": "Get stock trading list", "operationId": "getStockSecurityListUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "app", "in": "query", "description": "App", "required": false, "type": "string"}, {"name": "fqtype", "in": "query", "description": "fqtype", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "Limit", "required": false, "type": "string"}, {"name": "market", "in": "query", "description": "Stock market", "required": false, "type": "string"}, {"name": "openid", "in": "query", "description": "Openid", "required": false, "type": "string"}, {"name": "period", "in": "query", "description": "Time period", "required": false, "type": "string"}, {"name": "symbol", "in": "query", "description": "Stock symbol", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«object»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/market/stockInfo": {"get": {"tags": ["市场行情接口"], "summary": "获取股票信息", "operationId": "getStockInfoUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "instrument", "in": "query", "description": "instrument", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«object»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/market/timeLine": {"get": {"tags": ["市场行情接口"], "summary": "获取分时数据", "operationId": "timeLineUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "instrument", "in": "query", "description": "instrument", "required": true, "type": "string"}, {"name": "period", "in": "query", "description": "周期", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«object»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/market/timeLine/min": {"get": {"tags": ["市场行情接口"], "summary": "获取股票分时数据", "operationId": "getTimeLineMinUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "app", "in": "query", "description": "App", "required": false, "type": "string"}, {"name": "fqtype", "in": "query", "description": "fqtype", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "Limit", "required": false, "type": "string"}, {"name": "market", "in": "query", "description": "Stock market", "required": false, "type": "string"}, {"name": "openid", "in": "query", "description": "Openid", "required": false, "type": "string"}, {"name": "period", "in": "query", "description": "Time period", "required": false, "type": "string"}, {"name": "symbol", "in": "query", "description": "Stock symbol", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«object»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/mentor/info/page": {"get": {"tags": ["导师信息管理"], "summary": "分页查询导师信息", "operationId": "pageUsingGET_1", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "company", "in": "query", "required": false, "type": "string"}, {"name": "name", "in": "query", "required": false, "type": "string"}, {"name": "pageNum", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "required": false, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Pager«导师信息查询模型»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/product/increase_contract": {"post": {"tags": ["合约产品接口"], "summary": "产品加仓", "operationId": "increaseContractUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "increaseReqVo", "description": "increaseReqVo", "required": true, "schema": {"$ref": "#/definitions/产品加仓信息"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/product/list/{mentorId}": {"get": {"tags": ["合约产品接口"], "summary": "获取产品列表", "operationId": "listAllUsingGET_1", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "mentorId", "in": "path", "description": "mentorId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«List«合约产品数据»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/product/oderDetail": {"get": {"tags": ["合约产品接口"], "summary": "查询订单详情", "operationId": "oderDetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "orderNo", "in": "query", "description": "orderNo", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«产品订单详情»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/product/oderPage": {"get": {"tags": ["合约产品接口"], "summary": "分页查询购买的订单列表", "operationId": "oderPageUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "createTimeEnd", "in": "query", "description": "结束购买时间", "required": false, "type": "string"}, {"name": "createTimeStart", "in": "query", "description": "开始购买时间", "required": false, "type": "string"}, {"name": "orderNo", "in": "query", "description": "订单号", "required": false, "type": "string"}, {"name": "pageNum", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "processStatus", "in": "query", "description": "0.默认状态(待审核) 1.审核通过 2.审核通过 3.跟买中 4.已结束", "required": false, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Pager«订单列表查询»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/product/oderRejectPage": {"get": {"tags": ["合约产品接口"], "summary": "分页查询审核拒绝的订单", "operationId": "oderRejectPageUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "createTimeEnd", "in": "query", "description": "结束购买时间", "required": false, "type": "string"}, {"name": "createTimeStart", "in": "query", "description": "开始购买时间", "required": false, "type": "string"}, {"name": "orderNo", "in": "query", "description": "订单号", "required": false, "type": "string"}, {"name": "pageNum", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "processStatus", "in": "query", "description": "0.默认状态(待审核) 1.审核通过 2.审核通过 3.跟买中 4.已结束", "required": false, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Pager«产品订单模型»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/product/pay": {"post": {"tags": ["合约产品接口"], "summary": "购买产品", "operationId": "purchaseApplyUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "payReqVo", "description": "payReqVo", "required": true, "schema": {"$ref": "#/definitions/产品支付信息"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«订单列表查询»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/product/proposeProfit": {"post": {"tags": ["合约产品接口"], "summary": "提盈", "operationId": "proposeProfitUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "reqVO", "description": "reqVO", "required": true, "schema": {"$ref": "#/definitions/提盈"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/product/purchased": {"get": {"tags": ["合约产品接口"], "summary": "获取已购买的产品列表", "operationId": "purchasedListUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "mentorId", "in": "query", "description": "mentorId", "required": true, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«List«产品订单模型»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/product/transaction/{id}/record": {"get": {"tags": ["合约产品接口"], "summary": "交易记录", "operationId": "pageUsingGET_2", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "id", "in": "path", "description": "id", "required": true, "type": "integer", "format": "int64"}, {"name": "pageNum", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "required": false, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Pager«TransactionRecordVo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/product/unbinding": {"post": {"tags": ["合约产品接口"], "summary": "unbinding", "operationId": "unbindingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "payReqVo", "description": "payReqVo", "required": true, "schema": {"$ref": "#/definitions/ProductOrderUnbindingReqVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/profile/email_captcha/check": {"post": {"tags": ["用户信息接口"], "summary": "校验邮箱验证码", "operationId": "checkEmailCaptchaUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "checkReqVo", "description": "checkReqVo", "required": true, "schema": {"$ref": "#/definitions/UserEmailCaptchaCheckReqVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«随机数模型»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/profile/email_captcha/send": {"get": {"tags": ["用户信息接口"], "summary": "发送邮箱验证码", "operationId": "emailCaptchaUsingGET_1", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«随机数模型»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/profile/expend_address": {"get": {"tags": ["用户信息接口"], "summary": "获取用户提现地址", "operationId": "getExpendAddressUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«List«ExpendAddressVo»»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/profile/expend_address/save": {"post": {"tags": ["用户信息接口"], "summary": "新增用户提现地址", "operationId": "saveUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "createVO", "description": "createVO", "required": true, "schema": {"$ref": "#/definitions/用户提现地址修改模型"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/profile/info": {"get": {"tags": ["用户信息接口"], "summary": "获取用户信息", "operationId": "getProfileInfoUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«UserProfileInfo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/profile/login_password/change": {"post": {"tags": ["用户信息接口"], "summary": "修改登录密码", "operationId": "changeLoginPasswordUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "reqVo", "description": "reqVo", "required": true, "schema": {"$ref": "#/definitions/ChangePasswordReqVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/profile/pay_password/change": {"post": {"tags": ["用户信息接口"], "summary": "修改支付密码", "operationId": "changePasswordUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "changePasswordReqVo", "description": "changePasswordReqVo", "required": true, "schema": {"$ref": "#/definitions/ChangePayPasswordReqVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/profile/reset_google_token": {"post": {"tags": ["用户信息接口"], "summary": "重置谷歌秘钥", "operationId": "changePasswordUsingPOST_2", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "restGoogleKeyReqVo", "description": "restGoogleKeyReqVo", "required": true, "schema": {"$ref": "#/definitions/RestGoogleKeyReqVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«谷歌验证返回信息»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/profile/reset_google_token/check": {"post": {"tags": ["用户信息接口"], "summary": "校验重置后的谷歌验证码", "operationId": "checkGoogleTokenUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "checkReqVo", "description": "checkReqVo", "required": true, "schema": {"$ref": "#/definitions/谷歌验证模型"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/register/": {"post": {"tags": ["注册接口"], "summary": "用户注册", "operationId": "registerUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "registerCreateReqVo", "description": "registerCreateReqVo", "required": true, "schema": {"$ref": "#/definitions/用户注册模型1"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/register/captcha": {"get": {"tags": ["注册接口"], "summary": "获取注册验证码", "operationId": "captchaUsingGET_1", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«CaptchaVo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/register/checkCode": {"get": {"tags": ["注册接口"], "summary": "校验邀请码", "operationId": "checkCodeUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "invitationCode", "description": "邀请码", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«boolean»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/register/otp/send": {"post": {"tags": ["注册接口"], "summary": "发送邮箱验证码", "operationId": "emailCaptchaUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "registerOtpReqVo", "description": "registerOtpReqVo", "required": true, "schema": {"$ref": "#/definitions/发送验证码模型"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«string»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/stock/transaction/page": {"get": {"tags": ["stock-transaction-record-controller"], "summary": "分页查询交易记录表", "operationId": "pageUsingGET_3", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "orderNo", "in": "query", "required": false, "type": "string"}, {"name": "pageNum", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "required": false, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«StockTransactionRecordPageVO»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/tc/autoAddKefu": {"post": {"tags": ["tencent-IM相关接口"], "summary": "autoAddKefu", "operationId": "autoAddKefuUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "username", "in": "query", "description": "username", "required": false, "type": "string"}], "responses": {"200": {"description": "OK"}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/tc/checkFriendAssignment": {"post": {"tags": ["tencent-IM相关接口"], "summary": "Check Friend Assignment Status", "operationId": "checkFriendAssignmentUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«boolean»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/tc/deleteFriend": {"post": {"tags": ["tencent-IM相关接口"], "summary": "deleteFriend", "operationId": "deleteFriendUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "fromA<PERSON>unt", "in": "query", "description": "fromA<PERSON>unt", "required": true, "type": "string"}, {"name": "toAccount", "in": "query", "description": "toAccount", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«boolean»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/tc/friends": {"get": {"tags": ["tencent-IM相关接口"], "summary": "Get user's friend list from Tencent IM", "operationId": "getFriendsUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "username", "in": "query", "description": "username", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«string»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/tc/getProfile": {"post": {"tags": ["tencent-IM相关接口"], "summary": "Get User Profile", "operationId": "getUserProfileUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "userId", "in": "query", "description": "userId", "required": false, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«string»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/tc/updateProfile": {"post": {"tags": ["tencent-IM相关接口"], "summary": "updateUserProfile", "operationId": "updateUserProfileUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "userId", "in": "query", "description": "userId", "required": false, "type": "integer", "format": "int64"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«string»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/tc/userSig": {"post": {"tags": ["tencent-IM相关接口"], "summary": "Get UserSig", "operationId": "userSigUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«TcUserSigVo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/test/test": {"get": {"tags": ["test-controller"], "summary": "test", "operationId": "testUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"type": "string"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/upload/{fileType}": {"post": {"tags": ["文件上传接口"], "summary": "上传文件", "operationId": "uploadUsingPOST", "consumes": ["multipart/form-data"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "file", "in": "formData", "description": "file", "required": true, "type": "file"}, {"name": "fileType", "in": "path", "description": "文件类型,ID_CARD:身份证照片,ARTICLE_COVER:文章封面,ARTICLE_DETAIL:文章详情", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«UploadResult»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/wallet/balance": {"get": {"tags": ["钱包接口"], "summary": "获取全部钱包余额", "operationId": "getAllBalanceUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«全部钱包余额模型»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/wallet/balance/{type}": {"get": {"tags": ["钱包接口"], "summary": "获取单个钱包余额", "operationId": "getBalanceUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "type", "in": "path", "description": "钱包类型,deposit:入金钱包,profit:盈利钱包,community:社区钱包,collection:归集钱包", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«AccountBalanceVo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/wallet/collection/amount": {"post": {"tags": ["钱包接口"], "summary": "申请提现", "operationId": "createSettOrderUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "settAmountReqVo", "description": "settAmountReqVo", "required": true, "schema": {"$ref": "#/definitions/SettAmountReqVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«SettAmountResVo»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/wallet/collection/supervisor": {"get": {"tags": ["钱包接口"], "summary": "获取用户上级信息", "operationId": "getSupervisorInfoUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«UserSupervisorInfoVo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/wallet/collection/transfer": {"post": {"tags": ["钱包接口"], "summary": "划转给好友", "operationId": "transferToFriendUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "transferToFriendReqVo", "description": "transferToFriendReqVo", "required": true, "schema": {"$ref": "#/definitions/TransferToFriendReqVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/wallet/collection/transfer/username": {"get": {"tags": ["钱包接口"], "summary": "划转给好友时查询用户名", "operationId": "getUsernameByEmailUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "email", "description": "邮箱地址", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«string»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/wallet/collection/withdraw": {"post": {"tags": ["钱包接口"], "summary": "申请提现", "operationId": "createSettOrderUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "settOrderReqVo", "description": "settOrderReqVo", "required": true, "schema": {"$ref": "#/definitions/SettOrderReqVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«Void»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/wallet/transfer": {"post": {"tags": ["钱包接口"], "summary": "资金划转", "operationId": "transferUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"in": "body", "name": "walletTransferReqVo", "description": "walletTransferReqVo", "required": true, "schema": {"$ref": "#/definitions/钱包划转模型"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«归集资金返回模型»"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/wallet/transfer/preview": {"get": {"tags": ["钱包接口"], "summary": "交易钱包划转预览", "operationId": "previewTransferUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "amount", "in": "query", "description": "amount", "required": true, "type": "number"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«TransferPreviewDTO»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/wallet/{token}/address": {"get": {"tags": ["钱包接口"], "summary": "获取入金地址", "operationId": "getWalletAddressUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "token", "in": "path", "description": "网络线路", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/R«钱包地址信息»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/wallet/{type}/history": {"get": {"tags": ["钱包接口"], "summary": "获取钱包明细", "operationId": "listHistoryUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "令牌", "required": false, "type": "string"}, {"name": "pageNum", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "type", "in": "path", "description": "钱包类型,deposit:入金钱包,profit:盈利钱包,community:社区钱包,collection:归集钱包", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Pager«钱包明细»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}}, "definitions": {"AccountBalanceVo": {"type": "object", "properties": {"balance": {"type": "number"}}, "title": "AccountBalanceVo"}, "AnnouncementHaveReadVo": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "AnnouncementHaveReadVo"}, "AnnouncementVo": {"type": "object", "properties": {"content": {"type": "string"}, "endTime": {"type": "string"}, "haveRead": {"type": "boolean"}, "id": {"type": "integer", "format": "int64"}, "startTime": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "integer", "format": "int64"}}, "title": "AnnouncementVo"}, "ArticleDetailBO": {"type": "object", "properties": {"content": {"type": "string"}, "coverUrl": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int32"}, "imgUrl": {"type": "string"}, "modifiedTime": {"type": "string", "format": "date-time"}, "remark": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "title": {"type": "string"}, "type": {"type": "integer", "format": "int32"}}, "title": "ArticleDetailBO"}, "ArticleVo": {"type": "object", "properties": {"coverUrl": {"type": "string"}, "createTime": {"type": "string", "format": "date-time"}, "id": {"type": "integer", "format": "int32"}, "imgUrl": {"type": "string"}, "modifiedTime": {"type": "string", "format": "date-time"}, "remark": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "title": {"type": "string"}, "type": {"type": "integer", "format": "int32"}}, "title": "ArticleVo"}, "AuthStatusVo": {"type": "object", "properties": {"googleToken": {"type": "integer", "format": "int32"}, "identity": {"type": "integer", "format": "int32"}, "mobileNo": {"type": "integer", "format": "int32"}, "wallet": {"type": "integer", "format": "int32"}}, "title": "AuthStatusVo"}, "BalanceInfo": {"type": "object", "properties": {"availableBalance": {"type": "number"}, "lockedBalance": {"type": "number"}, "totalAmount": {"type": "number"}}, "title": "BalanceInfo"}, "CaptchaVo": {"type": "object", "properties": {"captcha": {"type": "string"}, "k": {"type": "string"}}, "title": "CaptchaVo"}, "CarouselImageVO": {"type": "object", "properties": {"imgUrl": {"type": "string"}, "linkUrl": {"type": "string"}, "sort": {"type": "integer", "format": "int32"}}, "title": "CarouselImageVO"}, "ChangePasswordReqVo": {"type": "object", "properties": {"code": {"type": "string", "description": "谷歌验证码"}, "confirmPassword": {"type": "string", "description": "确认密码"}, "nonce": {"type": "string", "description": "邮箱验证码随机数"}, "password": {"type": "string", "description": "支付密码"}}, "title": "ChangePasswordReqVo"}, "ChangePayPasswordReqVo": {"type": "object", "properties": {"code": {"type": "string", "description": "谷歌验证码"}, "confirmPayPassword": {"type": "string", "description": "确认密码"}, "nonce": {"type": "string", "description": "邮箱验证码随机数"}, "payPassword": {"type": "string", "description": "支付密码"}}, "title": "ChangePayPasswordReqVo"}, "ComponentStockVO": {"type": "object", "properties": {"kline": {"type": "array", "description": "7天K线图", "items": {"type": "number"}}, "latestPrice": {"type": "number", "description": "最新价格"}, "priceChange": {"type": "number", "description": "价格变化金额"}, "priceChangePercent": {"type": "number", "description": "价格变化百分比"}, "symbol": {"type": "string", "description": "股票代码"}}, "title": "ComponentStockVO"}, "CustomerServiceChannelVO": {"type": "object", "properties": {"content": {"type": "string"}, "contentType": {"type": "string"}, "icon": {"type": "string"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string"}, "type": {"type": "string"}}, "title": "CustomerServiceChannelVO"}, "EmailCaptchaCheckReqVo": {"type": "object", "properties": {"code": {"type": "string"}, "email": {"type": "string"}, "nonce": {"type": "string"}}, "title": "EmailCaptchaCheckReqVo"}, "ExpendAddressVo": {"type": "object", "properties": {"address": {"type": "string", "description": "钱包地址"}, "id": {"type": "integer", "format": "int64"}, "name": {"type": "string", "description": "地址名称"}, "type": {"type": "string"}}, "title": "ExpendAddressVo"}, "FriendListVo": {"type": "object", "properties": {"level": {"type": "integer", "format": "int32"}, "orderAmount": {"type": "integer", "format": "int64"}, "username": {"type": "string"}}, "title": "FriendListVo"}, "GainDistributionVO": {"type": "object", "properties": {"list": {"type": "array", "description": "涨跌分布", "items": {"type": "integer", "format": "int32"}}, "low": {"type": "integer", "format": "int32", "description": "跌"}, "up": {"type": "integer", "format": "int32", "description": "涨"}, "zero": {"type": "integer", "format": "int32", "description": "平"}}, "title": "GainDistributionVO"}, "GeoVo": {"type": "object", "properties": {"city": {"type": "string"}, "country": {"type": "string"}, "ip": {"type": "string"}, "isoCode": {"type": "string"}, "latitude": {"type": "number", "format": "double"}, "longitude": {"type": "number", "format": "double"}, "network": {"type": "string"}, "subdivision": {"type": "string"}}, "title": "GeoVo"}, "JSONObject": {"type": "object", "title": "JSONObject", "additionalProperties": {"type": "object"}}, "Kline": {"type": "object", "properties": {"close": {"type": "number"}, "high": {"type": "number"}, "low": {"type": "number"}, "open": {"type": "number"}, "price": {"type": "number"}, "time": {"type": "integer", "format": "int64"}, "volume": {"type": "number"}}, "title": "<PERSON><PERSON>"}, "PageResult«ArticleVo»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/ArticleVo"}}, "pageNum": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageResult«ArticleVo»"}, "PageResult«FriendListVo»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/FriendListVo"}}, "pageNum": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageResult«FriendListVo»"}, "PageResult«JSONObject»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/JSONObject"}}, "pageNum": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageResult«JSONObject»"}, "PageResult«TransactionRecordVo»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/TransactionRecordVo"}}, "pageNum": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageResult«TransactionRecordVo»"}, "PageResult«交易记录表查询模型»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/交易记录表查询模型"}}, "pageNum": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageResult«交易记录表查询模型»"}, "PageResult«产品订单模型»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/产品订单模型"}}, "pageNum": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageResult«产品订单模型»"}, "PageResult«导师信息查询模型»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/导师信息查询模型"}}, "pageNum": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageResult«导师信息查询模型»"}, "PageResult«订单列表查询»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/订单列表查询"}}, "pageNum": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageResult«订单列表查询»"}, "PageResult«钱包明细»": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/钱包明细"}}, "pageNum": {"type": "integer", "format": "int64"}, "pageSize": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}}, "title": "PageResult«钱包明细»"}, "Pager«ArticleVo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/PageResult«ArticleVo»"}, "msg": {"type": "string"}}, "title": "Pager«ArticleVo»"}, "Pager«FriendListVo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/PageResult«FriendListVo»"}, "msg": {"type": "string"}}, "title": "Pager«FriendListVo»"}, "Pager«JSONObject»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/PageResult«JSONObject»"}, "msg": {"type": "string"}}, "title": "Pager«JSONObject»"}, "Pager«TransactionRecordVo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/PageResult«TransactionRecordVo»"}, "msg": {"type": "string"}}, "title": "Pager«TransactionRecordVo»"}, "Pager«产品订单模型»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/PageResult«产品订单模型»"}, "msg": {"type": "string"}}, "title": "Pager«产品订单模型»"}, "Pager«导师信息查询模型»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/PageResult«导师信息查询模型»"}, "msg": {"type": "string"}}, "title": "Pager«导师信息查询模型»"}, "Pager«订单列表查询»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/PageResult«订单列表查询»"}, "msg": {"type": "string"}}, "title": "Pager«订单列表查询»"}, "Pager«钱包明细»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/PageResult«钱包明细»"}, "msg": {"type": "string"}}, "title": "Pager«钱包明细»"}, "ProductOrderUnbindingReqVo": {"type": "object", "properties": {"googleCode": {"type": "string", "description": "谷歌验证码"}, "payPassword": {"type": "string", "description": "支付密码"}, "productId": {"type": "integer", "format": "int64", "description": "产品ID"}, "status": {"type": "integer", "format": "int32", "description": "绑定状态，1：取消解绑，0：解绑"}}, "title": "ProductOrderUnbindingReqVo"}, "RestGoogleKeyReqVo": {"type": "object", "properties": {"emailCaptcha": {"type": "string", "description": "邮箱验证码"}, "nonce": {"type": "string", "description": "邮箱验证码随机数"}}, "title": "RestGoogleKeyReqVo"}, "R«AccountBalanceVo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/AccountBalanceVo"}, "msg": {"type": "string"}}, "title": "R«AccountBalanceVo»"}, "R«ArticleDetailBO»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/ArticleDetailBO"}, "msg": {"type": "string"}}, "title": "R«ArticleDetailBO»"}, "R«AuthStatusVo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/AuthStatusVo"}, "msg": {"type": "string"}}, "title": "R«AuthStatusVo»"}, "R«CaptchaVo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/CaptchaVo"}, "msg": {"type": "string"}}, "title": "R«CaptchaVo»"}, "R«GainDistributionVO»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/GainDistributionVO"}, "msg": {"type": "string"}}, "title": "R«GainDistributionVO»"}, "R«GeoVo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/GeoVo"}, "msg": {"type": "string"}}, "title": "R«GeoVo»"}, "R«JSONObject»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object", "additionalProperties": {"type": "object"}}, "msg": {"type": "string"}}, "title": "R«JSONObject»"}, "R«List«AnnouncementVo»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/AnnouncementVo"}}, "msg": {"type": "string"}}, "title": "R«List«AnnouncementVo»»"}, "R«List«CarouselImageVO»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/CarouselImageVO"}}, "msg": {"type": "string"}}, "title": "R«List«CarouselImageVO»»"}, "R«List«ComponentStockVO»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ComponentStockVO"}}, "msg": {"type": "string"}}, "title": "R«List«ComponentStockVO»»"}, "R«List«CustomerServiceChannelVO»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/CustomerServiceChannelVO"}}, "msg": {"type": "string"}}, "title": "R«List«CustomerServiceChannelVO»»"}, "R«List«ExpendAddressVo»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ExpendAddressVo"}}, "msg": {"type": "string"}}, "title": "R«List«ExpendAddressVo»»"}, "R«List«产品订单模型»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/产品订单模型"}}, "msg": {"type": "string"}}, "title": "R«List«产品订单模型»»"}, "R«List«合约产品数据»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/合约产品数据"}}, "msg": {"type": "string"}}, "title": "R«List«合约产品数据»»"}, "R«SettAmountResVo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/SettAmountResVo"}, "msg": {"type": "string"}}, "title": "R«SettAmountResVo»"}, "R«StockKline»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/StockKline"}, "msg": {"type": "string"}}, "title": "R«StockKline»"}, "R«StockTransactionRecordPageVO»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/StockTransactionRecordPageVO"}, "msg": {"type": "string"}}, "title": "R«StockTransactionRecordPageVO»"}, "R«TcUserSigVo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/TcUserSigVo"}, "msg": {"type": "string"}}, "title": "R«TcUserSigVo»"}, "R«TransferPreviewDTO»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/TransferPreviewDTO"}, "msg": {"type": "string"}}, "title": "R«TransferPreviewDTO»"}, "R«UploadResult»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/UploadResult"}, "msg": {"type": "string"}}, "title": "R«UploadResult»"}, "R«UserInfoVo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/UserInfoVo"}, "msg": {"type": "string"}}, "title": "R«UserInfoVo»"}, "R«UserProfileInfo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/UserProfileInfo"}, "msg": {"type": "string"}}, "title": "R«UserProfileInfo»"}, "R«UserSupervisorInfoVo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/UserSupervisorInfoVo"}, "msg": {"type": "string"}}, "title": "R«UserSupervisorInfoVo»"}, "R«Void»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "msg": {"type": "string"}}, "title": "R«Void»"}, "R«boolean»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "boolean"}, "msg": {"type": "string"}}, "title": "R«boolean»"}, "R«object»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "R«object»"}, "R«string»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "string"}, "msg": {"type": "string"}}, "title": "R«string»"}, "R«产品订单详情»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/产品订单详情"}, "msg": {"type": "string"}}, "title": "R«产品订单详情»"}, "R«全部钱包余额模型»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/全部钱包余额模型"}, "msg": {"type": "string"}}, "title": "R«全部钱包余额模型»"}, "R«归集资金返回模型»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/归集资金返回模型"}, "msg": {"type": "string"}}, "title": "R«归集资金返回模型»"}, "R«订单列表查询»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/订单列表查询"}, "msg": {"type": "string"}}, "title": "R«订单列表查询»"}, "R«谷歌验证返回信息»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/谷歌验证返回信息"}, "msg": {"type": "string"}}, "title": "R«谷歌验证返回信息»"}, "R«钱包地址信息»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/钱包地址信息"}, "msg": {"type": "string"}}, "title": "R«钱包地址信息»"}, "R«随机数模型»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/随机数模型"}, "msg": {"type": "string"}}, "title": "R«随机数模型»"}, "SettAmountReqVo": {"type": "object", "properties": {"amount": {"type": "number", "description": "提现金额"}}, "title": "SettAmountReqVo"}, "SettAmountResVo": {"type": "object", "properties": {"amount": {"type": "number", "description": "提现金额"}}, "title": "SettAmountResVo"}, "SettOrderReqVo": {"type": "object", "properties": {"addressId": {"type": "integer", "format": "int64", "description": "提现地址ID"}, "amount": {"type": "number", "description": "提现金额"}, "googleCode": {"type": "string", "description": "谷歌验证码"}, "payPassword": {"type": "string", "description": "支付密码"}}, "title": "SettOrderReqVo"}, "StockKline": {"type": "object", "properties": {"close": {"type": "number"}, "high": {"type": "number"}, "latestPrice": {"type": "number"}, "list": {"type": "array", "items": {"$ref": "#/definitions/Kline"}}, "low": {"type": "number"}, "open": {"type": "number"}, "symbol": {"type": "string"}}, "title": "StockKline"}, "StockTransactionRecordPageVO": {"type": "object", "properties": {"additionalCapital": {"type": "number"}, "initialCapital": {"type": "number"}, "result": {"$ref": "#/definitions/PageResult«交易记录表查询模型»"}, "totalEarnings": {"type": "number"}, "withdrawnEarnings": {"type": "number"}}, "title": "StockTransactionRecordPageVO"}, "TcUserSigVo": {"type": "object", "properties": {"imImage": {"type": "string"}, "nickName": {"type": "string"}, "sdkAppId": {"type": "string"}, "userId": {"type": "string"}, "userSig": {"type": "string"}}, "title": "TcUserSigVo"}, "TransactionRecordVo": {"type": "object", "properties": {"buyDateTime": {"type": "string", "format": "date-time"}, "buyPrice": {"type": "number"}, "contracts": {"type": "string"}, "fee": {"type": "number"}, "saleNumber": {"type": "number"}, "sellDateTime": {"type": "string", "format": "date-time"}, "sellPrice": {"type": "number"}, "totalNetProfit": {"type": "number"}}, "title": "TransactionRecordVo"}, "TransferPreviewDTO": {"type": "object", "properties": {"chargedAmount": {"type": "number"}, "expectedReceive": {"type": "number"}, "fee": {"type": "number"}, "freeAmount": {"type": "number"}}, "title": "TransferPreviewDTO"}, "TransferToFriendReqVo": {"type": "object", "properties": {"amount": {"type": "number", "description": "划转金额"}, "email": {"type": "string", "description": "用户邮箱"}, "googleCode": {"type": "string", "description": "谷歌验证码"}, "payPassword": {"type": "string", "description": "支付密码"}}, "title": "TransferToFriendReqVo"}, "UploadResult": {"type": "object", "properties": {"original": {"type": "string"}, "rePath": {"type": "string"}, "title": {"type": "string"}, "url": {"type": "string"}}, "title": "UploadResult"}, "UserEmailCaptchaCheckReqVo": {"type": "object", "properties": {"code": {"type": "string"}, "nonce": {"type": "string"}}, "title": "UserEmailCaptchaCheckReqVo"}, "UserInfoVo": {"type": "object", "properties": {"auth": {"type": "boolean"}, "email": {"type": "string"}, "level": {"type": "integer", "format": "int32"}, "username": {"type": "string"}}, "title": "UserInfoVo"}, "UserProfileInfo": {"type": "object", "properties": {"expendAddresses": {"type": "array", "items": {"$ref": "#/definitions/用户提现地址查询模型"}}, "invitationCode": {"type": "string"}, "userId": {"type": "integer", "format": "int64"}, "userLevel": {"type": "integer", "format": "int32"}}, "title": "UserProfileInfo"}, "UserSupervisorInfoVo": {"type": "object", "properties": {"email": {"type": "string"}, "username": {"type": "string"}}, "title": "UserSupervisorInfoVo"}, "交易记录表查询模型": {"type": "object", "properties": {"buyAmount": {"type": "number", "description": "购买金额"}, "buyDate": {"type": "string", "format": "date-time", "description": "买入日期"}, "buyPosition": {"type": "string", "description": "买入仓位"}, "buyPrice": {"type": "number", "description": "买入价格"}, "buyQuantity": {"type": "number", "description": "买入数量"}, "cancel": {"type": "boolean", "description": "是否可以撤回"}, "id": {"type": "integer", "format": "int64"}, "mentorCommission": {"type": "number", "description": "导师佣金"}, "mentorId": {"type": "integer", "format": "int64", "description": "导师ID"}, "mentorName": {"type": "string", "description": "导师姓名"}, "netProfit": {"type": "number", "description": "收益额"}, "orderEndTime": {"type": "string", "description": "订单到期时间"}, "orderNo": {"type": "string", "description": "订单号"}, "platformCommission": {"type": "number", "description": "平台佣金"}, "productId": {"type": "integer", "format": "int64", "description": "产品ID"}, "productName": {"type": "string"}, "sellAmount": {"type": "number", "description": "卖出金额"}, "sellDate": {"type": "string", "format": "date-time", "description": "卖出日期"}, "sellPrice": {"type": "number", "description": "卖出价格"}, "sellQuantity": {"type": "number", "description": "卖出数量"}, "status": {"type": "integer", "format": "int32", "description": "是否结算 0.开始中 1.结算"}, "totalProfit": {"type": "number", "description": "总收益额"}, "userId": {"type": "integer", "format": "int64", "description": "用户ID"}, "userName": {"type": "string"}}, "title": "交易记录表查询模型"}, "产品加仓信息": {"type": "object", "properties": {"accountType": {"type": "integer", "format": "int32", "description": "钱包标识，1：入金钱包，3:盈利钱包4：余额钱包"}, "amount": {"type": "number", "description": "购买的金额"}, "orderId": {"type": "integer", "format": "int64", "description": "订单ID"}, "password": {"type": "string", "description": "支付密码"}}, "title": "产品加仓信息"}, "产品支付信息": {"type": "object", "properties": {"a": {"type": "integer", "format": "int32", "description": "购买百分比：1:30%，2：50%，3：100%"}, "accountType": {"type": "integer", "format": "int32", "description": "钱包标识，1：入金钱包，3：归集钱包"}, "password": {"type": "string", "description": "支付密码"}, "productId": {"type": "integer", "format": "int64", "description": "产品ID"}}, "title": "产品支付信息"}, "产品订单模型": {"type": "object", "properties": {"buyPrice": {"type": "number"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "id": {"type": "integer", "format": "int64", "description": "主键id"}, "orderAmount": {"type": "number", "description": "订单金额"}, "orderNo": {"type": "string", "description": "订单编码"}, "sellPrice": {"type": "number"}, "status": {"type": "integer", "format": "int32"}, "stockName": {"type": "string"}, "totalProfit": {"type": "number", "description": "以往收益"}}, "title": "产品订单模型"}, "产品订单详情": {"type": "object", "properties": {"additionalAmount": {"type": "number", "description": "追加金额"}, "dailyAlreadyWithdrawAmount": {"type": "number", "description": "当日已提现的金额"}, "dailyCanWithdrawAmount": {"type": "number", "description": "当日剩余可提现的金额"}, "infoList": {"type": "array", "description": "买入卖出数据", "items": {"$ref": "#/definitions/订单列表查询"}}, "netProfit": {"type": "number", "description": "总收益额"}, "orderAmount": {"type": "number", "description": "初始金额"}, "orderNo": {"type": "string", "description": "订单号"}, "rejectAmount": {"type": "number", "description": "拒绝的追加金额"}, "totalAmount": {"type": "number", "description": "总投入金额"}}, "title": "产品订单详情"}, "全部钱包余额模型": {"type": "object", "properties": {"cash": {"type": "number"}, "community": {"$ref": "#/definitions/BalanceInfo"}}, "title": "全部钱包余额模型"}, "发送验证码模型": {"type": "object", "properties": {"account": {"type": "string", "description": "用户名"}, "captcha": {"type": "string", "description": "验证码"}, "k": {"type": "string", "description": "验证码随机数"}}, "title": "发送验证码模型"}, "合约产品数据": {"type": "object", "properties": {"commissionRate": {"type": "number"}, "cycle": {"type": "integer", "format": "int32"}, "id": {"type": "integer", "format": "int64"}, "maxAmount": {"type": "number"}, "mentor": {"type": "string"}, "minAmount": {"type": "number"}, "name": {"type": "string", "description": "产品名称"}, "singleAmount": {"type": "number", "description": "单笔金额"}, "sort": {"type": "integer", "format": "int32"}, "type": {"type": "integer", "format": "int32", "description": "产品类型"}}, "title": "合约产品数据"}, "导师信息查询模型": {"type": "object", "properties": {"avatar": {"type": "string", "description": "导师头像的 URL 或路径"}, "bio": {"type": "string", "description": "导师的简介"}, "company": {"type": "string", "description": "导师所属的公司名称"}, "id": {"type": "integer", "format": "int64"}, "maxDrawdown": {"type": "number", "description": "最大回撤（百分比，例如：-20.00）"}, "mentorAllow": {"type": "integer", "format": "int32", "description": "是否允许多个跟买"}, "monthlyProfit": {"type": "number", "description": "每月收益（单位：货币）"}, "name": {"type": "string", "description": "导师的姓名"}, "nickname": {"type": "string", "description": "导师的外号"}, "portfolio": {"type": "string", "description": "仓位信息（描述或链接）"}, "position": {"type": "string", "description": "导师的岗位或职位"}, "productAllowState": {"type": "integer", "format": "int32", "description": "交易产品限制"}, "status": {"type": "integer", "format": "int32", "description": "导师状态"}, "winRate": {"type": "number", "description": "胜率（百分比，例如：75.00）"}, "yearsOfExperience": {"type": "integer", "format": "int32", "description": "从业时间（单位：年）"}}, "title": "导师信息查询模型"}, "归集资金返回模型": {"type": "object", "properties": {"balance": {"type": "number", "description": "钱包余额"}}, "title": "归集资金返回模型"}, "手机号模型": {"type": "object", "properties": {"mobileNo": {"type": "string", "description": "手机号"}}, "title": "手机号模型"}, "提盈": {"type": "object", "properties": {"orderNo": {"type": "string", "description": "订单号"}, "payPassword": {"type": "string", "description": "支付密码"}, "profitAmount": {"type": "number", "description": "提盈金额"}}, "title": "提盈"}, "支付密码": {"type": "object", "properties": {"confirmPassword": {"type": "string", "description": "确认支付密码"}, "password": {"type": "string", "description": "支付密码"}}, "title": "支付密码"}, "用户提现地址修改模型": {"type": "object", "properties": {"address": {"type": "string", "description": "钱包地址"}, "googleCode": {"type": "string", "description": "谷歌验证码"}, "name": {"type": "string", "description": "地址名称"}, "type": {"type": "string", "description": "地址类型，ETHER：以太坊，TRON：波场"}}, "title": "用户提现地址修改模型"}, "用户提现地址查询模型": {"type": "object", "properties": {"address": {"type": "string", "description": "钱包地址"}, "name": {"type": "string", "description": "地址名称"}, "type": {"type": "string"}}, "title": "用户提现地址查询模型"}, "用户注册模型1": {"type": "object", "properties": {"invitationCode": {"type": "string", "description": "邀请码"}, "nonce": {"type": "string", "description": "邮箱验证码随机数"}, "otp": {"type": "string", "description": "邮箱验证码"}, "password": {"type": "string", "description": "登录密码"}, "username": {"type": "string", "description": "用户名"}}, "title": "用户注册模型1"}, "登录信息": {"type": "object", "properties": {"captcha": {"type": "string", "description": "验证码"}, "k": {"type": "string"}, "password": {"type": "string", "description": "密码"}, "username": {"type": "string", "description": "用户名"}}, "title": "登录信息"}, "订单列表查询": {"type": "object", "properties": {"additionalMount": {"type": "number", "description": "追加金额"}, "applyCreateTime": {"type": "string", "description": "申请时间"}, "auditContent": {"type": "string", "description": "审核内容"}, "buyDate": {"type": "string", "format": "date-time", "description": "买入日期"}, "buyPosition": {"type": "string", "description": "买入仓位"}, "buyPrice": {"type": "number", "description": "买入价格"}, "buyQuantity": {"type": "number", "description": "总买数量"}, "commissionRate": {"type": "number", "description": "导师佣金"}, "cycle": {"type": "integer", "format": "int32", "description": "周期天数"}, "dailyCanWithdrawAmount": {"type": "number", "description": "每日可提盈金额"}, "expireTime": {"type": "string", "description": "过期时间"}, "id": {"type": "integer", "format": "int64", "description": "id"}, "maxAmount": {"type": "number", "description": "最大金额"}, "mentorCommission": {"type": "number", "description": "导师佣金"}, "mentorId": {"type": "integer", "format": "int64", "description": "导师id"}, "mentorName": {"type": "string", "description": "导师"}, "minAmount": {"type": "number", "description": "最小金额"}, "netProfit": {"type": "number", "description": "纯利润"}, "orderNo": {"type": "string", "description": "订单号"}, "platformCommission": {"type": "number", "description": "平台佣金"}, "processStatus": {"type": "integer", "format": "int32", "description": "流程状态"}, "productId": {"type": "integer", "format": "int64", "description": "产品id"}, "productName": {"type": "string", "description": "产品名称"}, "sellDate": {"type": "string", "format": "date-time", "description": "卖出日期"}, "sellPrice": {"type": "number", "description": "卖出价格"}, "sellQuantity": {"type": "number", "description": "卖出数量"}, "singleAmount": {"type": "number", "description": "倍数金额"}, "startTime": {"type": "string", "description": "开始时间"}, "stockCode": {"type": "string", "description": "股票代码"}, "stockName": {"type": "string", "description": "股票名称"}, "totalAmount": {"type": "number", "description": "总金额"}, "totalProfit": {"type": "number", "description": "总收益额"}}, "title": "订单列表查询"}, "谷歌验证模型": {"type": "object", "properties": {"code": {"type": "string", "description": "谷歌验证码"}}, "title": "谷歌验证模型"}, "谷歌验证返回信息": {"type": "object", "properties": {"nonce": {"type": "string", "description": "随机字符串"}, "qrcode": {"type": "string", "description": "Base64编码的二维码"}, "token": {"type": "string", "description": "谷歌秘钥"}}, "title": "谷歌验证返回信息"}, "身份认证模型": {"type": "object", "properties": {"back": {"type": "string", "description": "身份证反面图片"}, "front": {"type": "string", "description": "身份证正面图片"}, "idCardNo": {"type": "string", "description": "身份证号"}}, "title": "身份认证模型"}, "钱包划转模型": {"type": "object", "properties": {"amount": {"type": "number", "description": "划转金额"}, "from": {"type": "string"}, "to": {"type": "string"}}, "title": "钱包划转模型"}, "钱包地址信息": {"type": "object", "properties": {"address": {"type": "string", "description": "钱包地址"}, "qrcode": {"type": "string", "description": "二维码信息"}, "timestamp": {"type": "integer", "format": "int64", "description": "生成地址的时间戳"}, "token": {"type": "string", "description": "代币标准"}}, "title": "钱包地址信息"}, "钱包明细": {"type": "object", "properties": {"amount": {"type": "number", "description": "账变金额"}, "balance": {"type": "number", "description": "账变后余额"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "fundDirection": {"type": "string", "description": "账变类型"}, "title": {"type": "string", "description": "名称"}}, "title": "钱包明细"}, "随机数模型": {"type": "object", "properties": {"nonce": {"type": "string", "description": "随机数，用于后续接口调用"}}, "title": "随机数模型"}}}